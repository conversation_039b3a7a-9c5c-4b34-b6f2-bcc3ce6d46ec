/**
 * Base Styles
 *
 * This file contains foundational styles:
 * - CSS imports
 * - CSS variables (custom properties)
 * - Reset styles
 * - Typography
 * - Body styles
 */

/* Font imports */
@import url('https://fonts.googleapis.com/css?family=Lato:300,400|Poppins:300,400,800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200..800&display=swap');

/* CSS Variables */
:root {
  /* Color Palette */
  --color-primary: hsl(184, 100%, 40%);
  --color-primary-light: hsl(184, 100%, 45%);
  --color-primary-lighter: hsl(184, 100%, 50%);
  --color-primary-dark: hsl(184, 100%, 35%);
  --color-primary-darker: hsl(184, 100%, 30%);

  --color-background: linear-gradient(to top left, #4fd1c5, #e6fffa);
  --color-card-bg: #eee;

  --color-text-dark: hsl(0, 0%, 20%);
  --color-text-medium: hsl(0, 0%, 40%);
  --color-text-light: hsl(0, 0%, 60%);

  --color-white: #fff;
  --color-black: #000;
  --color-grey-light: #aaa;
  --color-grey: #666;
  --color-grey-dark: #444;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  /* Typography */
  --font-primary: 'Poppins', sans-serif;
  --font-secondary: 'Lato', sans-serif;
  --font-tertiary: 'Manrope', sans-serif;

  /* Animation Timings */
  --transition-fast: 0.2s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.5s ease;
  --transition-elastic: 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.25);
  --transition-expand: 0.3s cubic-bezier(0.6, -0.28, 0.735, 0.045);

  /* Shadows */
  --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 20px rgba(0, 0, 0, 0.15);

  /* Text Shadows */
  --text-shadow-dark: 1px 1.01px 1.01px #555;
  --text-shadow-medium: 1px 1.01px 1.01px #777;
  --text-shadow-light: 1px 1.01px 1.01px #aaa;

  /* Background Clip */
  --bg-clip-text: text;

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-round: 50%;
}

/* ======================================================
BODY STYLES
====================================================== */
body {
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
  background: var(--color-background);
  height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* Reset styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Typography base styles */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-primary);
  font-weight: 800;
  color: var(--color-text-dark);
}

p {
  font-family: var(--font-secondary);
  color: var(--color-text-medium);
  line-height: 1.6;
}

a {
  text-decoration: none;
  color: var(--color-primary);
  transition: color var(--transition-medium);
}

a:hover {
  color: var(--color-primary-dark);
}

button {
  cursor: pointer;
  border: none;
  background: none;
  font-family: var(--font-secondary);
}

/* Utility classes */
.inactive {
  pointer-events: none;
  opacity: 0;
  transition: opacity var(--transition-medium);
}
