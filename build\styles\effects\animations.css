/**
 * Animation Styles
 *
 * This file contains keyframe animations and transition effects:
 * - Card intro animation
 * - Text reveal animations
 * - Page transition animations
 * - Button hover animations
 * - Icon animations
 * - Shimmer effect animations
 *
 * Performance notes:
 * - All animations use hardware-accelerated properties (transform, opacity) for better performance
 * - Elements using these animations should have will-change hints applied
 * - Animations are optimized to minimize repaints and reflows
 */

/* ======================================================
CARD ANIMATIONS
====================================================== */

/* Card intro animation */
@keyframes cardintro {
  0% {
    opacity: 1;
    transform: translateX(-100%) scale(0) rotateX(-45deg) rotateY(-180deg);
    top: 0px;
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) scale(1) rotateX(0deg) rotateY(0deg);
    top: 210px;
  }
}

/* ======================================================
TEXT ANIMATIONS
====================================================== */

/* Heading intro animation */
@keyframes h1-intro {
  0% {
    opacity: 0;
    transform: translateX(-0%) rotateX(45deg);
  }
  100% {
    opacity: 1;
    transform: translateX(-0%) rotateX(0deg);
  }
}

/* Subtitle intro animation */
@keyframes subtitle-intro {
  0% {
    left: 4%;
    opacity: 0;
    transform: translateX(-0%) rotateX(45deg);
  }
  100% {
    left: 0%;
    opacity: 1;
    transform: translateX(-0%) rotateX(0deg);
  }
}

/* Name intro animation */
@keyframes name-intro {
  0% {
    text-shadow: 1px 1.01px 1.01px #0fe5f5;
    left: 4%;
    opacity: 0;
  }
  100% {
    text-shadow: 1px 1.01px 1.01px #555;
    left: 0%;
    opacity: 1;
  }
}

/* Contact info intro animation */
@keyframes contact-intro {
  0% {
    left: -6%;
    opacity: 0;
    transform: translateY(-0%);
  }
  100% {
    left: 0%;
    opacity: 1;
    transform: translateY(-0%);
  }
}

/* Icon rotation animation */
@keyframes icon-rotate {
  0% {
    transform: scale(1.2) rotate(0deg);
  }
  25% {
    transform: scale(1.2) rotate(10deg);
  }
  50% {
    transform: scale(1.2) rotate(0deg);
  }
  75% {
    transform: scale(1.2) rotate(-10deg);
  }
  100% {
    transform: scale(1.2) rotate(0deg);
  }
}

/* ======================================================
BUTTON ANIMATIONS
====================================================== */

/* Book button hover animation */
@keyframes buttonhover {
  0% {
    transform: translate(-50%, -3%) scale(1); /* Normal size */
  }
  50% {
    transform: translate(-50%, -4%) scale(1.01); /* Slightly larger */
  }
  100% {
    transform: translate(-50%, -3%) scale(1); /* Back to normal */
  }
}

/* Book button text reveal animation */
@keyframes appt-text-reveal {
  0% {
    opacity: 0; /* Start invisible */
    right: 355px; /* Start position far to the right */
    filter: blur(6px); /* Initially blurred */
  }
  10% {
    opacity: 1; /* Fade in quickly */
  }
  100% {
    opacity: 1;
    right: 0px; /* Final position */
    filter: blur(0px); /* Clear and sharp */
  }
}

/* ======================================================
EFFECT ANIMATIONS
====================================================== */

/* Shimmer effect animation */
@keyframes shimmer {
  0% {
    filter: opacity(0.7) contrast(1.5) brightness(1.5) hue-rotate(0deg);
  }
  50% {
    filter: opacity(1) contrast(1.8) brightness(1.8) hue-rotate(30deg);
  }
  100% {
    filter: opacity(0.7) contrast(1.5) brightness(1.5) hue-rotate(0deg);
  }
}

/* ======================================================
PAGE TRANSITION ANIMATIONS
====================================================== */

/* Fade in animation */
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Fade out animation */
@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

/* Slide in from right animation */
@keyframes slideInRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Slide out to left animation */
@keyframes slideOutLeft {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-100%);
    opacity: 0;
  }
}

/* About Me page animations */
@keyframes about-me-fadeIn {
  0% {
    opacity: 0;
    transform: translateY(-200px);
  }
  100% {
    pointer-events: auto; /* Enable pointer events when fully visible for back button */
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes about-me-fadeOut {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-20px);
  }
}

/* Services page animations */
@keyframes services-fadeIn {
  0% {
    opacity: 0;
    transform: translateY(-200px) scale(0.95);
  }
  70% {
    opacity: 1;
    transform: translateY(10px) scale(1);
  }
  100% {
    pointer-events: auto; /* Enable pointer events when fully visible for back button */
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes services-fadeOut {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-20px) scale(0.98);
  }
}

/* Shine animation for buttons */
@keyframes shine {
  0% {
    transform: rotate(45deg) translateY(-50%) translateX(-100%);
  }
  100% {
    transform: rotate(45deg) translateY(-50%) translateX(100%);
  }
}

/* About Me Container mobile intro animation */
@keyframes about-me-container-intro {
  0% {
    filter: blur(4px);
    opacity: 0;
    transform: translateY(-30%) rotateX(45deg);
  }
  100% {
    filter: blur(0px);
    opacity: 1;
    transform: translateY(-0%) rotateX(0deg);
  }
}