@import '../base.css';

/**
 * Card Component Styles
 *
 * This file contains styles for the main business card component:
 * - Card container and structure
 * - Card content layout
 * - Card header and text styles
 * - Contact information styles
 * - Book button styles
 */

/* ======================================================
CARD STRUCTURE
====================================================== */

.card-container {
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  position: relative;
  transform: translateX(0) scale(1) rotateX(0deg) rotateY(0deg);
  perspective: 900px;
  pointer-events: none;
  opacity: 1;
  /*animation: 0.9s ease-in 0s 1 both cardintro;*/
  will-change: transform, opacity;
}

.card {
  width: 100%;
  max-width: 500px;
  height: 310px;
  position: relative;
  background: #bbb;
  padding: 0; /* We'll handle inner spacing via content layers */
  transform-style: preserve-3d;
  transition:
    height var(--transition-slow) cubic-bezier(0.25, 0.1, 0.25, 1),
    transform 0.05s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow var(--transition-medium);
  pointer-events: auto;
  will-change: transform, height, box-shadow;
}

/* MAIN PAGE SIZE */
.card:hover {
  height: 350px; /* Increased height to fit booking button */
  box-shadow: 0 0 25px 8px rgba(255, 255, 255, 0.7);
}

.card-inner {
  position: absolute;
  inset: 1px;
  box-shadow: 0 35px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden; /* Clip shine effects to card boundaries */
}

.card-border {
  position: absolute;
  inset: 0;
  overflow: hidden;
  z-index: 0;
}

.card-background {
  position: absolute;
  inset: 0;
  background-color: var(--color-card-bg);
}

.card-content {
  position: absolute;
  inset: 16px;
  background-color: var(--color-primary-lighter);
  padding: 1.25rem; /* inner breathing room */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  overflow: hidden;
  border: 1px solid var(--color-grey-light);
}

.card-content::before {
  content: '';
  position: absolute;
  inset: 4px;
  background: var(--color-card-bg);
  z-index: 1;
  box-sizing: border-box;
  border: 1px solid var(--color-grey-light);
}

.card-header {
  position: relative;
  z-index: 2;
  padding-bottom: 0.5rem;
}

/* ======================================================
CARD TEXT STYLES
====================================================== */

.card-header h1 {
  font-size: 1.75rem;
  font-weight: bold;
  color: var(--color-black);
  font-family: var(--font-primary);
  margin: 0;
  letter-spacing: -0.045em;
  text-shadow: -1px -1px rgba(0,0,0,0.4), 1px 1px rgba(255,255,255,1);
  font-size: 32px;
  background-color: var(--color-grey);
  text-shadow: var(--text-shadow-dark);
  -webkit-background-clip: var(--bg-clip-text);
  -moz-background-clip: var(--bg-clip-text);
  background-clip: var(--bg-clip-text);
  animation-name: h1-intro;
  animation-duration: 0.9s;
  animation-delay: 0s;
  animation-timing-function: var(--transition-elastic);
  animation-fill-mode: both;
}

.subtitle {
  font-size: 1.125rem;
  font-family: serif;
  text-shadow: -1px -1px rgba(0,0,0,0.4), 1px 1px rgba(255,255,255,1);
  color: rgba(0,200,200,0.4);
  background-color: var(--color-grey);
  text-shadow: var(--text-shadow-dark);
  -webkit-background-clip: var(--bg-clip-text);
  -moz-background-clip: var(--bg-clip-text);
  background-clip: var(--bg-clip-text);
  font-weight: 400;
  font-family: var(--font-secondary);
  font-size: 12px;
  letter-spacing: 1.3px;
  margin-top: -4px;
  color: var(--color-primary-dark);
  animation-name: subtitle-intro;
  animation-duration: 1.3s;
  animation-delay: 0.3s;
  animation-timing-function: var(--transition-elastic);
  animation-fill-mode: both;
  position: relative;
}

.name {
  font-size: 1.1rem;
  color: #4a5568;
  margin-top: 1.125rem;
  font-family: serif;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #eee;
  background-color: var(--color-grey);
  text-shadow: var(--text-shadow-dark);
  -webkit-background-clip: var(--bg-clip-text);
  -moz-background-clip: var(--bg-clip-text);
  background-clip: var(--bg-clip-text);
  transition: all var(--transition-medium);
  animation-name: name-intro;
  animation-duration: 1.3s;
  animation-delay: 0.9s;
  animation-timing-function: var(--transition-elastic);
  animation-fill-mode: both;
  position: relative;
}

.card-content:hover .name {
  color: var(--color-grey-dark);
  background-color: var(--color-grey);
  text-shadow: var(--text-shadow-medium);
  color: transparent;
}

/* ======================================================
ABOUT ME BUTTON STYLES, SERVICES BUTTON & MA NUMBER
====================================================== */

.about-me-container {
  /* Layout */
  display: flex;
  justify-content: left;
  align-items: center;
  gap: 10px;
  color: var(--color-text-medium);
  background-color: var(--color-grey);
  /* Font settings */
  font-weight: 400;
  font-family: var(--font-secondary);
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.2em;
  text-shadow: var(--text-shadow-medium);
  -webkit-background-clip: var(--bg-clip-text);
  -moz-background-clip: var(--bg-clip-text);
  background-clip: var(--bg-clip-text);
  /* Spacing */
  margin-top: -6px;
  position: relative;
  /* Animations */
  transition: all var(--transition-expand);
  opacity: 0;
  transform: translateY(-30%) rotateX(45deg);
  filter: blur(4px);
  will-change: transform, opacity, filter;
}

/* Drop down Hover Animations */
.card:hover .about-me-container {
  animation: about-me-container-intro 1s var(--transition-elastic) forwards;
}

/* Mobile Animation */
@media (max-width: 768px) {
  .about-me-container {
    animation: about-me-container-intro 1.3s var(--transition-elastic) forwards;
    animation-delay: 1.2s; /* Delay to start after name animation */
  }
}

/* About Me Button*/
.about-me { /* Button Container */
  margin-right: 0%;
}
.about-me span { /* Hidden Text */
  display: none;
}
.about-me:after { /* Current Text */
  content: '✾ About Me';
}
.about-me:hover::after { /* Hover Text */
  color: var(--color-primary);
  content: '✿ About Me';
}
.about-me:active::after { /* Active Text */
  color: var(--color-primary);
  content: '❉ About Me';
}

/* Services Button */
.services { /* Button Container */
  margin-left: 0%;
}
.services span { /* Hidden Text */
  display: none;
}
.services:after { /* Current Text */
  content: '✾ Services';
}
.services:hover::after { /* Hover Text */
  color: var(--color-primary);
  content: '✿ Services';
}
.services:active::after { /* Active Text */
  color: var(--color-primary);
  content: '❉ Services';
}

/* MA Number */
.MA-number { /* MA Number */
  margin-left: -0%;
}
.MA-number span { /* Hidden Text */
  display: none;
}
.MA-number:after { /* Current Text */
  content: '✾ MA#83097';
}
.MA-number:hover::after { /* Hover Text */
  color: var(--color-primary);
  content: '✿ MA#83097';
}
.MA-number:active::after { /* Active Text */
  color: var(--color-primary);
  content: '❉ MA#83097';
}

/* ======================================================
CONTACT INFO STYLES
====================================================== */

.contact-info {
  margin-top: 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  position: absolute;
  top: 135px;
  z-index: 2;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: transform var(--transition-medium), box-shadow var(--transition-medium), filter var(--transition-medium);
  padding: 5px 60px 5px 5px; /* Extended left padding to create larger hover area */
  border-radius: var(--radius-sm);
  position: relative;
  animation-name: contact-intro;
  animation-duration: 1.3s;
  animation-delay: 1s;
  animation-timing-function: var(--transition-elastic);
  animation-fill-mode: both;
}

.cti-one {
  animation-delay: 1.7s;
}
.cti-two {
  animation-delay: 2.2s;
}
.cti-three {
  animation-delay: 2.7s;
}

/* When any contact item is hovered, blur all contact items */
.contact-info:hover .contact-item {
  filter: blur(2px) contrast(3) brightness(3) invert(40%) hue-rotate(-180deg);
  opacity: 0.2;
}

/* But keep the actually hovered item clear */
.contact-info .contact-item:hover {
  filter: blur(0) contrast(1) brightness(1);
  opacity: 1;
}

.contact-item:hover {
  transform: translateY(-3px);
}

.icon {
  height: 1.125rem;
  width: 1.125rem;
  color: var(--color-primary-darker);
  flex-shrink: 0;
  transition: transform var(--transition-medium), color var(--transition-medium);
}

.contact-item:hover .icon {
  transform: scale(1.2) rotate(0deg);
  color: var(--color-primary-lighter);
  animation: var(--transition-bounce) icon-rotate both;
}

/* Service area button styling */
.service-area-button {
  background: none;
  border: none;
  color: inherit;
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
  cursor: pointer;
  padding: 2px 6px;
  margin-left: 4px;
  border-radius: var(--radius-sm);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-medium);
  text-underline-offset: 2px;
}

/* Show service area button on contact item hover */
.contact-item:hover .service-area-button {
  opacity: 1;
  visibility: visible;
}

/* Service area button hover state */
.service-area-button:hover {
  color: var(--color-primary-lighter);
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.contact-item span {
  font-size: 0.875rem;
  font-family: sans-serif;
  text-shadow: var(--text-shadow-light);
  -webkit-background-clip: var(--bg-clip-text);
  -moz-background-clip: var(--bg-clip-text);
  background-clip: var(--bg-clip-text);
  transition: color var(--transition-medium);
  color: var(--color-text-medium);
  font-weight: 600;
  font-family: var(--font-secondary);
  font-size: 14px;
  letter-spacing: 0.2px;
  transition: color var(--transition-medium), font-size var(--transition-medium);
}

.contact-item:hover span {
  color: var(--color-text-dark);
  font-size: 16px;
  font-weight: 900;
}

/* ======================================================
BOOK APPOINTMENT BUTTON
====================================================== */

.book-button {
  /* Stacking Context and Basic Layout */
  z-index: 2;
  display: inline-block;
  cursor: pointer;
  position: relative;
  overflow: hidden; /* Prevents text from showing during initial state */

  /* Initial Dimensions and Positioning */
  width: 100px; /* Initial circular shape width */
  height: auto; /* Maintains circular proportion */
  top: 90%; /* Positioned near bottom of container */
  left: 50%; /* Centered horizontally */
  transform: translate(-50%, 150%) scale(0.5); /* Positioned below card, scaled down */
  padding: 0.6rem;
  border-radius: 555px; /* Large value ensures circular shape */

  /* Initial Visual State */
  opacity: 0; /* Hidden initially */
  filter: blur(80px); /* Heavy blur effect when hidden */
  color: hsl(0, 0%, 90%); /* Gray text */
  background-color: var(--color-primary); /* Teal base color */

  /* Border Styling - Multi-tone borders create depth */
  border-top: var(--color-primary) 1px solid; /* Darker top border */
  border-bottom: var(--color-primary-lighter) 6px solid; /* Lighter, thicker bottom border */
  border-left: var(--color-primary-lighter) 2px solid; /* Lighter side borders */
  border-right: var(--color-primary-lighter) 2px solid;
  box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.0); /* No initial glow */

  /* Performance optimization */
  will-change: transform, width, opacity, filter;

  /* Transition Properties - Controls animation timing and easing */
  transition:
    width 0.4s ease-in-out, /* Faster width transition */
    left 0.4s ease-in-out,
    height 0.4s ease-in-out,
    background-color 0.2s ease-in-out, /* Quick color changes */
    opacity 0.4s ease-in-out, /* Smooth fade in/out */
    transform 0.4s ease-in-out, /* Simpler transform transition */
    filter 0.4s ease-in-out, /* Smooth blur transition */
    box-shadow var(--transition-medium); /* Smooth glow transition */
}

/* Button Appearance When Card is Hovered */
.card:hover .book-button {
  /* New Dimensions and Positioning */
  width: 100%; /* Expands to full width */
  height: 50px; /* Fixed height for button */
  top: 0%; /* Moves to top of container */
  transform: translate(-50%, -3%) scale(1); /* Slight upward shift, normal scale */

  /* Visible State */
  opacity: 1; /* Fully visible */
  filter: blur(0px); /* No blur */
  box-shadow: 0px 0px 0px 0px hsl(184, 10%, 40%); /* No shadow initially */

  /* Same transition properties to ensure consistent animation */
  transition:
    width 0.8s cubic-bezier(0.6, -0.28, 0.735, 0.045), /* Back ease-in for width expansion - slightly faster */
    left 0.8s ease,
    height 0.8s ease,
    background-color 0.02s ease-in-out, /* Quick color changes */
    opacity 0.6s ease-in-out, /* Smooth fade in/out */
    transform 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55), /* Elastic/spring effect */
    filter 0.35s ease-in-out, /* Smooth blur transition */
    box-shadow var(--transition-medium); /* Smooth glow transition */
}

/* Button Hover State */
.book-button:hover {
  /* Darker color scheme on hover */
  background-color: var(--color-primary-dark); /* Darker teal */
  border-bottom: var(--color-primary-darker) 6px solid; /* Darker borders */
  border-left: var(--color-primary-darker) 2px solid;
  border-right: var(--color-primary-darker) 2px solid;

  /* Applies the pulsing animation */
  animation: buttonhover 1.5s ease-in-out infinite;
}

/* Button Active State (When Clicked) */
.book-button:active {
  /* Maintain expanded dimensions */
  width: 100%;
  height: 50px;
  top: 0%;
  transform: scale(1); /* No scaling effect */

  /* Visual feedback for click */
  background-color: var(--color-primary-light); /* Lighter teal */
  box-shadow: 0px 0px 16px 6px var(--color-primary-darker); /* Prominent glow effect */

  /* Lighter borders create pressed appearance */
  border-top: hsl(184, 100%, 70%) 1px solid; /* Much lighter top */
  border-bottom: hsl(184, 100%, 60%) 3px solid; /* Thinner bottom border */
  border-left: hsl(184, 100%, 60%) 2px solid;
  border-right: hsl(184, 100%, 60%) 2px solid;
}

/* Button Text Styling and Animation */
.book-button span {
  /* Text styling */
  text-decoration: none;
  text-transform: uppercase;
  font-family: var(--font-secondary);
  text-align: center;
  font-weight: 900; /* Bold text */
  font-size: 16px;
  letter-spacing: 3px; /* Spaced out letters */

  /* Positioning */
  display: block;
  position: relative;
  z-index: 1; /* Ensures text appears above any effects */
  opacity: 0; /* Start invisible */
  right: 355px; /* Start position */

  /* Transition for manual control */
  transition: opacity 0.4s ease-in-out, right 0.4s ease-in-out, filter 0.4s ease-in-out;
}

/* Apply animation when card is hovered */
.card:hover .book-button span {
  animation: appt-text-reveal 1.4s var(--transition-elastic) forwards; /* Spring effect */
  animation-fill-mode: both;
  will-change: opacity, right, filter;
  pointer-events: auto;
}

/* Button Text Hover State */
.book-button:hover span {
  color: var(--color-white); /* White text on hover */
  font-size: 16px;
  font-weight: 900;
}

/* ======================================================
ANIMATIONS
====================================================== */
/* Animation keyframes have been moved to animations.css */
/* Responsive styles moved to responsive.css */