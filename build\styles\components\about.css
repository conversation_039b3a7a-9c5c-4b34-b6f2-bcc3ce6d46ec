@import '../base.css';

/**
 * About Component Styles
 *
 * This file contains styles for the about me page:
 * - About me page layout
 * - About me text styling
 * - Back button styling
 */

/* ======================================================
ABOUT ME PAGE STYLES
====================================================== */

/* ABOUT ME PAGE BUTTON - currently shows the page when clicked */
.about-me:active + .about-me-page {
  pointer-events: auto;
  opacity: 1;
}

/* ABOUT ME PAGE SIZE */
.card.about-me-active {
  top: -0px; /* Adjust the top position */
  height: 480px;  /* Adjust the height */
  max-width: 500px;   /* Adjust the max-width */
}

.about-me-page {
  position: absolute;
  inset: 6px;
  background-color: rgb(217, 241, 241);
  opacity: 0;
  z-index: 30;
  pointer-events: none;
  transition: opacity var(--transition-medium);
  mix-blend-mode: normal;
  padding: 20px;
  will-change: opacity;
}

.about-me-text {
  margin-top: 14px;
  margin-left: 12px;
  margin-right: 12px;
  text-indent: 1em;
  text-align: justify;
  text-shadow: 1px 1.01px 1.01px #777;
  background-color: var(--color-grey);
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
  color: var(--color-text-medium);
  font-weight: 400;
  font-family: var(--font-secondary);
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.2px;
  transition: color var(--transition-medium);
  position: relative;
}

/* ======================================================
BACK BUTTON STYLES
====================================================== */

/* Add styles for the back button */
.back-button {
  position: absolute;
  bottom: 20px;
  right: 20px;
  padding: 12px 24px;
  background: linear-gradient(to right, var(--color-primary-dark), var(--color-primary));
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all var(--transition-medium);
  font-family: var(--font-tertiary);
  font-weight: 600;
  letter-spacing: 1px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  will-change: transform, box-shadow;
}

.back-button::before {
  content: '←';
  margin-right: 10px;
  font-size: 1.2rem;
  transition: transform var(--transition-medium);
}

.back-button:hover {
  background: linear-gradient(to right, var(--color-primary-darker), var(--color-primary-dark));
  transform: translateY(-3px);
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.15);
}

.back-button:hover::before {
  transform: translateX(-4px);
}

.back-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* Responsive styles moved to responsive.css */