/**
 * Card Effects Module
 *
 * This module handles all visual effects for the business card including:
 * - Holographic effects
 * - Shine effects
 * - Parallax effects
 * - Reveal effects (glitter, vmaxbg, galaxy)
 * - Mouse gradient effects
 */

import { isMobileDevice, getElement, addSafeEventListener, removeSafeEventListener } from './utils.js';
import { addThrottledGlobalEvent } from './event-delegation.js';

/**
 * List of effects that should be disabled on mobile devices
 * @type {string[]}
 */
const MOBILE_DISABLED_EFFECTS = [
  'holographic',
  'shine',
  'rainbow-sphere',
  'glitter',
  'vmaxbg',
  'galaxy'
];

/**
 * Initializes all card effects and event listeners
 * @param {HTMLElement} card - The card element
 */
export function initCardEffects(card) {
  // Validate card element
  if (!card) {
    console.error('Card element is required for card effects');
    return { resetEffects: () => {}, cleanup: () => {} };
  }

  // Card effect elements - stored in a structured object to avoid redundant null checks
  const elements = {
    card,
    mouseGradient: document.querySelector('.mouse-gradient'),
    shineThick: document.querySelector('.shine-thick'),
    shineSkinny: document.querySelector('.shine-skinny'),
    shineExtraThick: document.querySelector('.shine-extra-thick'),
    holographicEffect: document.querySelector('.holographic-effect'),
    rainbowSphere: document.querySelector('.rainbow-sphere'),
    glitterReveal: document.querySelector('.glitter-reveal'),
    vmaxbgReveal: document.querySelector('.vmaxbg-reveal'),
    galaxyReveal: document.querySelector('.galaxy-reveal'),
    rainbowGif: document.querySelector('.rainbow-gif'),
    button: document.querySelector('.book-button'),
    buttonSpan: document.querySelector('.book-button span'),
    // Cache these elements for performance
    cardContent: card.querySelector('.card-content'),
    cardInner: card.querySelector('.card-inner')
  };

  /**
   * Checks if a specific effect should be disabled on mobile devices
   * @param {string} effectType - The type of effect to check
   * @returns {boolean} - True if the effect should be disabled on mobile
   */
  function shouldDisableEffectOnMobile(effectType) {
    return isMobileDevice() && MOBILE_DISABLED_EFFECTS.includes(effectType);
  }

  // State variables
  const state = {
    isHovering: false,
    isLeaving: false,
    leaveTimeout: null,
    currentShineAngle: 45, // Track the current angle of the shine effect
    removeMouseMoveListener: null // Function to remove mousemove listener
  };

  // Set up all event listeners using event delegation for consistency
  // Use event delegation for card events
  addSafeEventListener(elements.card, 'mouseenter', handleMouseEnter);
  addSafeEventListener(elements.card, 'mouseleave', handleMouseLeave);

  // Use throttled global event for mousemove
  state.removeMouseMoveListener = addThrottledGlobalEvent('mousemove', handleMouseMove, 10);

  // Initialize effects for mobile devices - disable specified effects on page load
  if (isMobileDevice()) {
    // Get all elements from the elements object
    const {
      mouseGradient, shineThick, shineSkinny, shineExtraThick,
      holographicEffect, rainbowSphere, glitterReveal,
      vmaxbgReveal, galaxyReveal
    } = elements;

    // Disable shine effects
    if (shouldDisableEffectOnMobile('shine')) {
      if (mouseGradient) mouseGradient.style.opacity = '0';
      if (shineThick) shineThick.style.opacity = '0';
      if (shineSkinny) shineSkinny.style.opacity = '0';
      if (shineExtraThick) shineExtraThick.style.opacity = '0';
    }

    // Disable holographic effect
    if (shouldDisableEffectOnMobile('holographic') && holographicEffect) {
      holographicEffect.style.opacity = '0';
    }

    // Disable rainbow sphere
    if (shouldDisableEffectOnMobile('rainbow-sphere') && rainbowSphere) {
      rainbowSphere.style.opacity = '0';
    }

    // Disable reveal effects
    if (shouldDisableEffectOnMobile('glitter') && glitterReveal) {
      glitterReveal.style.opacity = '0';
    }

    if (shouldDisableEffectOnMobile('vmaxbg') && vmaxbgReveal) {
      vmaxbgReveal.style.opacity = '0';
    }

    if (shouldDisableEffectOnMobile('galaxy') && galaxyReveal) {
      galaxyReveal.style.opacity = '0';
    }
  }

  /**
   * Calculates distance between two points
   * @param {number} x1 - First point x coordinate
   * @param {number} y1 - First point y coordinate
   * @param {number} x2 - Second point x coordinate
   * @param {number} y2 - Second point y coordinate
   * @returns {number} - Distance between points
   */
  function getDistance(x1, y1, x2, y2) {
    return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
  }

  /**
   * Handles mouse enter event on the card
   */
  function handleMouseEnter() {
    // If we're still in the leaving transition, don't re-enter yet
    if (state.isLeaving) return;

    state.isHovering = true;

    // Set opacity for all effect elements
    // Using the elements object for cleaner code and avoiding redundant null checks
    const {
      mouseGradient, shineThick, shineSkinny, shineExtraThick,
      holographicEffect, rainbowSphere, glitterReveal,
      vmaxbgReveal, galaxyReveal, rainbowGif, button, buttonSpan
    } = elements;

    // Apply opacity values to elements that exist, checking if they should be disabled on mobile
    if (mouseGradient && !shouldDisableEffectOnMobile('shine')) mouseGradient.style.opacity = '1';

    // Shine effects
    if (!shouldDisableEffectOnMobile('shine')) {
      if (shineThick) shineThick.style.opacity = '1';
      if (shineSkinny) shineSkinny.style.opacity = '1';
      if (shineExtraThick) shineExtraThick.style.opacity = '1';
    }

    // Holographic effect
    if (holographicEffect && !shouldDisableEffectOnMobile('holographic')) {
      holographicEffect.style.opacity = '1';
    }

    // Rainbow sphere
    if (rainbowSphere && !shouldDisableEffectOnMobile('rainbow-sphere')) {
      rainbowSphere.style.opacity = '0.1';
    }

    // Reveal effects
    if (glitterReveal && !shouldDisableEffectOnMobile('glitter')) {
      glitterReveal.style.opacity = '0.7';
    }

    if (vmaxbgReveal && !shouldDisableEffectOnMobile('vmaxbg')) {
      vmaxbgReveal.style.opacity = '1';
    }

    if (galaxyReveal && !shouldDisableEffectOnMobile('galaxy')) {
      galaxyReveal.style.opacity = '0.7';
    }

    // Always keep rainbow-gif active
    if (rainbowGif) rainbowGif.style.opacity = '0.15'; // Slightly increase opacity on hover

    // Handle button elements
    if (button) button.removeAttribute('style'); // Remove inline styles to let CSS handle transitions
    if (buttonSpan) buttonSpan.removeAttribute('style'); // Remove inline styles to let CSS handle animation
  }

  /**
   * Handles mouse leave event on the card
   */
  function handleMouseLeave() {
    state.isLeaving = true;
    state.isHovering = false;

    // Get all elements from the elements object
    const {
      card, mouseGradient, shineThick, shineSkinny, shineExtraThick,
      holographicEffect, rainbowSphere, glitterReveal,
      vmaxbgReveal, galaxyReveal, rainbowGif, buttonSpan, button
    } = elements;

    // Reset visual effects immediately, checking if they should be disabled on mobile
    if (mouseGradient && !shouldDisableEffectOnMobile('shine')) mouseGradient.style.opacity = '0';

    // Shine effects
    if (!shouldDisableEffectOnMobile('shine')) {
      if (shineThick) shineThick.style.opacity = '0';
      if (shineSkinny) shineSkinny.style.opacity = '0';
      if (shineExtraThick) shineExtraThick.style.opacity = '0';
    }

    // Holographic effect - keep subtle effect on desktop, disable on mobile
    if (holographicEffect) {
      if (shouldDisableEffectOnMobile('holographic')) {
        holographicEffect.style.opacity = '0'; // Completely hide on mobile
      } else {
        holographicEffect.style.opacity = '1'; // Return to default subtle effect on desktop
      }
    }

    // Rainbow sphere
    if (rainbowSphere) {
      if (shouldDisableEffectOnMobile('rainbow-sphere')) {
        rainbowSphere.style.opacity = '0'; // Hide on mobile
      } else {
        rainbowSphere.style.opacity = '0.1'; // Default opacity on desktop
      }
    }

    // Reveal effects
    if (glitterReveal) {
      if (shouldDisableEffectOnMobile('glitter')) {
        glitterReveal.style.opacity = '0'; // Hide on mobile
      } else {
        glitterReveal.style.opacity = '0.7'; // Default opacity on desktop
      }
    }

    if (vmaxbgReveal) {
      if (shouldDisableEffectOnMobile('vmaxbg')) {
        vmaxbgReveal.style.opacity = '0'; // Hide on mobile
      } else {
        vmaxbgReveal.style.opacity = '1'; // Default opacity on desktop
      }
    }

    if (galaxyReveal) {
      if (shouldDisableEffectOnMobile('galaxy')) {
        galaxyReveal.style.opacity = '0'; // Hide on mobile
      } else {
        galaxyReveal.style.opacity = '0.7'; // Default opacity on desktop
      }
    }

    // Reset rainbow gif - always keep active
    if (rainbowGif) {
      rainbowGif.style.transform = 'translate(0px, 0px) scale(1.0)'; // Reset both parallax and scaling
      rainbowGif.style.opacity = '0'; // Reset to default opacity
    }

    // Reset button text animation
    if (buttonSpan) {
      buttonSpan.style.animation = 'none'; // Reset the animation by removing it
      void buttonSpan.offsetWidth; // Force reflow
    }

    // Reset card transform
    if (card) {
      // Check if we're on About Me or Services page on mobile
      const isAboutMeOrServicesPage = card.classList.contains('about-me-active') || card.classList.contains('services-active');

      // Always reset to flat position for About Me and Services pages on mobile
      if (isMobileDevice() && isAboutMeOrServicesPage) {
        card.style.transform = 'rotateX(0deg) rotateY(0deg)';
      } else {
        // Normal reset for other cases
        card.style.transform = 'rotateX(0deg) rotateY(0deg)';
      }
    }

    // Clear any existing timeout
    clearTimeout(state.leaveTimeout);

    // Set a timeout to reset the button immediately
    state.leaveTimeout = setTimeout(() => {
      state.isLeaving = false;

      // Use the cached button element instead of querying the DOM again
      if (button) {
        // Just force a reflow to ensure transitions will work properly next time
        // but don't set inline styles that would override CSS
        void button.offsetWidth;
      }
    }, 0); // No delay to ensure CSS transitions are reset immediately
  }

  // Store animation frame ID for cleanup
  let currentAnimationFrame = null;

  /**
   * Handles mouse move event for card effects
   * @param {MouseEvent} e - Mouse event
   */
  function handleMouseMove(e) {
    // Skip processing if card doesn't exist
    if (!elements.card) return;

    // Cancel any pending animation frame to prevent queuing multiple frames
    if (currentAnimationFrame) {
      cancelAnimationFrame(currentAnimationFrame);
    }

    // Use requestAnimationFrame for smoother animations
    currentAnimationFrame = requestAnimationFrame(() => {
      updateCardEffects(e);
      currentAnimationFrame = null;
    });
  }

  /**
   * Updates all card visual effects based on mouse position
   * @param {MouseEvent} e - Mouse event
   */
  function updateCardEffects(e) {
    const { card, cardContent, cardInner } = elements;

    const rect = card.getBoundingClientRect();
    const cardCenterX = rect.left + rect.width / 2;
    const cardCenterY = rect.top + rect.height / 2;

    // Calculate distance from mouse to card center
    const distance = getDistance(e.clientX, e.clientY, cardCenterX, cardCenterY);
    const maxDistance = Math.max(rect.width, rect.height);

    // Handle card tilt effect
    const x = e.clientX - cardCenterX;
    const y = e.clientY - cardCenterY;

    // Check if we should apply rotation effects
    // Skip rotation for About Me and Services pages on mobile
    const isAboutMeOrServicesPage = card.classList.contains('about-me-active') || card.classList.contains('services-active');
    const shouldApplyRotation = !(isMobileDevice() && isAboutMeOrServicesPage);

    if (shouldApplyRotation) {
      const dampening = 0.5;
      const rotateX = Math.min(Math.max((y / (rect.height / 2)) * 20 * dampening, -20), 20) * -1;
      const rotateY = Math.min(Math.max((x / (rect.width / 2)) * -20 * dampening, -20), 20) * -1;
      card.style.transform = `rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
    } else {
      // Keep card flat on mobile for About Me and Services pages
      card.style.transform = 'rotateX(0deg) rotateY(0deg)';
    }

    // Get content rect for positioning elements - use cached elements for better performance
    const contentRect = cardContent.getBoundingClientRect();
    const innerRect = cardInner.getBoundingClientRect();
    const gradientX = e.clientX - contentRect.left;
    const gradientY = e.clientY - contentRect.top;

    // Calculate proximity factor for opacity
    const proximityFactor = Math.max(0, 1 - (distance / (maxDistance * 1.5)));

    // Update all effects, checking if they should be disabled on mobile

    // Mouse gradient (part of shine effect)
    if (!shouldDisableEffectOnMobile('shine')) {
      updateMouseGradient(gradientX, gradientY, proximityFactor);
    }

    // Shine effects
    if (!shouldDisableEffectOnMobile('shine')) {
      updateShineEffects(e, innerRect, proximityFactor);
    }

    // Holographic effect
    if (!shouldDisableEffectOnMobile('holographic')) {
      updateHolographicEffect(x, y, rect, proximityFactor);
    }

    // Always update rainbow gif effect (keep active on mobile)
    updateRainbowGifEffect(x, y, rect);

    // Rainbow sphere
    if (!shouldDisableEffectOnMobile('rainbow-sphere')) {
      updateRainbowSphere(e, innerRect, x, y, rect, distance, maxDistance);
    }

    // Reveal effects (glitter, vmaxbg, galaxy)
    if (!shouldDisableEffectOnMobile('glitter') ||
        !shouldDisableEffectOnMobile('vmaxbg') ||
        !shouldDisableEffectOnMobile('galaxy')) {
      updateRevealEffects(e, innerRect, x, y, rect, distance, maxDistance);
    }
  }

  /**
   * Updates mouse gradient effect
   * @param {number} gradientX - X position relative to content
   * @param {number} gradientY - Y position relative to content
   * @param {number} proximityFactor - Factor based on mouse proximity to card
   */
  function updateMouseGradient(gradientX, gradientY, proximityFactor) {
    const { mouseGradient } = elements;
    if (!mouseGradient) return;

    // Position the gradient at mouse position relative to card content
    mouseGradient.style.left = `${gradientX}px`;
    mouseGradient.style.top = `${gradientY}px`;

    // Update opacity based on hover state and proximity
    if (!state.isHovering) {
      // Gradually show gradient as mouse approaches card
      mouseGradient.style.opacity = proximityFactor.toFixed(2);
    }
    // When hovering, the opacity is set to 1 in the handleMouseEnter function
  }

  /**
   * Updates shine effects
   * @param {MouseEvent} e - Mouse event
   * @param {DOMRect} innerRect - Card inner element bounding rect
   * @param {number} proximityFactor - Factor based on mouse proximity to card
   */
  function updateShineEffects(e, innerRect, proximityFactor) {
    const { card, shineThick, shineSkinny, shineExtraThick } = elements;
    if (!shineThick || !shineSkinny || !shineExtraThick) return;

    // Calculate horizontal position based on mouse X position relative to card inner width
    const horizontalPosition = (e.clientX - innerRect.left) / innerRect.width;

    // Map the horizontal position to a percentage across the card inner
    // This will make the shine move from left (-50%) to right (150%) of the card
    const shinePositionX = (horizontalPosition * 200 - 50) + '%';

    // Determine if we should swap the angle based on horizontal mouse position only
    if (!state.isHovering) {
      // Check horizontal position relative to card
      // If mouse is to the left of the card, use -45 degrees
      // If mouse is to the right of the card, use 45 degrees
      const cardRect = card.getBoundingClientRect();

      // Determine if mouse is to the left or right of the card
      if (e.clientX < cardRect.left - 50) { // 50px buffer zone outside the card
        state.currentShineAngle = -45;
      } else if (e.clientX > cardRect.right + 50) { // 50px buffer zone outside the card
        state.currentShineAngle = 45;
      }
      // When mouse is within the buffer zone or over the card, angle stays the same
    }

    // Position the shine elements horizontally based on mouse X position
    shineThick.style.left = shinePositionX;
    shineThick.style.top = '50%'; // Center vertically
    shineThick.style.transform = `translate(-65%, -50%) rotate(${state.currentShineAngle}deg)`;

    // Position skinny bar with slight offset
    shineSkinny.style.left = `calc(${shinePositionX} + 10px)`;
    shineSkinny.style.top = '50%'; // Slight vertical offset
    shineSkinny.style.transform = `translate(-80%, -50%) rotate(${state.currentShineAngle}deg)`;

    // Position extra thick bar with different offset
    shineExtraThick.style.left = `calc(${shinePositionX} - 15px)`;
    shineExtraThick.style.top = '50%'; // Different vertical offset
    shineExtraThick.style.transform = `translate(-63%, -50%) rotate(${state.currentShineAngle}deg)`;

    // Show shine when close to or hovering over card
    if (!state.isHovering) {
      shineThick.style.opacity = proximityFactor.toFixed(2);
      shineSkinny.style.opacity = proximityFactor.toFixed(2);
      shineExtraThick.style.opacity = proximityFactor.toFixed(2);
    }
  }

  /**
   * Updates holographic effect
   * @param {number} x - X position relative to card center
   * @param {number} y - Y position relative to card center
   * @param {DOMRect} rect - Card bounding rect
   * @param {number} proximityFactor - Factor based on mouse proximity to card
   */
  function updateHolographicEffect(x, y, rect, proximityFactor) {
    const { holographicEffect } = elements;
    if (!holographicEffect) return;

    // Update background position using the generic function with larger movement amount
    updateBackgroundPosition(holographicEffect, x, y, rect, 20);

    // Adjust hue rotation based on mouse position
    const hueRotate = ((x / rect.width) * 0) - 0; // -30 to +30 degrees
    holographicEffect.style.filter = `hue-rotate(${hueRotate}deg) brightness(1.2)`;

    // Adjust opacity based on proximity when not hovering
    if (!state.isHovering) {
      // Scale from 0.5 (default) to 0.8 (hover) based on proximity
      const opacityValue = 1 + (proximityFactor * 0.1);
      holographicEffect.style.opacity = opacityValue.toFixed(2);
    }
  }

  /**
   * Updates rainbow gif parallax effect
   * @param {number} x - X position relative to card center
   * @param {number} y - Y position relative to card center
   * @param {DOMRect} rect - Card bounding rect
   */
  function updateRainbowGifEffect(x, y, rect) {
    const { rainbowGif } = elements;
    if (!rainbowGif) return;

    // Create a more pronounced parallax effect by using a larger multiplier
    // This will make the rainbow-gif move in the opposite direction of the mouse
    // for a deeper parallax effect
    const maxParallaxMove = 30; // Maximum pixel movement in any direction
    const parallaxX = (x / rect.width) * -maxParallaxMove; // Move in opposite direction
    const parallaxY = (y / rect.height) * -maxParallaxMove; // Move in opposite direction

    // Calculate the distance factor using the normalized coordinates
    const normalizedX = Math.abs(x / (rect.width / 2)); // 0 at center, 1 at edge
    const normalizedY = Math.abs(y / (rect.height / 2)); // 0 at center, 1 at edge
    const distanceFromCenter = Math.max(normalizedX, normalizedY);

    // Calculate scale factor: start at 1.0 (no scaling) at center
    // and increase as we move toward edges
    // The 0.15 value controls how much extra scaling to apply (adjust as needed)
    const scaleAmount = 1.0 + (distanceFromCenter * 0.15);

    // Apply both translation and scaling in a single transform
    rainbowGif.style.transform = `translate(${parallaxX}px, ${parallaxY}px) scale(${scaleAmount})`;

    // Set opacity based on hover state
    rainbowGif.style.opacity = state.isHovering ? '0' : '0.15';
  }

  /**
   * Updates rainbow sphere effect
   * @param {MouseEvent} e - Mouse event
   * @param {DOMRect} innerRect - Card inner element bounding rect
   * @param {number} x - X position relative to card center
   * @param {number} y - Y position relative to card center
   * @param {DOMRect} rect - Card bounding rect
   * @param {number} distance - Distance from mouse to card center
   * @param {number} maxDistance - Maximum distance
   */
  function updateRainbowSphere(e, innerRect, x, y, rect, distance, maxDistance) {
    const { rainbowSphere } = elements;
    if (!rainbowSphere) return;

    // Position the rainbow sphere at mouse position relative to card
    const sphereX = e.clientX - innerRect.left;
    const sphereY = e.clientY - innerRect.top;

    rainbowSphere.style.left = `${sphereX}px`;
    rainbowSphere.style.top = `${sphereY}px`;

    // Calculate distance factor using the generic function
    const distanceFactor = calculateDistanceFactor(x, y, rect);

    // Update opacity using the generic function with custom parameters
    updateElementOpacity(rainbowSphere, state.isHovering ? 0.7 : 1, distanceFactor, 1);

    // Add some color rotation based on position
    const blurAmount = 50 + (0.3 - distanceFactor) * 0.1; // More blur at edges
    const contrastAmount = 1.8 - (0.3 - distanceFactor) * 0.1; // Less contrast at edges

    rainbowSphere.style.filter = `blur(${blurAmount}px) contrast(${contrastAmount}) saturate(1.1)`;
    rainbowSphere.style.boxShadow = `0 0 ${30 * distanceFactor}px ${10 * distanceFactor}px rgba(255, 255, 255, ${0.3 * distanceFactor})`;
  }

  /**
   * Generic function to calculate distance factor from center
   * @param {number} x - X position relative to card center
   * @param {number} y - Y position relative to card center
   * @param {DOMRect} rect - Card bounding rect
   * @returns {number} - Distance factor (0-1, where 1 is at center, 0 is at max distance)
   */
  function calculateDistanceFactor(x, y, rect) {
    const distanceFromCenter = Math.sqrt(Math.pow(x, 2) + Math.pow(y, 2));
    const maxDistance = Math.sqrt(Math.pow(rect.width/2, 2) + Math.pow(rect.height/2, 2));
    return 1 - (distanceFromCenter / maxDistance);
  }

  /**
   * Generic function to update element background position based on mouse
   * @param {HTMLElement} element - The element to update
   * @param {number} x - X position relative to card center
   * @param {number} y - Y position relative to card center
   * @param {DOMRect} rect - Card bounding rect
   * @param {number} moveAmount - Amount of movement (percentage)
   */
  function updateBackgroundPosition(element, x, y, rect, moveAmount = 10) {
    if (!element) return;
    const bgPosX = 50 + (x / rect.width) * moveAmount;
    const bgPosY = 50 + (y / rect.height) * moveAmount;
    element.style.backgroundPosition = `${bgPosX}% ${bgPosY}%`;
  }

  /**
   * Generic function to update element opacity based on hover state and proximity
   * @param {HTMLElement} element - The element to update
   * @param {number} baseOpacity - Base opacity value
   * @param {number} distanceFactor - Factor based on distance from center
   * @param {number} hoverOpacityBoost - Additional opacity when hovering
   */
  function updateElementOpacity(element, baseOpacity, distanceFactor, hoverOpacityBoost = 1) {
    if (!element) return;
    if (!state.isHovering) {
      element.style.opacity = baseOpacity.toString();
    } else {
      element.style.opacity = (baseOpacity + distanceFactor * hoverOpacityBoost).toFixed(2);
    }
  }

  /**
   * Updates reveal effects (glitter, vmaxbg, galaxy)
   * @param {MouseEvent} e - Mouse event
   * @param {DOMRect} innerRect - Card inner element bounding rect
   * @param {number} x - X position relative to card center
   * @param {number} y - Y position relative to card center
   * @param {DOMRect} rect - Card bounding rect
   * @param {number} distance - Distance from mouse to card center
   * @param {number} maxDistance - Maximum distance
   */
  function updateRevealEffects(e, innerRect, x, y, rect, distance, maxDistance) {
    const { glitterReveal, vmaxbgReveal, galaxyReveal } = elements;

    // Define the configuration for each reveal effect with their effect type
    const revealEffects = [
      { element: glitterReveal, baseMaskSize: 55, maskSizeVariation: 25, baseOpacity: 0.7, effectType: 'glitter' },
      { element: vmaxbgReveal, baseMaskSize: 85, maskSizeVariation: 35, baseOpacity: 1, effectType: 'vmaxbg' },
      { element: galaxyReveal, baseMaskSize: 55, maskSizeVariation: 25, baseOpacity: 0.7, effectType: 'galaxy' }
    ];

    // Update each effect using the same function with different parameters
    // Only update effects that aren't disabled on mobile
    revealEffects.forEach(config => {
      // Skip this effect if it should be disabled on mobile
      if (shouldDisableEffectOnMobile(config.effectType)) {
        return;
      }

      updateRevealEffect(
        config.element,
        e,
        innerRect,
        x,
        y,
        rect,
        config.baseMaskSize,
        config.maskSizeVariation,
        config.baseOpacity
      );
    });
  }

  /**
   * Updates a specific reveal effect
   * @param {HTMLElement} element - The reveal effect element
   * @param {MouseEvent} e - Mouse event
   * @param {DOMRect} innerRect - Card inner element bounding rect
   * @param {number} x - X position relative to card center
   * @param {number} y - Y position relative to card center
   * @param {DOMRect} rect - Card bounding rect
   * @param {number} baseMaskSize - Base mask size percentage
   * @param {number} maskSizeVariation - Mask size variation percentage
   * @param {number} baseOpacity - Base opacity value
   */
  function updateRevealEffect(element, e, innerRect, x, y, rect, baseMaskSize, maskSizeVariation, baseOpacity) {
    if (!element) return;

    // Update the mask position to follow the mouse
    const maskX = ((e.clientX - innerRect.left) / innerRect.width) * 100;
    const maskY = ((e.clientY - innerRect.top) / innerRect.height) * 100;

    // Calculate distance factor using the generic function
    const distanceFactor = calculateDistanceFactor(x, y, rect);

    // Mask size increases as mouse gets closer to center
    const maskSize = baseMaskSize + distanceFactor * maskSizeVariation;

    // Apply mask image
    element.style.webkitMaskImage = `radial-gradient(circle at ${maskX}% ${maskY}%, black 0%, transparent ${maskSize}%)`;
    element.style.maskImage = `radial-gradient(circle at ${maskX}% ${maskY}%, black 0%, transparent ${maskSize}%)`;

    // Update background position using the generic function
    updateBackgroundPosition(element, x, y, rect, 10);

    // Update opacity using the generic function
    updateElementOpacity(element, baseOpacity, distanceFactor, 1);

    // Adjust hue rotation based on position
    const hueRotate = ((x / rect.width) * 30) - 15; // -15 to +15 degrees
    element.style.filter = `contrast(${1.5 + distanceFactor * 0.5}) brightness(${1.5 + distanceFactor * 0.5}) hue-rotate(${hueRotate}deg)`;
  }

  /**
   * Utility function to safely cancel any pending animation frames
   * @param {number|null} id - The animation frame ID to cancel
   */
  function cancelAnimationFrameSafe(id) {
    if (id) {
      window.cancelAnimationFrame(id);
    }
  }

  // Return public methods and properties
  return {
    // Public methods that can be called from outside
    resetEffects: () => {
      handleMouseLeave();
    },
    // Enhanced cleanup method to remove all event listeners and clear all timers
    cleanup: () => {
      // Remove global event listener
      if (state.removeMouseMoveListener) {
        state.removeMouseMoveListener();
        state.removeMouseMoveListener = null;
      }

      // Remove event listeners using the safe method
      if (elements.card) {
        removeSafeEventListener(elements.card, 'mouseenter', handleMouseEnter);
        removeSafeEventListener(elements.card, 'mouseleave', handleMouseLeave);
      }

      // Clear any pending timeouts
      if (state.leaveTimeout) {
        clearTimeout(state.leaveTimeout);
        state.leaveTimeout = null;
      }

      // Cancel any pending animation frames
      cancelAnimationFrameSafe(currentAnimationFrame);

      // Clear references to DOM elements to prevent memory leaks
      Object.keys(elements).forEach(key => {
        elements[key] = null;
      });

      // Clear state object
      Object.keys(state).forEach(key => {
        if (typeof state[key] !== 'function') {
          state[key] = null;
        }
      });
    }
  };
}