/**
 * Event Delegation Module
 *
 * This module provides utilities for implementing event delegation:
 * - Centralized event handling
 * - Reduced number of event listeners
 * - Improved performance for dynamic elements
 */

/**
 * Creates a delegated event listener for a parent element
 * 
 * @param {HTMLElement} parentElement - The parent element to attach the listener to
 * @param {string} eventType - The event type to listen for (e.g., 'click', 'mouseover')
 * @param {string} selector - CSS selector to match target elements
 * @param {Function} handler - Event handler function
 * @param {Object|boolean} options - Event listener options
 * @returns {Function} - Function to remove the event listener
 */
export function delegateEvent(parentElement, eventType, selector, handler, options = false) {
  if (!parentElement) {
    console.warn(`Cannot delegate ${eventType} event: parent element is undefined`);
    return () => {};
  }

  const delegatedHandler = (event) => {
    // Find all matching elements that could be the target
    const potentialTargets = Array.from(parentElement.querySelectorAll(selector));
    
    // Check if the event target or any of its parents match the selector
    let currentElement = event.target;
    
    while (currentElement && currentElement !== parentElement) {
      if (potentialTargets.includes(currentElement)) {
        // Call the handler with the matching element as 'this' and the first argument
        handler.call(currentElement, event, currentElement);
        break;
      }
      currentElement = currentElement.parentElement;
    }
  };

  // Store the delegated handler to enable removal
  parentElement._delegatedHandlers = parentElement._delegatedHandlers || {};
  parentElement._delegatedHandlers[`${eventType}:${selector}`] = delegatedHandler;

  // Add the event listener to the parent
  parentElement.addEventListener(eventType, delegatedHandler, options);

  // Return a function to remove the event listener
  return () => {
    if (parentElement && parentElement._delegatedHandlers) {
      const handler = parentElement._delegatedHandlers[`${eventType}:${selector}`];
      if (handler) {
        parentElement.removeEventListener(eventType, handler, options);
        delete parentElement._delegatedHandlers[`${eventType}:${selector}`];
      }
    }
  };
}

/**
 * Creates multiple delegated event listeners for a parent element
 * 
 * @param {HTMLElement} parentElement - The parent element to attach listeners to
 * @param {Object} eventMap - Map of event types to selectors and handlers
 * @param {Object|boolean} options - Event listener options
 * @returns {Function} - Function to remove all event listeners
 */
export function delegateEvents(parentElement, eventMap, options = false) {
  if (!parentElement) {
    console.warn('Cannot delegate events: parent element is undefined');
    return () => {};
  }

  const removeFunctions = [];

  // For each event type in the map
  Object.entries(eventMap).forEach(([eventType, selectorHandlerPairs]) => {
    // For each selector and handler pair for this event type
    Object.entries(selectorHandlerPairs).forEach(([selector, handler]) => {
      // Create a delegated event and store the remove function
      const removeFunction = delegateEvent(
        parentElement, 
        eventType, 
        selector, 
        handler, 
        options
      );
      removeFunctions.push(removeFunction);
    });
  });

  // Return a function that will remove all delegated events
  return () => {
    removeFunctions.forEach(removeFn => removeFn());
  };
}

/**
 * Adds a global event listener with throttling for performance
 * 
 * @param {string} eventType - The event type to listen for
 * @param {Function} handler - Event handler function
 * @param {number} throttleMs - Throttle time in milliseconds
 * @param {Object|boolean} options - Event listener options
 * @returns {Function} - Function to remove the event listener
 */
export function addThrottledGlobalEvent(eventType, handler, throttleMs = 16, options = false) {
  let lastTime = 0;
  let requestId = null;

  const throttledHandler = (event) => {
    const now = Date.now();
    
    // If we're within the throttle time, cancel any pending handler
    if (now - lastTime < throttleMs) {
      if (requestId) {
        cancelAnimationFrame(requestId);
      }
      
      // Schedule the handler to run when the throttle time has elapsed
      requestId = requestAnimationFrame(() => {
        lastTime = Date.now();
        handler(event);
      });
      
      return;
    }
    
    // If we're outside the throttle time, run the handler immediately
    lastTime = now;
    handler(event);
  };

  // Store reference to original handler for removal
  throttledHandler._originalHandler = handler;
  
  // Add the event listener
  document.addEventListener(eventType, throttledHandler, options);
  
  // Return a function to remove the event listener
  return () => {
    if (requestId) {
      cancelAnimationFrame(requestId);
    }
    document.removeEventListener(eventType, throttledHandler, options);
  };
}
