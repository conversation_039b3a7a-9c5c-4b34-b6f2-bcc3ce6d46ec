/**
 * Services Module
 *
 * This module handles services page specific functionality:
 * - Service item interactions
 * - Service booking button functionality
 * - Service information display
 */

import { isMobileDevice } from './utils.js';
import { delegateEvents } from './event-delegation.js';

/**
 * Initializes services functionality
 * @returns {Object} - Public methods for controlling services
 */
export function initServices() {
  // Service elements
  const servicesPage = document.querySelector('.services-page');

  // Event removal function
  let removeServiceItemListeners;

  // Initialize event listeners
  initEventListeners();

  /**
   * Sets up all event listeners for services using event delegation
   */
  function initEventListeners() {
    // Skip if services page doesn't exist
    if (!servicesPage) return;

    // Use event delegation for service items
    removeServiceItemListeners = delegateEvents(servicesPage, {
      'mouseenter': {
        '.services-item': handleServiceItemHover
      },
      'mouseleave': {
        '.services-item': handleServiceItemLeave
      }
    });
  }

  /**
   * Handles service item hover
   * @param {Event} e - Mouse event
   */
  function handleServiceItemHover(e) {
    // Skip hover effects on mobile
    if (isMobileDevice()) return;

    const item = e.currentTarget;

    // Add hover class
    item.classList.add('hovered');

    // Optional: Add any additional hover effects here
  }

  /**
   * Handles service item leave
   * @param {Event} e - Mouse event
   */
  function handleServiceItemLeave(e) {
    // Skip hover effects on mobile
    if (isMobileDevice()) return;

    const item = e.currentTarget;

    // Remove hover class
    item.classList.remove('hovered');

    // Optional: Remove any additional hover effects here
  }

  /**
   * Enhances service items with additional functionality
   * This is a placeholder for future enhancements
   */
  function enhanceServiceItems() {
    // This function can be expanded in the future to add more
    // interactive features to service items
  }

  // Return public methods
  return {
    enhanceServiceItems,
    // Clean up method to remove event listeners
    cleanup: () => {
      if (removeServiceItemListeners) {
        removeServiceItemListeners();
      }
    }
  };
}