@import './base.css';

/**
 * Responsive Styles
 *
 * This file contains all media queries for responsive design:
 * - Mobile breakpoints (max-width: 768px)
 * - Tablet breakpoints (min-width: 769px) and (max-width: 1024px)
 * - Desktop breakpoints (min-width: 1025px)
 *
 * Consolidating media queries here improves maintainability and ensures
 * consistent behavior across screen sizes.
 */

/* ======================================================
MOBILE STYLES (max-width: 768px)
====================================================== */
@media (max-width: 768px) {
  /* Card container and structure */
  .card-container {
    top: 0;
    left: 0;
    transform: none;
    width: 100%;
    min-height: 100%;
    height: auto;
    position: fixed;
    perspective: 600px;
    transition: all var(--transition-medium);
  }

  .card {
    max-width: 500px; /* Default max-width */
    width: 100%;
    height: 350px; /* Default height */
    border-radius: 0;
    transition:
      height var(--transition-slow) cubic-bezier(0.25, 0.1, 0.25, 1),
      max-width var(--transition-slow) cubic-bezier(0.25, 0.1, 0.25, 1),
      transform 0.05s cubic-bezier(0.4, 0, 0.2, 1),
      box-shadow var(--transition-medium);
  }
  .book-button {
    will-change: transform, box-shadow;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    touch-action: manipulation;
  }

  /* Common styles for active pages (about-me) */
  .card.about-me-active {
    top: 0;
    height: 450px; /* Full viewport height to ensure content fits */
    max-width: 100%;
    width: 100%;
    overflow-y: auto;
    border-radius: 0;
  }
  /* Common styles for active pages  (services) */
  .card.services-active {
    top: 0;
    height: 90vh; /* Full viewport height to ensure content fits */
    max-width: 100%;
    width: 100%;
    overflow-y: auto;
    border-radius: 0;
  }

  /* Default card state (when not showing about-me or services) */
  .card-default {
    max-width: 500px;
    height: 310px;
    overflow-y: hidden;
    position: relative;
    top: auto;
  }

  .card-default:hover {
    height: 350px;
  }

  /* About Me page specific styles */
  .about-me-page {
    padding: 15px;
    padding-bottom: 100px; /* Increased padding at the bottom to ensure content doesn't overlap with the larger button */
  }

  .about-me-text {
    font-size: 14px;
    margin-left: 8px;
    margin-right: 8px;
  }

  /* Ensure back button is always visible at the bottom */
  .back-button {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 100;
    width: 80%;
    max-width: 300px;
    padding: 15px 20px;
    font-size: 18px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(to right, var(--color-primary-dark), var(--color-primary));
    color: white;
    border: none;
    cursor: pointer;
    transition: all var(--transition-medium);
    font-family: var(--font-tertiary);
    font-weight: 600;
    letter-spacing: 1px;
    will-change: transform, box-shadow;
  }

  .back-button::before {
    content: '←';
    margin-right: 10px;
    font-size: 1.2rem;
    transition: transform var(--transition-medium);
  }

  .back-button:hover {
    background: linear-gradient(to right, var(--color-primary-darker), var(--color-primary-dark));
    transform: translateX(-50%) translateY(-3px);
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.15);
  }

  .back-button:hover::before {
    transform: translateX(-4px);
  }

  .back-button:active {
    transform: translateX(-50%) translateY(1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
  /* Services page specific styles */

  .services-page {
    padding: 20px 15px 30px 15px; /* Reduced bottom padding since footer is now relative */
    border-radius: 0;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  }

  .services-container {
    flex-direction: column;
    gap: 15px;
  }

  .services-item {
    flex: 1 1 100%;
    min-width: 0;
    max-width: 100%;
    padding: 15px;
    margin-bottom: 15px;
  }

  .service-icon {
    width: 30px;
    height: 30px;
    flex-shrink: 0;
  }

  .service-header-content h2 {
    font-size: 1.1rem;
  }

  .service-description {
    font-size: 0.85rem;
  }

  .service-icon-container {
    min-width: 50px;
    height: 50px;
  }

  /* Important note styling for mobile */
  .important-note {
    padding: 12px 15px;
    width: 80%;
    margin: 20px auto 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transform: none;
    border-left: 4px solid var(--color-primary);
  }

  .important-note:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transform: none;
    border-left: 4px solid var(--color-primary-dark);
    background-color: rgba(255, 255, 255, 0.95);
  }

  .note-title {
    font-size: 0.9rem;
  }

  .note-text {
    font-size: 0.8rem;
  }

  /* Remove hover effects for service items on mobile */
  .services-item:hover {
    transform: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .services-item:hover .service-icon-container {
    background-color: rgba(79, 209, 197, 0.1);
    transform: none;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
  }

  /* Services footer and buttons for mobile */
  .services-footer {
    margin: 20px 0 0 0;
    padding: 15px 0;
    position: relative;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    background-color: rgba(230, 245, 245, 0.95); /* Match services page background with slight transparency */
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /* Change services buttons to column layout on mobile */
  .services-buttons-container {
    flex-direction: column;
    gap: 12px;
    padding: 0;
    align-items: center;
    max-width: 100%;
    width: 90%;
    margin: 0 auto;
  }

  /* Make services buttons full width on mobile */
  .services-book-button,
  .services-back-button,
  .services-area-button {
    flex: none;
    width: 100%;
    max-width: 350px;
    padding: 15px 20px;
    font-size: 18px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    margin: 0;
  }

  /* Make book button more prominent */
  .services-book-button {
    order: 1; /* Show book button first */
  }

  /* Style back button */
  .services-back-button {
    order: 2;
  }

  /* Remove hover effect for services title */
  .services-title:hover::after {
    width: 80px;
  }
}

/* ======================================================
TABLET STYLES (min-width: 769px) and (max-width: 1024px)
====================================================== */
@media (min-width: 769px) and (max-width: 1024px) {
  .services-container {
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
  }

  .services-item {
    flex: 0 1 calc(50% - 20px);
    min-width: 0;
  }

  /* Improve button visibility on tablets */
  .back-button {
    padding: 14px 28px;
    font-size: 16px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  }

  .services-buttons-container {
    gap: 20px;
  }

  .services-book-button,
  .services-back-button,
  .services-area-button {
    padding: 16px 24px;
    font-size: 16px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  }
}

/* ======================================================
PRINT STYLES
====================================================== */
@media print {
  .card-container {
    position: relative;
    top: 0;
    transform: none;
    margin: 0 auto;
  }

  .card {
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .book-button,
  .about-me-button,
  .services-button {
    display: none;
  }
}
