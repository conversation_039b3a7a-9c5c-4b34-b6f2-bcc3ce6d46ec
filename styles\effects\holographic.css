/**
 * Holographic Effects Styles
 *
 * This file contains styles for holographic visual effects:
 * - Holographic base effect
 * - Holographic grey effect
 * - Holographic screen effect
 * - Holographic sparkles effect
 * - Rainbow gif effect
 * - Rainbow sphere effect
 */

/* ======================================================
HOLOGRAPHIC EFFECTS
====================================================== */

/* Base holographic effect */
.holographic-effect {
  position: absolute;
  inset: 0; /* Cover the entire card */
  background-image: url(https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExMWl2eWhkZm9jMnl2b2R4YnR3ZGw0cmd1eTFtdThoYmRrdHV2NDR3YyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/WWSPhALYIBk1wtIwGZ/giphy.gif);
  background-size: 100% 100%;
  background-position: center;
  mix-blend-mode: overlay;
  opacity: 1; /* Start with a subtle effect */
  filter: hue-rotate(0deg) brightness(1) blur(0px);
  z-index: 3; /* Above card-background but below content */
  pointer-events: none; /* Allow clicks to pass through */
  will-change: background-position, filter, opacity;
}

/* Grey holographic effect */
.holographic-effect-grey {
  position: absolute;
  inset: 0; /* Cover the entire card */
  background-image: url(https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExMWl2eWhkZm9jMnl2b2R4YnR3ZGw0cmd1eTFtdThoYmRrdHV2NDR3YyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/WWSPhALYIBk1wtIwGZ/giphy.gif);
  background-size: 100%;
  background-position: center;
  mix-blend-mode: difference;
  opacity: 0.3; /* Start with a subtle effect */
  filter: grayscale(100%) invert(100%) hue-rotate(0deg) brightness(1) blur(0px);
  z-index: 4; /* Above card-background but below content */
  pointer-events: none; /* Allow clicks to pass through */
}

/* Screen holographic effect */
.holographic-effect-screen {
  position: absolute;
  inset: 0; /* Cover the entire card */
  background-image: url(https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExMWl2eWhkZm9jMnl2b2R4YnR3ZGw0cmd1eTFtdThoYmRrdHV2NDR3YyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/WWSPhALYIBk1wtIwGZ/giphy.gif);
  background-size: 100%;
  background-position: center;
  mix-blend-mode: screen;
  opacity: 0.3; /* Start with a subtle effect */
  filter: grayscale(100%) invert(100%) hue-rotate(0deg) brightness(1) blur(0px);
  z-index: 5; /* Above card-background but below content */
  pointer-events: none; /* Allow clicks to pass through */
}

/* Sparkles holographic effect */
.holographic-effect-sparkles {
  position: absolute;
  inset: 0; /* Cover the entire card */
  background-image: url(https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExejZmZHBtbTl4ZjF5azcxa2l0eXg4eDR2MHM3ZWsxam15cWlmb3A3cCZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/dZS6D8OxCz8DHs7HbJ/giphy.gif);
  background-size: 100%;
  background-position: center;
  mix-blend-mode: overlay;
  opacity: 0.1; /* Start with a subtle effect */
  filter: hue-rotate(0deg) brightness(1) blur(0px);
  z-index: 6; /* Above card-background but below content */
  pointer-events: none; /* Allow clicks to pass through */
}

/* Rainbow gif effect */
.rainbow-gif {
  position: absolute;
  inset: 0; /* Cover the entire card */
  background-image: url(https://media0.giphy.com/media/v1.Y2lkPTc5MGI3NjExM3Rld2JzN2VhajQ3ZW9pMjh3cDV6Mmp1MWU1OHg5enNrN2RqeHB4dyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/2aQS3AHfvvfIkSdbFM/giphy.gif);
  background-size: 120% 120%; /* Base size - will be scaled dynamically */
  background-position: center;
  mix-blend-mode: multiply;
  opacity: 0.15; /* Start with a subtle effect */
  filter: blur(0px) hue-rotate(0deg);
  z-index: 66; /* Above card-background but below content */
  pointer-events: none; /* Allow clicks to pass through */
  transition: opacity var(--transition-medium), transform 0.05s ease-out; /* Smooth transition for parallax movement */
  transform-origin: center; /* Ensure scaling happens from the center */
  will-change: transform, opacity;
}

/* Rainbow Sphere Light Source */
.rainbow-sphere {
  position: absolute;
  width: 350px;
  height: 450px;
  border-radius: 0%;
  pointer-events: none;
  z-index: 5;
  opacity: 0;
  transition: opacity var(--transition-medium);
  transform: translate(-50%, -50%);
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 0, 0, 0.75) 10%,
    rgba(255, 165, 0, 0.7) 20%,
    rgba(255, 255, 0, 0.65) 30%,
    rgba(0, 255, 0, 0.65) 40%,
    rgba(0, 255, 255, 0.6) 50%,
    rgba(0, 0, 255, 0.5) 60%,
    rgba(128, 0, 128, 0.4) 70%,
    rgba(0, 0, 0, 0) 80%
  );
  mix-blend-mode: normal;
  filter: blur(55px) contrast(1.8) saturate(1);
  box-shadow: 0 0 30px 10px rgba(255, 255, 255, 0.3);
  will-change: opacity, transform, filter;
}

/* Glitter Reveal Layer */
.glitter-reveal {
  position: absolute;
  inset: 0;
  background-image: url('../../assets/images/glitter.png');
  background-size: 120% 120%;
  background-position: center;
  opacity: 0;
  z-index: 6;
  pointer-events: none;
  mix-blend-mode: screen;
  filter: contrast(1) brightness(1) hue-rotate(0deg);
  mask-image: radial-gradient(circle at 50% 50%, black 0%, transparent 70%);
  -webkit-mask-image: radial-gradient(circle at 50% 50%, black 0%, transparent 70%);
  animation: shimmer 3s infinite linear;
  will-change: opacity, filter, background-position;
}

/* VMAX Background Reveal Layer */
.vmaxbg-reveal {
  position: absolute;
  inset: 0;
  background-image: url('../../assets/images/vmaxbg.jpg');
  background-size: 50% 50%;
  background-position: center;
  opacity: 0;
  z-index: 4;
  pointer-events: none;
  mix-blend-mode: overlay;
  filter: contrast(1) brightness(1) hue-rotate(0deg);
  mask-image: radial-gradient(circle at 50% 50%, black 0%, transparent 70%);
  -webkit-mask-image: radial-gradient(circle at 50% 50%, black 0%, transparent 70%);
  animation: shimmer 3s infinite linear;
}

/* Galaxy Reveal Layer */
.galaxy-reveal {
  position: absolute;
  inset: 0;
  background-image: url('../../assets/images/cosmos-middle-trans.png');
  background-size: 70% 70%;
  background-position: center;
  opacity: 0;
  z-index: 6;
  pointer-events: none;
  mix-blend-mode: overlay;
  filter: contrast(1) brightness(1) hue-rotate(0deg);
  mask-image: radial-gradient(circle at 50% 50%, black 0%, transparent 60%);
  -webkit-mask-image: radial-gradient(circle at 50% 50%, black 0%, transparent 60%);
  animation: shimmer 3s infinite linear;
}

/* Adjustment layer for color blending */
.adjustment-layer {
  position: absolute;
  inset: 0;
  background-color: var(--color-primary-lighter);
  mix-blend-mode: normal;
  opacity: 1;
  z-index: 1;
  pointer-events: none;
  transition: opacity var(--transition-medium), background-color var(--transition-medium), mix-blend-mode var(--transition-medium);
}

.card:hover .adjustment-layer {
  background-color: hsl(39, 100%, 55%);
  opacity: 1;
  mix-blend-mode: overlay;
}

/* Shimmer animation for reveal effects */
@keyframes shimmer {
  0% {
    filter: opacity(0.7) contrast(1.5) brightness(1.5) hue-rotate(0deg);
  }
  50% {
    filter: opacity(1) contrast(1.8) brightness(1.8) hue-rotate(30deg);
  }
  100% {
    filter: opacity(0.7) contrast(1.5) brightness(1.5) hue-rotate(0deg);
  }
}