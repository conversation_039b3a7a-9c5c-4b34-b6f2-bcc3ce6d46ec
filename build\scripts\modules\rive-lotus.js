// Define the path to the WebAssembly files
window.RIVE_WASM_URL = 'scripts/vendor/rive/rive.wasm';
window.RIVE_WASM_FALLBACK_URL = 'scripts/vendor/rive/rive_fallback.wasm';


/**
 * Rive Animation Module
 * Handles initialization, animation states, and responsive behavior for the Rive animation
 */
const RiveAnimationModule = {
    // DOM element references
    elements: {
        canvasContainer: null,
        relaxationText: null,
        serviceSection: null,
        animationContainer: null,
        relaxationLevelText: null,
        canvas: null
    },

    // Animation state
    animation: {
        instance: null,
        inputs: null,
        isLoaded: false
    },

    // Configuration constants
    config: {
        mobileBreakpoint: 640,
        animationFile: "/assets/images/lotus.riv",
        stateMachine: "main_state",
        thresholds: {
            calm: 30,
            relaxed: 60,
            blissful: 90
        },
        delays: {
            introAnimation: 1200,
            textFadeIn: 200,
            serviceReveal: {
                mobile: 650,
                desktop: 800
            },
            positionTransition: 30
        }
    },

    /**
     * Initialize the Rive animation module
     */
    init: function() {
        this.cacheElements();
        this.setupInitialState();
        this.loadAnimation();
    },

    /**
     * Cache DOM element references
     */
    cacheElements: function() {
        this.elements.canvasContainer = document.getElementById('rive-canvas-container');
        this.elements.relaxationText = document.getElementById('relaxation-text');
        this.elements.serviceSection = document.getElementById('service-section');
        this.elements.animationContainer = document.getElementById('animation-container');
        this.elements.relaxationLevelText = document.getElementById('relaxation-level-text');
        this.elements.canvas = document.getElementById('canvas');
    },

    /**
     * Set up initial animation state
     */
    setupInitialState: function() {
        // Start with the text hidden and Rive centered
        this.elements.canvasContainer.classList.add('centered');
        this.elements.relaxationText.classList.add('hidden-text');

        // Hide the person and service sections initially for intro animation
        document.getElementById('person-section').classList.add('hidden-section');
        this.elements.serviceSection.classList.add('hidden-section');

        // Check if we're on mobile
        const isMobile = this.isMobileDevice();

        // Set appropriate initial styles based on device
        if (isMobile) {
            this.applyMobileLayout();
        }

        // Add a class for intro animation
        this.elements.animationContainer.classList.add('intro-animation-active');
    },

    /**
     * Check if the current device is mobile based on screen width
     * @returns {boolean} True if the device is mobile
     */
    isMobileDevice: function() {
        return window.innerWidth < this.config.mobileBreakpoint;
    },

    /**
     * Apply mobile-specific layout styles
     */
    applyMobileLayout: function() {
        // For mobile, ensure the container is ready for column layout
        this.elements.animationContainer.style.flexDirection = 'column';

        // Ensure proper positioning for mobile
        this.elements.canvasContainer.style.position = 'relative';
        this.elements.canvasContainer.style.left = 'auto';
        this.elements.canvasContainer.style.transform = 'none';
    },

    /**
     * Load the Rive animation
     */
    loadAnimation: function() {
        if (typeof rive === 'undefined') {
            this.handleAnimationError('Rive library not loaded properly');
            return;
        }

        try {
            const riveInstance = new rive.Rive({
                src: this.config.animationFile,
                canvas: this.elements.canvas,
                autoplay: true,
                stateMachines: this.config.stateMachine,
                onLoad: () => this.handleAnimationLoaded(riveInstance),
                onError: (err) => this.handleAnimationError(err)
            });
        } catch (error) {
            this.handleAnimationError(error);
        }
    },

    /**
     * Handle successful animation loading
     * @param {Object} riveInstance - The Rive animation instance
     */
    handleAnimationLoaded: function(riveInstance) {
        riveInstance.resizeDrawingSurfaceToCanvas();

        // Store animation inputs for state changes
        const inputs = riveInstance.stateMachineInputs(this.config.stateMachine);

        this.animation.instance = riveInstance;
        this.animation.inputs = {
            calm: inputs.find(i => i.name === 'b_calm'),
            relaxed: inputs.find(i => i.name === 'b_relaxed'),
            blissful: inputs.find(i => i.name === 'b_blissful'),
            zen: inputs.find(i => i.name === 'b_zen')
        };

        this.animation.isLoaded = true;

        // Start with a lower relaxation level
        this.updateRelaxationState(0);

        // Begin intro animation sequence
        this.startIntroAnimation();
    },

    /**
     * Handle animation loading errors
     * @param {Error} error - The error that occurred
     */
    handleAnimationError: function(error) {
        console.error('Error with Rive animation:', error);

        // Show a fallback message in the animation container
        const fallbackMessage = document.createElement('div');
        fallbackMessage.className = 'animation-error';
        fallbackMessage.textContent = 'Animation could not be loaded';
        fallbackMessage.style.color = '#555';
        fallbackMessage.style.textAlign = 'center';
        fallbackMessage.style.padding = '2rem';
        fallbackMessage.style.fontFamily = "'Poppins', sans-serif";

        // Replace canvas with error message
        if (this.elements.canvasContainer) {
            this.elements.canvasContainer.innerHTML = '';
            this.elements.canvasContainer.appendChild(fallbackMessage);
        }

        // Continue with the UI flow despite the animation error
        this.elements.serviceSection.classList.remove('hidden-section');
    },

    /**
     * Start the intro animation sequence
     */
    startIntroAnimation: function() {
        setTimeout(() => {
            const isMobile = this.isMobileDevice();

            if (isMobile) {
                this.playMobileIntroAnimation();
            } else {
                this.playDesktopIntroAnimation();
            }

            // Set up responsive behavior
            this.setupResponsiveHandlers();
        }, this.config.delays.introAnimation);
    },

    /**
     * Play the mobile version of the intro animation
     */
    playMobileIntroAnimation: function() {
        // For mobile, ensure proper positioning first
        this.applyMobileLayout();

        // Remove centered class
        this.elements.canvasContainer.classList.remove('centered');

        // Show the text with a fade-in
        setTimeout(() => {
            // First make the text container visible but still transparent
            this.elements.relaxationText.style.display = 'flex';

            // For mobile, ensure text is properly centered
            this.elements.relaxationText.style.alignItems = 'center';
            this.elements.relaxationText.style.textAlign = 'center';

            // Force a reflow to ensure the display change takes effect
            void this.elements.relaxationText.offsetHeight;

            // Then remove the hidden class to trigger the fade-in
            this.elements.relaxationText.classList.remove('hidden-text');
            this.elements.animationContainer.classList.remove('intro-animation-active');

            // After relaxation meter animation completes, animate in the service section
            setTimeout(() => {
                this.revealServiceSection();
            }, this.config.delays.serviceReveal.mobile);
        }, this.config.delays.textFadeIn);
    },

    /**
     * Play the desktop version of the intro animation
     */
    playDesktopIntroAnimation: function() {
        // For desktop, do the full animation
        // First ensure it's properly positioned in the center
        this.elements.canvasContainer.style.position = 'absolute';
        this.elements.canvasContainer.style.left = '50%';
        this.elements.canvasContainer.style.transform = 'translateX(-50%)';

        // Force a reflow to ensure the browser recognizes the centered position
        void this.elements.canvasContainer.offsetWidth;

        // Then transition to the final position
        setTimeout(() => {
            // Remove the centered class
            this.elements.canvasContainer.classList.remove('centered');

            // Reset all positioning to match the original layout
            this.elements.canvasContainer.style.position = 'relative';
            this.elements.canvasContainer.style.left = '0px';
            this.elements.canvasContainer.style.transform = 'translateX(0%)';

            // After a small additional delay, show the text
            setTimeout(() => {
                // First make the text container visible but still transparent
                this.elements.relaxationText.style.display = 'flex';

                // For mobile, ensure text is properly centered
                if (this.isMobileDevice()) {
                    this.elements.relaxationText.style.alignItems = 'center';
                    this.elements.relaxationText.style.textAlign = 'center';
                }

                // Force a reflow to ensure the display change takes effect
                void this.elements.relaxationText.offsetHeight;

                // Then remove the hidden class to trigger the fade-in
                this.elements.relaxationText.classList.remove('hidden-text');

                // Remove the intro animation class to restore responsive behavior
                this.elements.animationContainer.classList.remove('intro-animation-active');

                // After relaxation meter animation completes, animate in the service section
                setTimeout(() => {
                    this.revealServiceSection();
                }, this.config.delays.serviceReveal.desktop);
            }, this.config.delays.textFadeIn);
        }, this.config.delays.positionTransition);
    },

    /**
     * Reveal the person section with animation
     */
    revealServiceSection: function() {
        // First make it visible but still transparent
        document.getElementById('person-section').style.display = 'block';

        // Force a reflow before removing the class to ensure the animation works
        void document.getElementById('person-section').offsetHeight;

        // Then remove the hidden class to trigger the fade-in animation
        document.getElementById('person-section').classList.remove('hidden-section');
    },

    /**
     * Set up event handlers for responsive behavior
     */
    setupResponsiveHandlers: function() {
        // Add window resize event listener to ensure responsive behavior
        window.addEventListener('resize', () => {
            // Check if we're on mobile
            const isMobile = this.isMobileDevice();

            // Only adjust if the intro animation is complete
            if (!this.elements.animationContainer.classList.contains('intro-animation-active')) {
                // Reset any inline styles that might interfere with responsiveness
                this.elements.canvasContainer.style.position = 'relative';
                this.elements.canvasContainer.style.left = '';
                this.elements.canvasContainer.style.transform = '';

                // For mobile, ensure column layout
                if (isMobile) {
                    this.elements.animationContainer.style.flexDirection = 'column';
                } else {
                    this.elements.animationContainer.style.flexDirection = 'row';
                }
            }
        });

        // Add window load event to handle page refresh properly
        window.addEventListener('load', () => {
            // Check if we're on mobile
            const isMobile = this.isMobileDevice();

            // If animation is already complete (page refresh case)
            if (!this.elements.animationContainer.classList.contains('intro-animation-active') &&
                !this.elements.relaxationText.classList.contains('hidden-text')) {

                // For mobile, ensure proper layout
                if (isMobile) {
                    // Force column layout
                    this.elements.animationContainer.style.flexDirection = 'column';

                    // Ensure proper positioning
                    this.elements.canvasContainer.style.position = 'relative';
                    this.elements.canvasContainer.style.left = 'auto';
                    this.elements.canvasContainer.style.transform = 'none';

                    // Make sure text is visible
                    this.elements.relaxationText.style.display = 'flex';

                    // Ensure text is properly centered on mobile
                    this.elements.relaxationText.style.alignItems = 'center';
                    this.elements.relaxationText.style.textAlign = 'center';
                }
            }
        });
    },

    /**
     * Update the relaxation animation state based on score
     * @param {number} score - The relaxation score (0-100)
     */
    updateRelaxationState: function(score) {
        if (!this.animation.isLoaded || !this.animation.inputs) return;

        // Reset all states
        if (this.animation.inputs.calm) this.animation.inputs.calm.value = false;
        if (this.animation.inputs.relaxed) this.animation.inputs.relaxed.value = false;
        if (this.animation.inputs.blissful) this.animation.inputs.blissful.value = false;
        if (this.animation.inputs.zen) this.animation.inputs.zen.value = false;

        // Set appropriate state based on score and update text
        if (score > 0) {
            if (score < this.config.thresholds.calm) {
                if (this.animation.inputs.calm) this.animation.inputs.calm.value = true;
                this.elements.relaxationLevelText.textContent = 'Calm';
            } else if (score < this.config.thresholds.relaxed) {
                if (this.animation.inputs.relaxed) this.animation.inputs.relaxed.value = true;
                this.elements.relaxationLevelText.textContent = 'Relaxed';
            } else if (score < this.config.thresholds.blissful) {
                if (this.animation.inputs.blissful) this.animation.inputs.blissful.value = true;
                this.elements.relaxationLevelText.textContent = 'Blissful';
            } else {
                if (this.animation.inputs.zen) this.animation.inputs.zen.value = true;
                this.elements.relaxationLevelText.textContent = 'Zen';
            }
        } else {
            this.elements.relaxationLevelText.textContent = 'Calm';
        }
    }
};

// Initialize the Rive animation when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    RiveAnimationModule.init();
});

// Expose the updateRelaxationAnimation function for external use
function updateRelaxationAnimation(score) {
    RiveAnimationModule.updateRelaxationState(score);
}