{"version": 3, "file": "rive.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;;;;;;;;;;;ACTA;AACA;AACA;AACA;AACA,uBAAuB;;AAEvB;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,sBAAsB,eAAe;AACpG;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG,EAAE;AACL,yDAAyD;AACzD;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,KAAK;AACL;AACA,GAAG,EAAE;AACL,mEAAmE;AACnE;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,EAAE;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD;AACrD;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,UAAU;AACV;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA,YAAY;AACZ;AACA,OAAO;AACP;AACA;AACA,UAAU;AACV;AACA;AACA,OAAO,EAAE;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD,oCAAoC,mCAAmC,yCAAyC,kCAAkC,+BAA+B,8BAA8B,+EAA+E,mBAAmB;AACpW,qDAAqD,0CAA0C,kCAAkC,+BAA+B,0DAA0D,mBAAmB;AAC7O;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,sBAAsB,UAAU,IAAI;AACzF;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,8CAA8C,kBAAkB;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,EAAE,qCAAqC;AAC1C;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,GAAG,EAAE,sCAAsC;AAC3C;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,GAAG;AACH,cAAc;AACd,GAAG;AACH,cAAc;AACd,GAAG;AACH,oBAAoB,iBAAiB;AACrC,GAAG;AACH,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,OAAO;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,EAAE;AACL;AACA;AACA,oDAAoD;AACpD;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH;AACA,oBAAoB,OAAO;AAC3B;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,4MAA4M;AAC1N;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH,GAAG;AACH;AACA,GAAG,EAAE;AACL;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA,KAAK,EAAE;AACP;AACA;AACA,YAAY,IAAI;AAChB;AACA,qBAAqB;AACrB;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA,kBAAkB;AAClB;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK,EAAE;AACP,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,0BAA0B;AACjD;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,iKAAiK,0BAA0B;AAC3L;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,SAAS,aAAa;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,EAAE;AACpC;AACA;AACA;AACA,UAAU;AACV;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,cAAc;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,oDAAoD,UAAU,IAAI,aAAa;AAC/E;AACA;AACA;AACA;AACA;AACA,mBAAmB,KAAK;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,IAAI;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,8BAA8B,GAAG,SAAS,EAAE;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,2BAA2B,IAAI;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA,oBAAoB,cAAc;AAClC;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,EAAE;AAClD;AACA;AACA,wBAAwB;AACxB;AACA,kBAAkB,EAAE;AACpB;AACA;AACA;AACA;AACA,+BAA+B,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gEAAgE,EAAE,gDAAgD,iBAAiB,sBAAsB,OAAO;AAChK;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0HAA0H,EAAE,wIAAwI,EAAE;AACtQ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ;AACjB,6CAA6C,OAAO,uBAAuB,OAAO;AAClF;AACA;AACA;AACA;AACA;AACA,+CAA+C,UAAU;AACzD;AACA,2BAA2B,MAAM,SAAS,UAAU;AACpD,gEAAgE,UAAU;AAC1E;AACA;AACA;AACA;AACA,wCAAwC,UAAU;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,MAAM,SAAS,UAAU;AACpD,gEAAgE,UAAU;AAC1E,6DAA6D,iCAAiC,oBAAoB,UAAU;AAC5H;AACA;AACA;AACA;AACA,0EAA0E,iCAAiC,oBAAoB,UAAU;AACzI;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,UAAU;AACzD;AACA,2BAA2B,MAAM,SAAS,UAAU;AACpD,gEAAgE,UAAU;AAC1E,kDAAkD,YAAY,oBAAoB,UAAU;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yEAAyE,EAAE,IAAI,EAAE;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,EAAE;AACpB;AACA;AACA;AACA;AACA;AACA,cAAc,cAAc;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,GAAG,cAAc,kBAAkB,sBAAsB,GAAG;AACxG;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,OAAO;AAC3B;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,8BAA8B,cAAc;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,OAAO;AACjC;AACA;AACA;AACA;AACA;AACA,8BAA8B,GAAG,uBAAuB,EAAE;AAC1D,uCAAuC,GAAG,mCAAmC,mBAAmB;AAChG,sDAAsD,GAAG;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,cAAc;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,yBAAyB,cAAc;AACvC;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,cAAc,kBAAkB;AAChC;AACA;AACA;AACA;AACA;AACA,eAAe,MAAM;AACrB;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,0BAA0B,mBAAmB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,eAAe;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,OAAO;AACzB;AACA;AACA;AACA;AACA,CAAC;AACD,0BAA0B,cAAc;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,cAAc;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,yBAAyB,cAAc;AACvC;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,OAAO;AACvC;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA,aAAa,kOAAkO;AAC/O;AACA;AACA;AACA;AACA;AACA,gBAAgB,EAAE,GAAG,KAAK;AAC1B;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4DAA4D,aAAa;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,MAAM;AACrE;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,GAAG;AACH,2BAA2B,cAAc;AACzC;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,aAAa,GAAG,WAAW,GAAG,SAAS,GAAG,SAAS;AAC7D;AACA;AACA,4BAA4B,gBAAgB;AAC5C;AACA;AACA;AACA;AACA;AACA,kCAAkC,UAAU;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,IAAI;AACtB;AACA;AACA,SAAS,IAAI;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,GAAG,OAAO,yDAAyD,GAAG;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,6BAA6B,kBAAkB,aAAa;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD,uBAAuB,eAAe,UAAU;AACzG;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,GAAG;AACjD;AACA,KAAK;AACL,6CAA6C,QAAQ;AACrD;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,GAAG,SAAS;AAC/C;AACA;AACA;AACA,uEAAuE,EAAE;AACzE;AACA;AACA;AACA;AACA;AACA;AACA,0FAA0F,EAAE;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,KAAK;AACL,aAAa;AACb,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,iDAAiD,EAAE;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,kDAAkD;AACvD,GAAG;AACH,CAAC;AACD,CAAC;AACD;AACA;AACA,SAAS;AACT;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA,GAAG,UAAU;AACb,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,GAAG;AAC9B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,GAAG,qCAAqC,iBAAiB,gBAAgB,4BAA4B;AACrJ;AACA;AACA,KAAK;AACL,8BAA8B,aAAa,QAAQ,EAAE;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,wBAAwB,GAAG;AAC3B;AACA;AACA,eAAe,OAAO,GAAG,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA,eAAe,OAAO,GAAG,EAAE,QAAQ;AACnC,0BAA0B,GAAG;AAC7B,KAAK;AACL;AACA,0BAA0B,GAAG;AAC7B,MAAM;AACN,WAAW,GAAG;AACd;AACA;AACA;AACA;AACA,eAAe;AACf;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;AACL;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA,2BAA2B,OAAO;AAClC;AACA;AACA,iGAAiG,MAAM,eAAe,OAAO;AAC7H;AACA;AACA,6BAA6B,QAAQ;AACrC;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,wBAAwB,GAAG;AAC3B;AACA;AACA,eAAe,OAAO,GAAG,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA,eAAe,OAAO,GAAG,EAAE,QAAQ;AACnC,0BAA0B,GAAG;AAC7B,KAAK;AACL;AACA,0BAA0B,GAAG;AAC7B,MAAM;AACN;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;AACH,CAAC;AACD;AACA,SAAS;AACT;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG,qDAAqD;AACxD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,GAAG;AACH;AACA,GAAG,8DAA8D;AACjE;AACA,CAAC;AACD;AACA;AACA;AACA,8CAA8C,OAAO,QAAQ,eAAe,YAAY,OAAO,GAAG,EAAE;AACpG,GAAG,EAAE,EAAE;AACP;AACA;AACA,CAAC;AACD;AACA;AACA,SAAS;AACT;AACA,GAAG;AACH;AACA,GAAG,2DAA2D;AAC9D,CAAC;AACD;AACA;AACA;AACA;AACA,sBAAsB,GAAG;AACzB,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,SAAS,wGAAwG;AACjH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,kEAAkE,GAAG,OAAO;AACrF,CAAC;AACD;AACA;AACA,SAAS;AACT;AACA;AACA,6BAA6B,QAAQ;AACrC;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,kBAAkB,OAAO;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,oBAAoB,OAAO;AAC3B;AACA;AACA;AACA;AACA,QAAQ;AACR,oBAAoB,OAAO;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG,EAAE;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA,SAAS;AACT,0DAA0D,QAAQ;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,2EAA2E,EAAE;AAC7E;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG,EAAE;AACL,CAAC;AACD,WAAW;AACX,CAAC;AACD,iBAAiB,yDAAyD;AAC1E,CAAC;AACD;AACA,SAAS;AACT,GAAG;AACH,GAAG,EAAE;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,WAAW;AACtC;AACA;AACA;AACA,gBAAgB,WAAW;AAC3B;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD,aAAa;AACb,CAAC;AACD;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,kBAAkB,QAAQ;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,gBAAgB,cAAc;AAC9B;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,CAAC;AACD;AACA,CAAC;AACD,yBAAyB,OAAO;AAChC;AACA;AACA,oBAAoB,OAAO;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0EAA0E,iBAAiB;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,gBAAgB;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA;AACA;AACA,oEAAoE,qBAAqB;AACzF;AACA;AACA;AACA;;;;AAIA;AACA;;AAEA;AACA,CAAC;AACD,iEAAe,IAAI;;;;;;;;;;;;;;;;;ACtsEqB;;;;;;;;;;;ACOxC;;;;;;;;;;GAUG;AACH;IAUE;;;;;OAKG;IACH,mBACU,SAA0B,EAC1B,QAAkB,EAC1B,OAAmB,EACZ,OAAgB;QAHf,cAAS,GAAT,SAAS,CAAiB;QAC1B,aAAQ,GAAR,QAAQ,CAAU;QAEnB,YAAO,GAAP,OAAO,CAAS;QAnBlB,cAAS,GAAG,CAAC,CAAC;QAGrB;;;WAGG;QACI,YAAO,GAAkB,IAAI,CAAC;QAcnC,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,uBAAuB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC3E,CAAC;IAKD,sBAAW,2BAAI;QAHf;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAC7B,CAAC;;;OAAA;IAKD,sBAAW,2BAAI;QAHf;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC5B,CAAC;QAED;;WAEG;aACH,UAAgB,KAAa;YAC3B,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC;QAC7B,CAAC;;;OAPA;IAYD,sBAAW,gCAAS;QAHpB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;QAClC,CAAC;;;OAAA;IAMD,sBAAW,iCAAU;QAJrB;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC;QAC/B,CAAC;;;OAAA;IAED;;;;OAIG;IACI,2BAAO,GAAd,UAAe,IAAY;QACzB,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;YAC1B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACI,yBAAK,GAAZ,UAAa,GAAW;QACtB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,2BAAO,GAAd;QACE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;IACzB,CAAC;IACH,gBAAC;AAAD,CAAC;;;;;;;;;;;;;;;;AChHuE;AACjB;;;;;;;;;;;ACDvD,sBA0OA;AAvNA;;;;;;GAMG;AACH,IAAM,oBAAoB,GAAG,UAC3B,KAA8B,EAC9B,oBAA6B;;IAE7B,IACE,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACpD,MAAC,KAAoB,CAAC,OAAO,0CAAE,MAAM,GACrC,CAAC;QACD,4EAA4E;QAC5E,0EAA0E;QAC1E,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC1B,KAAK,CAAC,cAAc,EAAE,CAAC;QACzB,CAAC;QACD,OAAO;YACL,OAAO,EAAG,KAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO;YACjD,OAAO,EAAG,KAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO;SAClD,CAAC;IACJ,CAAC;SAAM,IACL,KAAK,CAAC,IAAI,KAAK,UAAU;SACzB,MAAC,KAAoB,CAAC,cAAc,0CAAE,MAAM,GAC5C,CAAC;QACD,OAAO;YACL,OAAO,EAAG,KAAoB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO;YACxD,OAAO,EAAG,KAAoB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO;SACzD,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,OAAO;YACL,OAAO,EAAG,KAAoB,CAAC,OAAO;YACtC,OAAO,EAAG,KAAoB,CAAC,OAAO;SACvC,CAAC;IACJ,CAAC;AACH,CAAC,CAAC;AAEF;;;GAGG;AACI,IAAM,yBAAyB,GAAG,UAAC,EAUhB;QATxB,MAAM,cACN,QAAQ,gBACR,qBAAkB,EAAlB,aAAa,mBAAG,EAAE,OAClB,QAAQ,gBACR,IAAI,YACJ,GAAG,WACH,SAAS,iBACT,4BAA4B,EAA5B,oBAAoB,mBAAG,KAAK,OAC5B,yBAAuB,EAAvB,iBAAiB,mBAAG,GAAG;IAEvB,IACE,CAAC,MAAM;QACP,CAAC,aAAa,CAAC,MAAM;QACrB,CAAC,QAAQ;QACT,CAAC,IAAI;QACL,CAAC,QAAQ;QACT,OAAO,MAAM,KAAK,WAAW,EAC7B,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACD;;;;;;;;;;;;;QAaI;IACJ,IAAI,cAAc,GAAkB,IAAI,CAAC;IACzC,IAAI,sBAAsB,GAAG,KAAK,CAAC;IAEnC,IAAM,oBAAoB,GAAG,UAAC,KAA8B;QAC1D,+CAA+C;QAC/C,uGAAuG;QACvG,wFAAwF;QACxF,IAAI,sBAAsB,IAAI,KAAK,YAAY,UAAU,EAAE,CAAC;YAC1D,2BAA2B;YAC3B,IAAI,KAAK,CAAC,IAAI,IAAI,SAAS,EAAE,CAAC;gBAC5B,sBAAsB,GAAG,KAAK,CAAC;YACjC,CAAC;YAED,OAAO;QACT,CAAC;QAED,qEAAqE;QACrE,0BAA0B;QAC1B,sBAAsB;YACpB,oBAAoB;gBACpB,KAAK,CAAC,IAAI,KAAK,UAAU;gBACzB,cAAc,KAAK,YAAY,CAAC;QAElC,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC;QAE5B,IAAM,YAAY,GAChB,KAAK,CAAC,aACP,CAAC,qBAAqB,EAAE,CAAC;QAEpB,SAAuB,oBAAoB,CAC/C,KAAK,EACL,oBAAoB,CACrB,EAHO,OAAO,eAAE,OAAO,aAGvB,CAAC;QACF,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACzB,OAAO;QACT,CAAC;QACD,IAAM,OAAO,GAAG,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC;QAC5C,IAAM,OAAO,GAAG,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC;QAC3C,IAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CACzC,GAAG,EACH,SAAS,EACT;YACE,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,YAAY,CAAC,KAAK;YACxB,IAAI,EAAE,YAAY,CAAC,MAAM;SAC1B,EACD,QAAQ,CAAC,MAAM,EACf,iBAAiB,CAClB,CAAC;QACF,IAAM,cAAc,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;QACxC,aAAa,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QACrC,IAAM,uBAAuB,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACjE,IAAM,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAClC,cAAc,EACd,uBAAuB,CACxB,CAAC;QACF,IAAM,YAAY,GAAG,iBAAiB,CAAC,CAAC,EAAE,CAAC;QAC3C,IAAM,YAAY,GAAG,iBAAiB,CAAC,CAAC,EAAE,CAAC;QAE3C,iBAAiB,CAAC,MAAM,EAAE,CAAC;QAC3B,cAAc,CAAC,MAAM,EAAE,CAAC;QACxB,uBAAuB,CAAC,MAAM,EAAE,CAAC;QACjC,aAAa,CAAC,MAAM,EAAE,CAAC;QAEvB,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB;;;;;;;;;;;eAWG;YACH,KAAK,UAAU;gBACb,KAA2B,UAAa,EAAb,+BAAa,EAAb,2BAAa,EAAb,IAAa,EAAE,CAAC;oBAAtC,IAAM,YAAY;oBACrB,YAAY,CAAC,WAAW,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;gBACvD,CAAC;gBACD,MAAM;YAER,wCAAwC;YACxC,KAAK,WAAW,CAAC;YACjB,KAAK,WAAW,CAAC;YACjB,KAAK,WAAW,CAAC,CAAC,CAAC;gBACjB,KAA2B,UAAa,EAAb,+BAAa,EAAb,2BAAa,EAAb,IAAa,EAAE,CAAC;oBAAtC,IAAM,YAAY;oBACrB,YAAY,CAAC,WAAW,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;gBACvD,CAAC;gBACD,MAAM;YACR,CAAC;YACD,6DAA6D;YAC7D,KAAK,YAAY,CAAC;YAClB,KAAK,WAAW,CAAC,CAAC,CAAC;gBACjB,KAA2B,UAAa,EAAb,+BAAa,EAAb,2BAAa,EAAb,IAAa,EAAE,CAAC;oBAAtC,IAAM,YAAY;oBACrB,YAAY,CAAC,WAAW,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;gBACvD,CAAC;gBACD,MAAM;YACR,CAAC;YACD,uCAAuC;YACvC,KAAK,UAAU,CAAC;YAChB,KAAK,SAAS,CAAC,CAAC,CAAC;gBACf,KAA2B,UAAa,EAAb,+BAAa,EAAb,2BAAa,EAAb,IAAa,EAAE,CAAC;oBAAtC,IAAM,YAAY;oBACrB,YAAY,CAAC,SAAS,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;gBACrD,CAAC;gBACD,MAAM;YACR,CAAC;YACD,QAAQ;QACV,CAAC;IACH,CAAC,CAAC;IACF,IAAM,QAAQ,GAAG,oBAAoB,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;IACjD,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IAC/C,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAC9C,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IAC/C,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IAC/C,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC7C,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,EAAE;QAC7C,OAAO,EAAE,oBAAoB;KAC9B,CAAC,CAAC;IACH,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,QAAQ,EAAE;QAC9C,OAAO,EAAE,oBAAoB;KAC9B,CAAC,CAAC;IACH,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAC9C,OAAO;QACL,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAClD,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QACjD,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAClD,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAClD,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAChD,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAClD,MAAM,CAAC,mBAAmB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QACnD,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACnD,CAAC,CAAC;AACJ,CAAC,CAAC;;;;;;;;;;;;ACzOF,iEAAiE;AACjE,IAAM,oBAAoB,GAAG,uCAAuC,CAAC;AACrE,IAAM,iBAAiB,GAAG,kBAAkB,CAAC;AAC7C,IAAM,mBAAmB,GAAG,mBAAmB,CAAC;AAChD,IAAM,mBAAmB,GACvB,oDAAoD,CAAC;AACvD,IAAM,cAAc,GAAG,mBAAmB,CAAC;AAC3C,IAAM,uBAAuB,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACpC,IAAM,SAAS,GAAG,aAAa,CAAC;AAEvC,SAAS,4BAA4B,CAAC,GAAW;IAC/C,OAAO,uBAAuB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACtD,CAAC;AAED,4DAA4D;AAC5D,SAAS,oBAAoB,CAAC,GAAW;IACvC,IAAM,eAAe,GAAG,GAAG,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;IAC7D,OAAO,eAAe,CAAC,OAAO,CAAC,iBAAiB,EAAE,UAAC,KAAK,EAAE,GAAG;QAC3D,OAAO,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC;AACL,CAAC;AAEM,SAAS,WAAW,CAAC,GAAY;IACtC,IAAI,CAAC,GAAG,EAAE,CAAC;QACT,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAM,YAAY,GAAG,oBAAoB,CAAC,GAAG,CAAC;SAC3C,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC;SAChC,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC;SAChC,IAAI,EAAE,CAAC;IAEV,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,4BAA4B,CAAC,YAAY,CAAC,EAAE,CAAC;QAC/C,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,IAAM,qBAAqB,GAAG,YAAY,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IAEjE,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC3B,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,IAAM,SAAS,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAAC;IAE3C,IAAI,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;QACzC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO,YAAY,CAAC;AACtB,CAAC;;;;;;UCrDD;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACN0C;AACH;AACC;AACoC;AAE5E;IAAwB,6BAAK;IAA7B;;QACS,oBAAc,GAAG,IAAI,CAAC;;IAC/B,CAAC;IAAD,gBAAC;AAAD,CAAC,CAFuB,KAAK,GAE5B;AA+BD,mBAAmB;AACnB,IAAM,mBAAmB,GAAG,UAAC,KAAU;IACrC,YAAK,IAAI,KAAK,CAAC,cAAc;QAC3B,CAAC,CAAC,KAAK,CAAC,OAAO;QACf,CAAC,CAAC,uCAAuC;AAF3C,CAE2C,CAAC;AAE9C,iBAAiB;AAEjB,6BAA6B;AAC7B,IAAY,GASX;AATD,WAAY,GAAG;IACb,sBAAe;IACf,0BAAmB;IACnB,oBAAa;IACb,4BAAqB;IACrB,8BAAuB;IACvB,oBAAa;IACb,8BAAuB;IACvB,wBAAiB;AACnB,CAAC,EATW,GAAG,KAAH,GAAG,QASd;AAED,mCAAmC;AACnC,IAAY,SAUX;AAVD,WAAY,SAAS;IACnB,8BAAiB;IACjB,gCAAmB;IACnB,oCAAuB;IACvB,kCAAqB;IACrB,sCAAyB;IACzB,wCAA2B;IAC3B,sCAAyB;IACzB,0CAA6B;IAC7B,wCAA2B;AAC7B,CAAC,EAVW,SAAS,KAAT,SAAS,QAUpB;AAaD,yDAAyD;AACzD;IAcE,gBAAY,MAAyB;;QACnC,IAAI,CAAC,GAAG,GAAG,YAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,GAAG,mCAAI,GAAG,CAAC,OAAO,CAAC;QACtC,IAAI,CAAC,SAAS,GAAG,YAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,SAAS,mCAAI,SAAS,CAAC,MAAM,CAAC;QACvD,IAAI,CAAC,iBAAiB,GAAG,YAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,iBAAiB,mCAAI,CAAC,CAAC;QACxD,IAAI,CAAC,IAAI,GAAG,YAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,mCAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,YAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,mCAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,YAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,mCAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,YAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,mCAAI,CAAC,CAAC;IAChC,CAAC;IAED,qEAAqE;IAC9D,UAAG,GAAV,UAAW,EAOQ;YANjB,GAAG,WACH,SAAS,iBACT,IAAI,YACJ,IAAI,YACJ,IAAI,YACJ,IAAI;QAEJ,OAAO,CAAC,IAAI,CACV,kEAAkE,CACnE,CAAC;QACF,OAAO,IAAI,MAAM,CAAC,EAAE,GAAG,OAAE,SAAS,aAAE,IAAI,QAAE,IAAI,QAAE,IAAI,QAAE,IAAI,QAAE,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACI,yBAAQ,GAAf,UAAgB,EAQG;YAPjB,GAAG,WACH,SAAS,iBACT,iBAAiB,yBACjB,IAAI,YACJ,IAAI,YACJ,IAAI,YACJ,IAAI;QAEJ,OAAO,IAAI,MAAM,CAAC;YAChB,GAAG,EAAE,GAAG,aAAH,GAAG,cAAH,GAAG,GAAI,IAAI,CAAC,GAAG;YACpB,SAAS,EAAE,SAAS,aAAT,SAAS,cAAT,SAAS,GAAI,IAAI,CAAC,SAAS;YACtC,iBAAiB,EAAE,iBAAiB,aAAjB,iBAAiB,cAAjB,iBAAiB,GAAI,IAAI,CAAC,iBAAiB;YAC9D,IAAI,EAAE,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,IAAI,CAAC,IAAI;YACvB,IAAI,EAAE,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,IAAI,CAAC,IAAI;YACvB,IAAI,EAAE,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,IAAI,CAAC,IAAI;YACvB,IAAI,EAAE,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,IAAI,CAAC,IAAI;SACxB,CAAC,CAAC;IACL,CAAC;IAED,0CAA0C;IACnC,2BAAU,GAAjB,UAAkB,IAAmB;QACnC,IAAI,IAAI,CAAC,gBAAgB;YAAE,OAAO,IAAI,CAAC,gBAAgB,CAAC;QAExD,IAAI,GAAG,CAAC;QACR,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;YAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;aAC5C,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO;YAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;aACrD,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI;YAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;aAC/C,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,QAAQ;YAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;aACvD,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,SAAS;YAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;aACzD,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,SAAS;YAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;aACzD,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM;YAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;;YACnD,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;QAEzB,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC;QAC5B,OAAO,GAAG,CAAC;IACb,CAAC;IAED,gDAAgD;IACzC,iCAAgB,GAAvB,UAAwB,IAAmB;QACzC,IAAI,IAAI,CAAC,sBAAsB;YAAE,OAAO,IAAI,CAAC,sBAAsB,CAAC;QAEpE,IAAI,SAAS,CAAC;QACd,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,OAAO;YACtC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;aAChC,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS;YAC7C,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;aAClC,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,QAAQ;YAC5C,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;aACjC,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,UAAU;YAC9C,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;aACnC,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,WAAW;YAC/C,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;aACpC,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,UAAU;YAC9C,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;aACnC,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,YAAY;YAChD,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;aACrC,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,WAAW;YAC/C,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;;YACpC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QAEvC,IAAI,CAAC,sBAAsB,GAAG,SAAS,CAAC;QACxC,OAAO,SAAS,CAAC;IACnB,CAAC;IACH,aAAC;AAAD,CAAC;;AASD,4EAA4E;AAC5E,eAAe;AACf;IAaE,8BAA8B;IAC9B;IAAuB,CAAC;IAExB,oBAAoB;IACL,yBAAW,GAA1B;QACE,0DAAU,CAAC;YACT,oBAAoB;YACpB,UAAU,EAAE,cAAM,oBAAa,CAAC,OAAO,EAArB,CAAqB;SACxC,CAAC;aACC,IAAI,CAAC,UAAC,IAAmB;;YACxB,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC;YAC7B,yBAAyB;YACzB,OAAO,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9C,mBAAa,CAAC,aAAa,CAAC,KAAK,EAAE,0CAAG,aAAa,CAAC,OAAO,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC,CAAC;aACD,KAAK,CAAC,UAAC,KAAK;YACX,iCAAiC;YACjC,IAAM,YAAY,GAAG;gBACnB,OAAO,EAAE,MAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,KAAI,eAAe;gBAC1C,IAAI,EAAE,MAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,KAAI,OAAO;gBAC5B,oEAAoE;gBACpE,SAAS,EACP,KAAK,YAAY,WAAW,CAAC,YAAY;oBACzC,KAAK,YAAY,WAAW,CAAC,YAAY;gBAC3C,aAAa,EAAE,KAAK;aACrB,CAAC;YAEF,mCAAmC;YACnC,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,YAAY,CAAC,CAAC;YAE7D,wGAAwG;YACxG,uEAAuE;YACvE,0HAA0H;YAC1H,IAAM,iBAAiB,GAAG,uCAAgC,8CAAgB,cAAI,iDAAmB,wBAAqB,CAAC;YACvH,IAAI,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,iBAAiB,EAAE,CAAC;gBAC9D,OAAO,CAAC,IAAI,CACV,mCAA4B,aAAa,CAAC,OAAO,eAAK,YAAY,CAAC,OAAO,mCAAgC,CAC3G,CAAC;gBACF,aAAa,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;gBAC5C,aAAa,CAAC,WAAW,EAAE,CAAC;YAC9B,CAAC;iBAAM,CAAC;gBACN,IAAM,YAAY,GAAG;oBACnB,6CAAsC,aAAa,CAAC,OAAO,iBAAO,iBAAiB,MAAG;oBACtF,mBAAmB;oBACnB,8BAA8B;oBAC9B,oDAAoD;oBACpD,8CAA8C;oBAC9C,kBAAkB;oBAClB,kBAAW,YAAY,CAAC,IAAI,CAAE;oBAC9B,qBAAc,YAAY,CAAC,OAAO,CAAE;oBACpC,wCAAiC,YAAY,CAAC,SAAS,CAAE;oBACzD,gCAAgC;oBAChC,kCAAkC;oBAClC,yDAAyD;oBACzD,2CAA2C;iBAC5C,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEb,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC,CAAC,CAAC;IACP,CAAC;IAED,6CAA6C;IAC/B,yBAAW,GAAzB,UAA0B,QAAyB;QACjD,6CAA6C;QAC7C,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;YAC7B,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC;YAC/B,aAAa,CAAC,WAAW,EAAE,CAAC;QAC9B,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAC3B,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAED,4CAA4C;IAC9B,2BAAa,GAA3B;QACE,OAAO,IAAI,OAAO,CAAgB,UAAC,OAAO;YACxC,oBAAa,CAAC,WAAW,CAAC,UAAC,IAAmB,IAAW,cAAO,CAAC,IAAI,CAAC,EAAb,CAAa,CAAC;QAAvE,CAAuE,CACxE,CAAC;IACJ,CAAC;IAED,6BAA6B;IACf,wBAAU,GAAxB,UAAyB,GAAW;QAClC,aAAa,CAAC,OAAO,GAAG,GAAG,CAAC;IAC9B,CAAC;IAED,4BAA4B;IACd,wBAAU,GAAxB;QACE,OAAO,aAAa,CAAC,OAAO,CAAC;IAC/B,CAAC;IAtGD,sDAAsD;IACvC,uBAAS,GAAG,KAAK,CAAC;IACjC,+DAA+D;IAChD,2BAAa,GAAsB,EAAE,CAAC;IAGrD,8DAA8D;IAC9D,oDAAoD;IACrC,qBAAO,GAAG,4BAAqB,8CAAgB,cAAI,iDAAmB,eAAY,CAAC;IA+FpG,oBAAC;CAAA;AA1GyB;AA4G1B,aAAa;AAEb,yBAAyB;AAEzB,IAAY,qBAIX;AAJD,WAAY,qBAAqB;IAC/B,sEAAW;IACX,wEAAY;IACZ,wEAAY;AACd,CAAC,EAJW,qBAAqB,KAArB,qBAAqB,QAIhC;AAED;;GAEG;AACH;IACE,2BACkB,IAA2B,EACnC,YAAyB;QADjB,SAAI,GAAJ,IAAI,CAAuB;QACnC,iBAAY,GAAZ,YAAY,CAAa;IAChC,CAAC;IAKJ,sBAAW,mCAAI;QAHf;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;QAChC,CAAC;;;OAAA;IAKD,sBAAW,oCAAK;QAHhB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;QACjC,CAAC;QAED;;WAEG;aACH,UAAiB,KAAuB;YACtC,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;QAClC,CAAC;;;OAPA;IASD;;OAEG;IACI,gCAAI,GAAX;QACE,IAAI,IAAI,CAAC,IAAI,KAAK,qBAAqB,CAAC,OAAO,EAAE,CAAC;YAChD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;IAED;;OAEG;IACI,kCAAM,GAAb;QACE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC3B,CAAC;IACH,wBAAC;AAAD,CAAC;;AAED,IAAY,aAGX;AAHD,WAAY,aAAa;IACvB,yDAAa;IACb,yDAAa;AACf,CAAC,EAHW,aAAa,KAAb,aAAa,QAGxB;AAED;IAWE;;;;OAIG;IACH,sBACU,YAA6B,EACrC,OAAsB,EACf,OAAgB,EACf,QAAqB;QAHrB,iBAAY,GAAZ,YAAY,CAAiB;QAE9B,YAAO,GAAP,OAAO,CAAS;QACf,aAAQ,GAAR,QAAQ,CAAa;QAnB/B;;WAEG;QACa,WAAM,GAAwB,EAAE,CAAC;QAkB/C,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,oBAAoB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QACzE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;IAED,sBAAW,8BAAI;aAAf;YACE,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;QAChC,CAAC;;;OAAA;IAKD,sBAAW,uCAAa;QAHxB;;WAEG;aACH;YACE,IAAM,KAAK,GAAa,EAAE,CAAC;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3D,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;;;OAAA;IAED;;;OAGG;IACI,8BAAO,GAAd,UAAe,IAAY;QACzB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,sCAAe,GAAtB,UAAuB,IAAY;QACjC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAED;;;OAGG;IACI,yCAAkB,GAAzB;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;IAC5C,CAAC;IAED;;;;;;OAMG;IACH,sCAAe,GAAf,UAAgB,CAAS;QACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACK,iCAAU,GAAlB,UAAmB,OAAsB;QACvC,0DAA0D;QAC1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED;;;OAGG;IACK,sCAAe,GAAvB,UACE,KAAkB,EAClB,OAAsB;QAEtB,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACzC,OAAO,IAAI,iBAAiB,CAC1B,qBAAqB,CAAC,OAAO,EAC7B,KAAK,CAAC,MAAM,EAAE,CACf,CAAC;QACJ,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YAClD,OAAO,IAAI,iBAAiB,CAC1B,qBAAqB,CAAC,MAAM,EAC5B,KAAK,CAAC,QAAQ,EAAE,CACjB,CAAC;QACJ,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YACnD,OAAO,IAAI,iBAAiB,CAC1B,qBAAqB,CAAC,OAAO,EAC7B,KAAK,CAAC,SAAS,EAAE,CAClB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,8BAAO,GAAd;QACE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK;YACxB,KAAK,CAAC,MAAM,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;IACzB,CAAC;IAEM,4CAAqB,GAA5B,UAA6B,iBAAoC;QAC/D,IAAI,iBAAiB,CAAC,eAAe,IAAI,IAAI,EAAE,CAAC;YAC9C,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IACH,mBAAC;AAAD,CAAC;AAED,aAAa;AAEb,mBAAmB;AAEnB;;GAEG;AACH;IACE;;;;;;;OAOG;IACH,kBACU,OAAsB,EACtB,QAAqB,EACrB,YAA0B,EAClB,UAA4B,EAC5B,aAAkC;QADlC,4CAA4B;QAC5B,kDAAkC;QAJ1C,YAAO,GAAP,OAAO,CAAe;QACtB,aAAQ,GAAR,QAAQ,CAAa;QACrB,iBAAY,GAAZ,YAAY,CAAc;QAClB,eAAU,GAAV,UAAU,CAAkB;QAC5B,kBAAa,GAAb,aAAa,CAAqB;IACjD,CAAC;IAEJ;;;;;;OAMG;IACI,sBAAG,GAAV,UACE,WAA8B,EAC9B,OAAgB,EAChB,SAAgB;QAAhB,4CAAgB;QAEhB,WAAW,GAAG,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAC5C,oDAAoD;QACpD,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC,EAArB,CAAqB,CAAC,CAAC;YACtD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC,EAArB,CAAqB,CAAC,CAAC;QAC3D,CAAC;aAAM,CAAC;YACN,8DAA8D;YAC9D,IAAM,uBAAuB,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAC;YACnE,IAAM,qBAAqB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAC;YACpE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5C,IAAM,MAAM,GAAG,uBAAuB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/D,IAAM,MAAM,GAAG,qBAAqB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7D,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,EAAE,CAAC;oBAC/B,IAAI,MAAM,IAAI,CAAC,EAAE,CAAC;wBAChB,wCAAwC;wBACxC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC;oBAC5C,CAAC;yBAAM,CAAC;wBACN,4CAA4C;wBAC5C,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC;oBAC/C,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,yCAAyC;oBACzC,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3D,IAAI,IAAI,EAAE,CAAC;wBACT,IAAM,YAAY,GAAG,IAAI,iDAAS,CAChC,IAAI,EACJ,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,OAAO,EACZ,OAAO,CACR,CAAC;wBACF,qDAAqD;wBACrD,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;wBACxB,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;wBACxB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBACrC,CAAC;yBAAM,CAAC;wBACN,6CAA6C;wBAC7C,IAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5D,IAAI,EAAE,EAAE,CAAC;4BACP,IAAM,eAAe,GAAG,IAAI,YAAY,CACtC,EAAE,EACF,IAAI,CAAC,OAAO,EACZ,OAAO,EACP,IAAI,CAAC,QAAQ,CACd,CAAC;4BACF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;wBAC3C,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QACD,yCAAyC;QACzC,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;oBACrB,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,IAAI,EAAE,IAAI,CAAC,OAAO;iBACnB,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;oBACrB,IAAI,EAAE,SAAS,CAAC,KAAK;oBACrB,IAAI,EAAE,IAAI,CAAC,MAAM;iBAClB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IAC9C,CAAC;IAED;;;;OAIG;IACI,uCAAoB,GAA3B,UAA4B,WAAqB,EAAE,OAAgB;QACjE,8DAA8D;QAC9D,2EAA2E;QAC3E,kEAAkE;QAClE,wCAAwC;QACxC,IAAM,uBAAuB,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAC;QACnE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,IAAM,MAAM,GAAG,uBAAuB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,IAAI,MAAM,IAAI,CAAC,EAAE,CAAC;gBAChB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC;YAC5C,CAAC;iBAAM,CAAC;gBACN,yCAAyC;gBACzC,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3D,IAAI,IAAI,EAAE,CAAC;oBACT,IAAM,YAAY,GAAG,IAAI,iDAAS,CAChC,IAAI,EACJ,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,OAAO,EACZ,OAAO,CACR,CAAC;oBACF,qDAAqD;oBACrD,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBACxB,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBACxB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACrC,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,KAAK,CAAC,8BAAuB,WAAW,CAAC,CAAC,CAAC,gBAAa,CAAC,CAAC;gBACpE,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;OAIG;IACI,oCAAiB,GAAxB,UAAyB,WAAqB,EAAE,OAAgB;QAC9D,8DAA8D;QAC9D,2EAA2E;QAC3E,kEAAkE;QAClE,wCAAwC;QACxC,IAAM,0BAA0B,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAC;QACzE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,IAAM,MAAM,GAAG,0BAA0B,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YAClE,IAAI,MAAM,IAAI,CAAC,EAAE,CAAC;gBAChB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC;YAC/C,CAAC;iBAAM,CAAC;gBACN,6CAA6C;gBAC7C,IAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5D,IAAI,EAAE,EAAE,CAAC;oBACP,IAAM,eAAe,GAAG,IAAI,YAAY,CACtC,EAAE,EACF,IAAI,CAAC,OAAO,EACZ,OAAO,EACP,IAAI,CAAC,QAAQ,CACd,CAAC;oBACF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;oBACzC,IAAI,CAAC,OAAO,EAAE,CAAC;wBACb,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;oBACrC,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,IAAI,CAAC,kCAA2B,WAAW,CAAC,CAAC,CAAC,gBAAa,CAAC,CAAC;oBACrE,4FAA4F;oBAC5F,8CAA8C;oBAC9C,IAAI,CAAC,oBAAoB,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;gBACvD,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;OAIG;IACI,uBAAI,GAAX,UAAY,WAA8B;QACxC,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IACrC,CAAC;IAED;;;;;OAKG;IACI,wBAAK,GAAZ,UAAa,WAAqB;QAChC,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;;;;OAKG;IACI,wBAAK,GAAZ,UAAa,WAAqB,EAAE,KAAa;QAC/C,IAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,UAAC,CAAC;YAC5C,kBAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;QAA5B,CAA4B,CAC7B,CAAC;QACF,YAAY,CAAC,OAAO,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,EAAnB,CAAmB,CAAC,CAAC;QACjD,OAAO,YAAY,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAC;IACzC,CAAC;IAMD,sBAAW,6BAAO;QAJlB;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,UAAU;iBACnB,MAAM,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,OAAO,EAAT,CAAS,CAAC;iBACxB,GAAG,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,EAAN,CAAM,CAAC;iBAClB,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,OAAO,EAAT,CAAS,CAAC,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAC,CAAC;QAC5E,CAAC;;;OAAA;IAMD,sBAAW,4BAAM;QAJjB;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,UAAU;iBACnB,MAAM,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,CAAC,OAAO,EAAV,CAAU,CAAC;iBACzB,GAAG,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,EAAN,CAAM,CAAC;iBAClB,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,CAAC,OAAO,EAAV,CAAU,CAAC,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAC,CAAC;QAC7E,CAAC;;;OAAA;IAED;;;;OAIG;IACI,uBAAI,GAAX,UAAY,WAA+B;QAA3C,iBA6CC;QA5CC,WAAW,GAAG,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAE5C,qDAAqD;QACrD,IAAI,YAAY,GAAa,EAAE,CAAC;QAChC,kBAAkB;QAClB,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,YAAY,GAAG,IAAI,CAAC,UAAU;iBAC3B,GAAG,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,EAAN,CAAM,CAAC;iBAClB,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAC,CAAC;YACjD,sCAAsC;YACtC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,OAAO,EAAE,EAAX,CAAW,CAAC,CAAC;YAC5C,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,OAAO,EAAE,EAAX,CAAW,CAAC,CAAC;YAC/C,uBAAuB;YACvB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAClD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAC1D,CAAC;aAAM,CAAC;YACN,kDAAkD;YAClD,IAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,UAAC,CAAC;gBAClD,kBAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;YAA5B,CAA4B,CAC7B,CAAC;YAEF,kBAAkB,CAAC,OAAO,CAAC,UAAC,CAAC;gBAC3B,CAAC,CAAC,OAAO,EAAE,CAAC;gBACZ,KAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;YACH,IAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,UAAC,CAAC;gBACnD,kBAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;YAA5B,CAA4B,CAC7B,CAAC;YACF,gBAAgB,CAAC,OAAO,CAAC,UAAC,CAAC;gBACzB,CAAC,CAAC,OAAO,EAAE,CAAC;gBACZ,KAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;YACH,YAAY,GAAG,kBAAkB;iBAC9B,GAAG,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,EAAN,CAAM,CAAC;iBAClB,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YACrB,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,IAAI,EAAE,YAAY;SACnB,CAAC,CAAC;QAEH,wCAAwC;QACxC,OAAO,YAAY,CAAC;IACtB,CAAC;IAKD,sBAAW,+BAAS;QAHpB;;WAEG;aACH;YACE,OAAO,CACL,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,IAAI,IAAK,UAAG,IAAI,IAAI,CAAC,OAAO,EAAnB,CAAmB,EAAE,KAAK,CAAC;gBACjE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,IAAI,IAAK,UAAG,IAAI,IAAI,CAAC,OAAO,EAAnB,CAAmB,EAAE,KAAK,CAAC,CACrE,CAAC;QACJ,CAAC;;;OAAA;IAKD,sBAAW,8BAAQ;QAHnB;;WAEG;aACH;YACE,OAAO,CACL,CAAC,IAAI,CAAC,SAAS;gBACf,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAC9D,CAAC;QACJ,CAAC;;;OAAA;IAKD,sBAAW,+BAAS;QAHpB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC;QACzE,CAAC;;;OAAA;IAED;;;OAGG;IACI,6BAAU,GAAjB,UAAkB,OAAgB,EAAE,SAAgB;QAAhB,4CAAgB;QAClD,IAAI,aAAqB,CAAC;QAC1B,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpE,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,GAAG,CAAC,EAAE,CAAC;gBACvC,0BAA0B;gBAC1B,IAAI,CAAC,GAAG,CACN,CAAC,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAC1D,OAAO,EACP,SAAS,CACV,CAAC;YACJ,CAAC;iBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,GAAG,CAAC,EAAE,CAAC;gBACjD,8BAA8B;gBAC9B,IAAI,CAAC,GAAG,CACN,CAAC,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAC7D,OAAO,EACP,SAAS,CACV,CAAC;YACJ,CAAC;QACH,CAAC;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,gCAAa,GAApB;QACE,KAAwB,UAAwC,EAAxC,SAAI,CAAC,UAAU,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,OAAO,EAAT,CAAS,CAAC,EAAxC,cAAwC,EAAxC,IAAwC,EAAE,CAAC;YAA9D,IAAM,SAAS;YAClB,+BAA+B;YAC/B,IAAI,SAAS,CAAC,SAAS,KAAK,CAAC,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;gBACrD,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC;gBACxB,2DAA2D;gBAC3D,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC5B,CAAC;iBAAM,IAAI,SAAS,CAAC,SAAS,KAAK,CAAC,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;gBAC5D,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;oBACrB,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,IAAI,EAAE,EAAE,SAAS,EAAE,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE;iBACzD,CAAC,CAAC;gBACH,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC;YAC1B,CAAC;YACD,mDAAmD;YACnD,qDAAqD;YACrD,kBAAkB;iBACb,IAAI,SAAS,CAAC,SAAS,KAAK,CAAC,IAAI,SAAS,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;gBAC9D,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;oBACrB,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,IAAI,EAAE,EAAE,SAAS,EAAE,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,QAAQ,EAAE;iBAC7D,CAAC,CAAC;gBACH,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,qCAAkB,GAAzB;QACE,IAAM,aAAa,GAAa,EAAE,CAAC;QACnC,KAA2B,UAA6C,EAA7C,SAAI,CAAC,aAAa,CAAC,MAAM,CAAC,UAAC,EAAE,IAAK,SAAE,CAAC,OAAO,EAAV,CAAU,CAAC,EAA7C,cAA6C,EAA7C,IAA6C,EAAE,CAAC;YAAtE,IAAM,YAAY;YACrB,aAAa,CAAC,IAAI,OAAlB,aAAa,EAAS,YAAY,CAAC,aAAa,EAAE;QACpD,CAAC;QACD,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gBACrB,IAAI,EAAE,SAAS,CAAC,WAAW;gBAC3B,IAAI,EAAE,aAAa;aACpB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEM,kCAAe,GAAtB,UAAuB,IAAY;QACjC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YACrB,IAAI,EAAE,SAAS,CAAC,OAAO;YACvB,IAAI,EAAE,IAAI;SACX,CAAC,CAAC;IACL,CAAC;IACH,eAAC;AAAD,CAAC;AAED,aAAa;AAEb,iBAAiB;AAEjB;;GAEG;AACH,IAAY,SAYX;AAZD,WAAY,SAAS;IACnB,0BAAa;IACb,oCAAuB;IACvB,0BAAa;IACb,4BAAe;IACf,0BAAa;IACb,0BAAa;IACb,0BAAa;IACb,gCAAmB;IACnB,wCAA2B;IAC3B,oCAAuB;IACvB,oDAAuC;AACzC,CAAC,EAZW,SAAS,KAAT,SAAS,QAYpB;AAWD;;GAEG;AACH,IAAY,QAIX;AAJD,WAAY,QAAQ;IAClB,+BAAmB;IACnB,yBAAa;IACb,iCAAqB;AACvB,CAAC,EAJW,QAAQ,KAAR,QAAQ,QAInB;AA4BD,oCAAoC;AACpC;IACE,sBAAoB,SAA+B;QAA/B,0CAA+B;QAA/B,cAAS,GAAT,SAAS,CAAsB;IAAG,CAAC;IAEvD,mCAAmC;IAC3B,mCAAY,GAApB,UAAqB,IAAe;QAClC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,KAAK,IAAI,EAAf,CAAe,CAAC,CAAC;IACvD,CAAC;IAED,kBAAkB;IACX,0BAAG,GAAV,UAAW,QAAuB;QAChC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChC,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,6BAAM,GAAb,UAAc,QAAuB;QACnC,4EAA4E;QAC5E,0EAA0E;QAC1E,iDAAiD;QACjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/C,IAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC1C,IAAI,eAAe,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAC3C,IAAI,eAAe,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ,EAAE,CAAC;oBACnD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC5B,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACI,gCAAS,GAAhB,UAAiB,IAAgB;QAAjC,iBAQC;QAPC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAClD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS;iBACX,MAAM,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,KAAK,IAAI,EAAf,CAAe,CAAC;iBAC9B,OAAO,CAAC,UAAC,CAAC,IAAK,YAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAd,CAAc,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAED,iBAAiB;IACV,2BAAI,GAAX,UAAY,KAAY;QACtB,IAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACrD,cAAc,CAAC,OAAO,CAAC,UAAC,QAAQ,IAAK,eAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAxB,CAAwB,CAAC,CAAC;IACjE,CAAC;IACH,mBAAC;AAAD,CAAC;AAaD,2BAA2B;AAC3B;IAGE,0BAAoB,YAA0B;QAA1B,iBAAY,GAAZ,YAAY,CAAc;QAFtC,UAAK,GAAW,EAAE,CAAC;IAEsB,CAAC;IAElD,4BAA4B;IACrB,8BAAG,GAAV,UAAW,IAAU;QACnB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC;IAED,mCAAmC;IAC5B,kCAAO,GAAd;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YAChC,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,EAAE,CAAC;gBACjB,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,CAAC;YACD,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,EAAE,CAAC;gBAChB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;IACH,CAAC;IACH,uBAAC;AAAD,CAAC;AAED,aAAa;AAEb,gBAAgB;AAEhB,IAAK,iBAGJ;AAHD,WAAK,iBAAiB;IACpB,mEAAS;IACT,uEAAW;AACb,CAAC,EAHI,iBAAiB,KAAjB,iBAAiB,QAGrB;AAED,gEAAgE;AAChE;IAA2B,gCAAY;IAAvC;;QACU,cAAQ,GAAY,KAAK,CAAC;QAC1B,cAAQ,GAAY,KAAK,CAAC;QAE1B,aAAO,GAAsB,iBAAiB,CAAC,WAAW,CAAC;;IA8GrE,CAAC;IA3Ge,4BAAK,GAAnB,UAAoB,IAAY;;;gBAC9B,sBAAO,IAAI,OAAO,CAAC,UAAC,OAAO,IAAK,iBAAU,CAAC,OAAO,EAAE,IAAI,CAAC,EAAzB,CAAyB,CAAC,EAAC;;;KAC5D;IAEa,8BAAO,GAArB;;;gBACE,sBAAO,IAAI,OAAO,CAAC,UAAC,CAAC,EAAE,MAAM,IAAK,iBAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAtB,CAAsB,CAAC,EAAC;;;KAC3D;IAED,yFAAyF;IACjF,wCAAiB,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,iBAAiB,EAAE,CAAC,CAAC;QACjD,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACW,kCAAW,GAAzB;;;gBACE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;oBACrB,IAAI,CAAC,OAAO,GAAG,iBAAiB,CAAC,SAAS,CAAC;oBAC3C,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC3B,CAAC;;;;KACF;IAED;;;;;OAKG;IACW,gCAAS,GAAvB;;;;;;6BAEI,KAAI,CAAC,OAAO,KAAK,iBAAiB,CAAC,WAAW;4BAC9C,IAAI,CAAC,aAAa,KAAK,IAAI,GAD3B,wBAC2B;;;;wBAKzB,qBAAM,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;;wBAAjE,SAAiE,CAAC;wBAClE,IAAI,CAAC,WAAW,EAAE,CAAC;;;;;;;;;KAKxB;IAED;;;;OAIG;IACW,sCAAe,GAA7B;;;;;6BACM,CAAC,IAAI,CAAC,QAAQ,EAAd,wBAAc;wBAChB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;6BAGjB,QAAO,MAAM,IAAI,WAAW,GAA5B,wBAA4B;wBAC9B,IAAI,CAAC,WAAW,EAAE,CAAC;;;wBAEnB,IAAI,CAAC,aAAa,GAAG,IAAI,YAAY,EAAE,CAAC;wBACxC,IAAI,CAAC,mBAAmB,EAAE,CAAC;;;6BACpB,KAAI,CAAC,OAAO,KAAK,iBAAiB,CAAC,WAAW;wBACnD,qBAAM,IAAI,CAAC,SAAS,EAAE;;wBAAtB,SAAsB,CAAC;wBACvB,qBAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;;wBAAtB,SAAsB,CAAC;;;;;;KAI9B;IAEO,0CAAmB,GAA3B;QAAA,iBAcC;QAbC,0EAA0E;QAC1E,8CAA8C;QAC9C,IAAM,cAAc,GAAG;;gBACrB,uEAAuE;gBACvE,8EAA8E;gBAC9E,4DAA4D;gBAE5D,IAAI,CAAC,WAAW,EAAE,CAAC;;;aACpB,CAAC;QACF,2CAA2C;QAC3C,QAAQ,CAAC,gBAAgB,CAAC,aAAa,EAAE,cAAc,EAAE;YACvD,IAAI,EAAE,IAAI;SACX,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACU,qCAAc,GAA3B;;;gBACE,IAAI,CAAC,eAAe,EAAE,CAAC;;;;KACxB;IAED,sBAAW,sCAAY;aAAvB;YACE,IAAI,IAAI,CAAC,OAAO,KAAK,iBAAiB,CAAC,WAAW,EAAE,CAAC;gBACnD,8EAA8E;gBAC9E,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,OAAO,CAAC,CAAC;YACX,CAAC;YACD,OAAO,CAAC,CAAC;QACX,CAAC;;;OAAA;IAED,sBAAW,gCAAM;aAAjB;YACE,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;;;OAAA;IACH,mBAAC;AAAD,CAAC,CAlH0B,YAAY,GAkHtC;AAED,IAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;AAiBxC;IAAA;IAIA,CAAC;IAHC,oCAAO,GAAP,cAAW,CAAC;IACZ,sCAAS,GAAT,cAAa,CAAC;IACd,uCAAU,GAAV,cAAc,CAAC;IACjB,yBAAC;AAAD,CAAC;AAED,IAAM,gBAAgB,GAAG,UAAU,CAAC,cAAc,IAAI,kBAAkB,CAAC;AAEzE;;;;GAIG;AAEH;IAKE;QAAA,iBAEC;QANO,iBAAY,GAA2C,IAAI,GAAG,EAAE,CAAC;QAQzE;;;WAGG;QACK,qBAAgB,GAAG,UAAC,KAA0B;YACpD,IAAM,QAAQ,GAAG,KAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,MAA2B,CAAC,CAAC;YAC1E,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBACtB,QAAQ,CAAC,QAAQ,CACf,KAAK,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,YAAY,IAAI,CAAC,CAChE,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,KAAI,CAAC,eAAe,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC,CAAC;QAEM,gBAAW,GAAG,UAAC,OAA8B;YACnD,OAAO,CAAC,OAAO,CAAC,KAAI,CAAC,gBAAgB,CAAC,CAAC;QACzC,CAAC,CAAC;QApBA,IAAI,CAAC,eAAe,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAChE,CAAC;IAqBD,6BAA6B;IACtB,6BAAG,GAAV,UAAW,OAA0B,EAAE,QAAkB;QACvD,IAAI,QAAQ,GAAmB;YAC7B,QAAQ;YACR,OAAO;SACR,CAAC;QACF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QACzC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACtC,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,gCAAgC;IACzB,gCAAM,GAAb,UAAc,QAAwB;QACpC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACjD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;IACH,sBAAC;AAAD,CAAC;AAED,IAAM,SAAS,GAAG,IAAI,eAAe,EAAE,CAAC;AA0HxC;IAkCE,kBAAY,MAA0B;QAVtC,2EAA2E;QACnE,uBAAkB,GAAY,IAAI,CAAC;QAKnC,mBAAc,GAAW,CAAC,CAAC;QAE3B,cAAS,GAAY,KAAK,CAAC;QAGjC,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAE5B,IAAI,MAAM,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;QAC9D,IAAI,CAAC,kBAAkB;YACrB,OAAO,MAAM,CAAC,kBAAkB,IAAI,SAAS;gBAC3C,CAAC,CAAC,MAAM,CAAC,kBAAkB;gBAC3B,CAAC,CAAC,IAAI,CAAC;QAEX,8BAA8B;QAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;QACvC,IAAI,MAAM,CAAC,MAAM;YAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAC1D,IAAI,MAAM,CAAC,WAAW;YAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;IAC3E,CAAC;IAEa,2BAAQ,GAAtB;;;;;;;6BACM,IAAI,CAAC,GAAG,EAAR,wBAAQ;wBACV,SAAI;wBAAU,qBAAM,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC;;wBAA1C,GAAK,MAAM,GAAG,SAA4B,CAAC;;;wBAE7C,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;4BACnB,sBAAO;wBACT,CAAC;wBAED,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;4BACrB,MAAM,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC;gCAC9C,YAAY,EAAE,IAAI,CAAC,WAAW;6BAC/B,CAAC,CAAC;wBACL,CAAC;wBACD,qBAAqB;wBACrB,SAAI;wBAAQ,qBAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CACjC,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,EAC3B,MAAM,EACN,IAAI,CAAC,kBAAkB,CACxB;;wBALD,qBAAqB;wBACrB,GAAK,IAAI,GAAG,SAIX,CAAC;wBACF,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;4BACnB,UAAI,CAAC,IAAI,0CAAE,MAAM,EAAE,CAAC;4BACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;4BACjB,sBAAO;wBACT,CAAC;wBACD,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;4BACvB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gCACrB,IAAI,EAAE,SAAS,CAAC,IAAI;gCACpB,IAAI,EAAE,IAAI;6BACX,CAAC,CAAC;wBACL,CAAC;6BAAM,CAAC;4BACN,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gCACrB,IAAI,EAAE,SAAS,CAAC,SAAS;gCACzB,IAAI,EAAE,IAAI;6BACX,CAAC,CAAC;4BACH,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;wBACjD,CAAC;;;;;KACF;IAEY,uBAAI,GAAjB;;;;;;wBACE,+CAA+C;wBAC/C,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;4BAC9B,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;wBAChD,CAAC;wBACD,SAAI;wBAAW,qBAAM,aAAa,CAAC,aAAa,EAAE;;wBAAlD,GAAK,OAAO,GAAG,SAAmC,CAAC;wBAEnD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;4BACnB,sBAAO;wBACT,CAAC;wBACD,qBAAM,IAAI,CAAC,QAAQ,EAAE;;wBAArB,SAAqB,CAAC;;;;;KACvB;IAED;;;;OAIG;IACI,qBAAE,GAAT,UAAU,IAAe,EAAE,QAAuB;QAChD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;YACpB,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,QAAQ;SACnB,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,sBAAG,GAAV,UAAW,IAAe,EAAE,QAAuB;QACjD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;YACvB,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,QAAQ;SACnB,CAAC,CAAC;IACL,CAAC;IAEM,0BAAO,GAAd;;QACE,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;QACzB,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,2BAA2B,EAAE,CAAC;YACnC,UAAI,CAAC,IAAI,0CAAE,MAAM,EAAE,CAAC;YACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACjB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACI,8CAA2B,GAAlC,UAAmC,IAAgB;QACjD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAEM,8BAAW,GAAlB;QACE,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;YACvB,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;YACzB,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB,CAAC;IACH,CAAC;IArJD,6CAA6C;IACrB,4BAAmB,GACzC,0CAA0C,CAAC;IAE7C,oCAAoC;IACZ,6BAAoB,GAC1C,yBAAyB,CAAC;IAgJ9B,eAAC;CAAA;AAvJoB;AAyJrB;IA0GE,cAAY,MAAsB;QAAlC,iBAkEC;;QA5JD,kCAAkC;QAC1B,WAAM,GAAG,KAAK,CAAC;QAEvB,qCAAqC;QAC7B,cAAS,GAAG,KAAK,CAAC;QAE1B,sEAAsE;QAC9D,cAAS,GAA0B,IAAI,CAAC;QAEhD;;;;;;WAMG;QACK,oBAAe,GAAG,KAAK,CAAC;QAKhC,mBAAmB;QACX,aAAQ,GAAuB,IAAI,CAAC;QAE5C,oCAAoC;QAC5B,iBAAY,GAAwB,IAAI,CAAC;QA4BzC,+BAA0B,GAAG,KAAK,CAAC;QAEnC,8BAAyB,GAAG,KAAK,CAAC;QAE1C,2EAA2E;QACnE,uBAAkB,GAAG,IAAI,CAAC;QAElC,mEAAmE;QAC3D,YAAO,GAAG,CAAC,CAAC;QAEpB,kEAAkE;QAC1D,mBAAc,GAAuB,SAAS,CAAC;QAEvD,mEAAmE;QAC3D,oBAAe,GAAuB,SAAS,CAAC;QAExD,8FAA8F;QACtF,0BAAqB,GAAG,CAAC,CAAC;QAElC,yCAAyC;QACjC,iBAAY,GAAG,KAAK,CAAC;QAE7B,uBAAuB;QACf,wBAAmB,GAAyB,IAAI,CAAC;QAEzD,iCAAiC;QACzB,eAAU,GAA+B,IAAI,CAAC;QAE9C,uBAAkB,GAA6B,IAAI,CAAC;QACpD,eAAU,GAAsB,IAAI,CAAC;QAE7C,qFAAqF;QAC9E,cAAS,GAAa,EAAE,CAAC;QACzB,eAAU,GAAa,EAAE,CAAC;QAC1B,eAAU,GAAG,CAAC,CAAC;QACf,yBAAoB,GAAG,KAAK,CAAC;QAuF5B,mBAAc,GAAG,UAAC,WAAoB;YAC5C,IAAM,cAAc,GAAG,KAAI,CAAC,YAAY,KAAK,WAAW,CAAC;YACzD,KAAI,CAAC,YAAY,GAAG,WAAW,CAAC;YAChC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,IAAI,cAAc,EAAE,CAAC;oBACnB,KAAI,CAAC,4BAA4B,EAAE,CAAC;gBACtC,CAAC;YACH,CAAC;iBAAM,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;gBACpD,KAAI,CAAC,cAAc,EAAE,CAAC;YACxB,CAAC;QACH,CAAC,CAAC;QAoTF;;;WAGG;QACK,sBAAiB,GAAG,CAAC,CAAC;QAtZ5B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC5B,IAAI,MAAM,CAAC,MAAM,CAAC,WAAW,KAAK,iBAAiB,EAAE,CAAC;YACpD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,GAAG,CAC5B,IAAI,CAAC,MAA2B,EAChC,IAAI,CAAC,cAAc,CACpB,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC5B,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAChC,IAAI,CAAC,MAAM,GAAG,YAAM,CAAC,MAAM,mCAAI,IAAI,MAAM,EAAE,CAAC;QAC5C,IAAI,CAAC,0BAA0B,GAAG,CAAC,CAAC,MAAM,CAAC,0BAA0B,CAAC;QACtE,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,MAAM,CAAC,oBAAoB,CAAC;QAC1D,IAAI,CAAC,yBAAyB,GAAG,CAAC,CAAC,MAAM,CAAC,yBAAyB,CAAC;QACpE,IAAI,CAAC,kBAAkB;YACrB,MAAM,CAAC,kBAAkB,KAAK,SAAS;gBACrC,CAAC,CAAC,IAAI;gBACN,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAEhC,8BAA8B;QAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;QACvC,IAAI,MAAM,CAAC,MAAM;YAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAC1D,IAAI,MAAM,CAAC,WAAW;YAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;QACzE,IAAI,MAAM,CAAC,MAAM;YAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAC1D,IAAI,MAAM,CAAC,OAAO;YAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QAC7D,IAAI,MAAM,CAAC,MAAM;YAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAC1D,IAAI,MAAM,CAAC,MAAM;YAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAC1D,IAAI,MAAM,CAAC,aAAa;YACtB,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;QACvD,IAAI,MAAM,CAAC,SAAS;YAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAEnE;;WAEG;QACH,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM;YAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAC5E,IAAI,MAAM,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,WAAW;YAC3C,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC;QACnD,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM;YAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAC5E,IAAI,MAAM,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO;YACnC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QAC3C,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM;YAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAC5E,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM;YAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAC5E,IAAI,MAAM,CAAC,aAAa,IAAI,CAAC,MAAM,CAAC,aAAa;YAC/C,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;QAEvD;;WAEG;QACH,IAAI,MAAM,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;QAE9D,yBAAyB;QACzB,IAAI,CAAC,SAAS,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEzD,IAAI,CAAC,IAAI,CAAC;YACR,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,UAAU,EAAE,MAAM,CAAC,UAAU;YAC7B,aAAa,EAAE,MAAM,CAAC,aAAa;YACnC,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,oBAAoB,EAAE,MAAM,CAAC,oBAAoB;SAClD,CAAC,CAAC;IACL,CAAC;IAED,sBAAW,gCAAc;aAAzB;YACE,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;QACpC,CAAC;;;OAAA;IAED,4EAA4E;IAC9D,QAAG,GAAjB,UAAkB,MAAsB;QACtC,OAAO,CAAC,IAAI,CACV,gEAAgE,CACjE,CAAC;QACF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAED,yDAAyD;IACjD,mCAAoB,GAA5B;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;IAC7B,CAAC;IAcD,gEAAgE;IACxD,mBAAI,GAAZ,UAAa,EAUQ;QAVrB,iBAwEC;YAvEC,GAAG,WACH,MAAM,cACN,QAAQ,gBACR,UAAU,kBACV,aAAa,qBACb,QAAQ,gBACR,gBAAgB,EAAhB,QAAQ,mBAAG,KAAK,OAChB,4BAA4B,EAA5B,oBAAoB,mBAAG,KAAK,OAC5B,gBAAgB,EAAhB,QAAQ,mBAAG,KAAK;QAEhB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,+CAA+C;QAC/C,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChD,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAChD,CAAC;QAED,iDAAiD;QACjD,IAAM,sBAAsB,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAC;QAE5D,oDAAoD;QACpD,IAAM,yBAAyB,GAAG,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAElE,uDAAuD;QACvD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAE7B,+BAA+B;QAC/B,aAAa,CAAC,aAAa,EAAE;aAC1B,IAAI,CAAC,UAAC,OAAO;YACZ,IAAI,KAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,OAAO;YACT,CAAC;YACD,KAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YAEvB,KAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,KAAI,CAAC,kBAAkB,EAAE,CAAC;YAE1B,8EAA8E;YAC9E,KAAI,CAAC,QAAQ,GAAG,KAAI,CAAC,OAAO,CAAC,YAAY,CACvC,KAAI,CAAC,MAAM,EACX,oBAAoB,CACrB,CAAC;YAEF,2EAA2E;YAC3E,uBAAuB;YACvB,IAAI,CAAC,CAAC,KAAI,CAAC,MAAM,CAAC,KAAK,IAAI,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC/C,KAAI,CAAC,4BAA4B,EAAE,CAAC;YACtC,CAAC;YAED,oDAAoD;YACpD,KAAI,CAAC,QAAQ,CACX,QAAQ,EACR,sBAAsB,EACtB,yBAAyB,EACzB,QAAQ,EACR,QAAQ,CACT;iBACE,IAAI,CAAC,cAAM,YAAI,CAAC,kBAAkB,EAAE,EAAzB,CAAyB,CAAC;iBACrC,KAAK,CAAC,UAAC,CAAC;gBACP,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;aACD,KAAK,CAAC,UAAC,CAAC;YACP,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG;IACI,iCAAkB,GAAzB,UACE,mBAA+C;QADjD,iBA6BC;QA1BC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE,CAAC;YACrC,IAAM,mBAAmB,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,IAAI,EAAE,CAAC;iBAC5D,MAAM,CAAC,UAAC,EAAE,IAAK,SAAE,CAAC,OAAO,IAAI,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,EAApD,CAAoD,CAAC;iBACpE,GAAG,CAAC,UAAC,EAAE,IAAK,SAAE,CAAC,QAAQ,EAAX,CAAW,CAAC,CAAC;YAC5B,IAAI,wBAAwB,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACzD,IACE,mBAAmB;gBACnB,sBAAsB,IAAI,mBAAmB,EAC7C,CAAC;gBACD,wBAAwB,GAAG,mBAAmB,CAAC,oBAAoB,CAAC;YACtE,CAAC;YACD,IAAI,CAAC,YAAY,GAAG,iEAAyB,CAAC;gBAC5C,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,aAAa,EAAE,mBAAmB;gBAClC,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,IAAI,EAAE,IAAI,CAAC,OAAO;gBAClB,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC;gBAC1C,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC;gBACtD,oBAAoB,EAAE,wBAAwB;gBAC9C,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;aAClD,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACI,kCAAmB,GAA1B;QACE,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC3B,CAAC;IACH,CAAC;IAED;;;OAGG;IACK,8BAAe,GAAvB;QAAA,iBAYC;;QAXC,6BAA6B;QAC7B,IAAI,YAAY,CAAC,MAAM,IAAI,iBAAiB,CAAC,WAAW,EAAE,CAAC;YACzD,IAAI,WAAI,CAAC,QAAQ,0CAAE,QAAQ,KAAI,IAAI,CAAC,mBAAmB,KAAK,IAAI,EAAE,CAAC;gBACjE,IAAI,CAAC,mBAAmB,GAAG;oBACzB,IAAI,EAAE,SAAS,CAAC,iBAAiB;oBACjC,QAAQ,EAAE,cAAM,YAAI,CAAC,oBAAoB,EAAE,EAA3B,CAA2B;iBAC5C,CAAC;gBACF,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAC3C,YAAY,CAAC,cAAc,EAAE,CAAC;YAChC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,+BAAgB,GAAxB;QACE,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO;QAE3B,8CAA8C;QAC9C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK;YACvC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QAC7C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM;YACzC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;IACjD,CAAC;IAED,2DAA2D;IAC7C,uBAAQ,GAAtB,UACE,YAAoB,EACpB,cAAwB,EACxB,iBAA2B,EAC3B,QAAiB,EACjB,QAAiB;;;;;;;;6BAGX,KAAI,CAAC,QAAQ,IAAI,IAAI,GAArB,wBAAqB;wBACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC;4BAC3B,GAAG,EAAE,IAAI,CAAC,GAAG;4BACb,MAAM,EAAE,IAAI,CAAC,MAAM;4BACnB,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;4BAC3C,WAAW,EAAE,IAAI,CAAC,WAAW;yBAC9B,CAAC,CAAC;wBACH,qBAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;;wBAA1B,SAA0B,CAAC;;;wBAE7B,wEAAwE;wBACxE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;4BACnB,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;wBAChD,CAAC;wBACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;wBACxC,4BAA4B;wBAC5B,IAAI,CAAC,YAAY,CACf,YAAY,EACZ,cAAc,EACd,iBAAiB,EACjB,QAAQ,EACR,QAAQ,CACT,CAAC;wBAEF,+BAA+B;wBAC/B,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBAExB,kBAAkB;wBAClB,IAAI,CAAC,eAAe,EAAE,CAAC;wBAEvB,yCAAyC;wBACzC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;wBACnB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;4BACrB,IAAI,EAAE,SAAS,CAAC,IAAI;4BACpB,IAAI,EAAE,UAAI,CAAC,GAAG,mCAAI,QAAQ;yBAC3B,CAAC,CAAC;wBAEH,wEAAwE;wBACxE,4CAA4C;wBAC5C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;wBAC5B,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;wBAEzB,IAAI,CAAC,SAAS,EAAE,CAAC;wBAEjB,sBAAO,OAAO,CAAC,OAAO,EAAE,EAAC;;;wBAEnB,GAAG,GAAG,mBAAmB,CAAC,OAAK,CAAC,CAAC;wBACvC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAClB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;wBACjE,sBAAO,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAC;;;;;KAE9B;IAED,0BAA0B;IAClB,2BAAY,GAApB,UACE,YAAoB,EACpB,cAAwB,EACxB,iBAA2B,EAC3B,QAAiB,EACjB,QAAiB;QAEjB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,OAAO;QACT,CAAC;QACD,qBAAqB;QACrB,IAAM,YAAY,GAAG,YAAY;YAC/B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;YACxC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;QAEhC,mCAAmC;QACnC,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,IAAM,GAAG,GAAG,8CAA8C,CAAC;YAC3D,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;YACjE,OAAO;QACT,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC;QAC7B,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,YAAY,CAAC,YAAY,CAAC;QAE/D,mDAAmD;QACnD,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,GAAG,CAAC,EAAE,CAAC;YACvC,IAAM,GAAG,GAAG,4BAA4B,CAAC;YACzC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;YACjE,MAAM,GAAG,CAAC;QACZ,CAAC;QAED,0BAA0B;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAC1B,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,YAAY,CAClB,CAAC;QAEF,uEAAuE;QACvE,2EAA2E;QAC3E,wEAAwE;QACxE,4BAA4B;QAC5B,IAAI,aAAuB,CAAC;QAC5B,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9D,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACzD,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;YAC7D,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QAC/D,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;QAC9D,CAAC;QACD,sCAAsC;QACtC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;YACjB,KAAK,EAAE;gBACL,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK;gBACjD,IAAI,EAAE,aAAa;aACpB;SACF,CAAC,CAAC;QAEH,IAAI,QAAQ,EAAE,CAAC;YACb,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;YACnE,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;gBACvB,IAAM,eAAe,GAAG,SAAS,CAAC,eAAe,EAAE,CAAC;gBACpD,IAAI,eAAe,KAAK,IAAI,EAAE,CAAC;oBAC7B,IAAM,iBAAiB,GAAG,IAAI,iBAAiB,CAC7C,eAAe,EACf,IAAI,CACL,CAAC;oBACF,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;gBAChD,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,mCAAmC;IAC5B,wBAAS,GAAhB;;QACE,IAAI,cAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,QAAQ,0CAAE,WAAW,EAAE,CAAC;YACpC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACzD,IAAI,CAAC,UAAU,CAAC,QAAS,CAAC,QAAS,CAAC,WAAqB,CAAC,CAAC;gBAC3D,UAAI,CAAC,OAAO,0CAAE,qBAAqB,EAAE,CAAC;YACxC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC;IACH,CAAC;IAeD;;;OAGG;IACK,mBAAI,GAAZ,UAAa,IAAY,EAAE,QAAuB;;QAChD,iEAAiE;QACjE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAM,MAAM,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAEjC,0DAA0D;QAC1D,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,+BAA+B;QAC/B,IAAI,CAAC,iBAAiB,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC;QACrD,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,EAAE,CAAC;YAClC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;YAC3B,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,EAAI,CAAC;QACf,CAAC;QAED,uDAAuD;QACvD,IAAM,WAAW,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC;QACxD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,mEAAmE;QACnE,kDAAkD;QAClD,2DAA2D;QAC3D,IAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU;aAC9C,MAAM,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,OAAO,IAAI,CAAC,CAAC,UAAU,EAAzB,CAAyB,CAAC;YACzC,2EAA2E;YAC3E,8EAA8E;aAC7E,IAAI,CAAC,UAAC,KAAK,IAAK,QAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAA3B,CAA2B,CAAC,CAAC;QAChD,KAAwB,UAAgB,EAAhB,qCAAgB,EAAhB,8BAAgB,EAAhB,IAAgB,EAAE,CAAC;YAAtC,IAAM,SAAS;YAClB,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC/B,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAC/B,SAAS,CAAC,SAAS,IAAI,CAAC,CAAC;YAC3B,CAAC;YACD,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvB,CAAC;QAED,uEAAuE;QACvE,2DAA2D;QAC3D,IAAM,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,CAC5D,UAAC,CAAC,IAAK,QAAC,CAAC,OAAO,EAAT,CAAS,CACjB,CAAC;QACF,KAA2B,UAAmB,EAAnB,2CAAmB,EAAnB,iCAAmB,EAAnB,IAAmB,EAAE,CAAC;YAA5C,IAAM,YAAY;YACrB,oEAAoE;YACpE,IAAM,iBAAiB,GAAG,YAAY,CAAC,kBAAkB,EAAE,CAAC;YAC5D,IAAI,iBAAiB,EAAE,CAAC;gBACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC3C,IAAM,OAAK,GAAG,YAAY,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;oBAE9C,IAAI,OAAK,EAAE,CAAC;wBACV,IAAI,OAAK,CAAC,IAAI,KAAK,aAAa,CAAC,OAAO,EAAE,CAAC;4BACzC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gCACrB,IAAI,EAAE,SAAS,CAAC,SAAS;gCACzB,IAAI,EAAE,OAAwB;6BAC/B,CAAC,CAAC;4BACH,qDAAqD;4BACrD,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;gCACnC,IAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gCAC3C,SAAkB,OAAwB,EAAxC,GAAG,WAAE,MAAM,YAA6B,CAAC;gCAEjD,IAAM,YAAY,GAAG,mDAAW,CAAC,GAAG,CAAC,CAAC;gCACtC,GAAG,IAAI,YAAY,CAAC,YAAY,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;gCACvD,MAAM,IAAI,YAAY,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gCACtD,IAAI,YAAY,IAAI,YAAY,KAAK,6CAAS,EAAE,CAAC;oCAC/C,YAAY,CAAC,KAAK,EAAE,CAAC;gCACvB,CAAC;4BACH,CAAC;wBACH,CAAC;6BAAM,CAAC;4BACN,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gCACrB,IAAI,EAAE,SAAS,CAAC,SAAS;gCACzB,IAAI,EAAE,OAAqB;6BAC5B,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YACD,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAC1C,8CAA8C;QAChD,CAAC;QAED,oEAAoE;QACpE,uBAAuB;QACvB,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC5C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC;QAEO,YAAQ,GAAK,IAAI,SAAT,CAAU;QAC1B,4CAA4C;QAC5C,QAAQ,CAAC,KAAK,EAAE,CAAC;QACjB,QAAQ,CAAC,IAAI,EAAE,CAAC;QAEhB,6CAA6C;QAC7C,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,+BAA+B;QAC/B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/B,CAAC;QAED,QAAQ,CAAC,OAAO,EAAE,CAAC;QACnB,QAAQ,CAAC,KAAK,EAAE,CAAC;QAEjB,uCAAuC;QACvC,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;QAE9B,uDAAuD;QACvD,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;QAEnC,uBAAuB;QACvB,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAE3C,kDAAkD;QAClD,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,IAAI,EAAE,CAAC;YAC1C,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YACxB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC;QAED,UAAI,CAAC,kBAAkB,0CAAE,eAAe,EAAE,CAAC;QAE3C,uEAAuE;QACvE,wFAAwF;QACxF,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;YAC5B,gCAAgC;YAChC,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YAClC,mEAAmE;YACnE,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QAC1B,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;YACnC,+CAA+C;YAC/C,wDAAwD;YACxD,uBAAuB;YACvB,oBAAoB;YACpB,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,4BAAa,GAArB;QACQ,SAA2C,IAAI,EAA7C,QAAQ,gBAAE,OAAO,eAAE,OAAO,eAAE,QAAQ,cAAS,CAAC;QACtD,kEAAkE;QAClE,QAAQ,CAAC,KAAK,CACZ,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,EAC3B,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,EACjC;YACE,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,IAAI,EAAE,OAAO,CAAC,IAAI;SACnB,EACD,QAAQ,CAAC,MAAM,EACf,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,iBAAiB,CACvD,CAAC;IACJ,CAAC;IAED,sBAAW,qBAAG;aAAd;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QAC/B,CAAC;;;OAAA;IAED,sBAAW,2BAAS;aAApB;YACE,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAChC,OAAO,CAAC,CAAC;YACX,CAAC;YACD,OAAO,CACL,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,QAAC,GAAG,CAAC,EAAL,CAAK,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAClE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;;;OAAA;IAED;;;;;;;OAOG;IACI,sBAAO,GAAd;;QACE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,uDAAuD;QACvD,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,+DAA+D;QAC/D,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,uBAAuB;QACvB,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;YAC5B,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACnC,CAAC;QACD,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,UAAI,CAAC,QAAQ,0CAAE,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,IAAI,CAAC,mBAAmB,KAAK,IAAI,EAAE,CAAC;YACtC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC9C,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAClC,CAAC;QACD,UAAI,CAAC,kBAAkB,0CAAE,OAAO,EAAE,CAAC;QACnC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;;OAGG;IACI,iCAAkB,GAAzB;;QACE,UAAI,CAAC,QAAQ,0CAAE,MAAM,EAAE,CAAC;QACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;IAED;;;;;;OAMG;IACI,+BAAgB,GAAvB;QACE,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;QACD,mDAAmD;QACnD,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACK,8BAAe,GAAvB,UAAwB,WAAmB;;QACzC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC1C,OAAO;QACT,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;YACnE,OAAO;QACT,CAAC;QACD,IAAM,OAAO,GAAoB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACpE,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,CAAC,IAAI,CACV,iDAA0C,WAAW,uBAAa,UAAI,CAAC,QAAQ,0CAAE,IAAI,8GAA2G,CACjM,CAAC;YACF,OAAO;QACT,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;;OAMG;IACI,8BAAe,GAAtB,UAAuB,WAAmB;QACxC,IAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAClD,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;IAC5C,CAAC;IAED;;;;;OAKG;IACI,8BAAe,GAAtB,UAAuB,WAAmB,EAAE,YAAoB;QAC9D,IAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAClD,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,IAAI,GAAG,YAAY,CAAC;QAC9B,CAAC;IACH,CAAC;IAED,yEAAyE;IAClE,mBAAI,GAAX,UAAY,cAAkC,EAAE,QAAe;QAA/D,iBAgBC;QAfC,cAAc,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;QAElD,8CAA8C;QAC9C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;gBACjB,MAAM,EAAE,cAAM,YAAI,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAnC,CAAmC;aAClD,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACnC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;QACD,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAED,8DAA8D;IACvD,oBAAK,GAAZ,UAAa,cAAkC;QAA/C,iBAcC;QAbC,cAAc,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;QAElD,wDAAwD;QACxD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;gBACjB,MAAM,EAAE,cAAM,YAAI,CAAC,KAAK,CAAC,cAAc,CAAC,EAA1B,CAA0B;aACzC,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IACtC,CAAC;IAEM,oBAAK,GAAZ,UAAa,cAAkC,EAAE,KAAc;QAA/D,iBAeC;QAdC,cAAc,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;QAElD,wDAAwD;QACxD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;gBACjB,MAAM,EAAE,cAAM,YAAI,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,EAAjC,CAAiC;aAChD,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,mEAAmE;QACnE,yEAAyE;QACzE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAED,iEAAiE;IAC1D,mBAAI,GAAX,UAAY,cAA8C;QAA1D,iBAgBC;QAfC,cAAc,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;QAClD,wDAAwD;QACxD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;gBACjB,MAAM,EAAE,cAAM,YAAI,CAAC,IAAI,CAAC,cAAc,CAAC,EAAzB,CAAyB;aACxC,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QACD,2DAA2D;QAC3D,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACI,oBAAK,GAAZ,UAAa,MAA4B;;QACvC,4EAA4E;QAC5E,IAAM,YAAY,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,QAAQ,CAAC;QACtC,IAAM,cAAc,GAAG,gBAAgB,CAAC,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,UAAU,CAAC,CAAC;QAC5D,IAAM,iBAAiB,GAAG,gBAAgB,CAAC,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,aAAa,CAAC,CAAC;QAClE,IAAM,QAAQ,GAAG,YAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,QAAQ,mCAAI,KAAK,CAAC;QAC3C,IAAM,QAAQ,GAAG,YAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,QAAQ,mCAAI,KAAK,CAAC;QAE3C,+BAA+B;QAC/B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,mDAAmD;QACnD,IAAI,CAAC,YAAY,CACf,YAAY,EACZ,cAAc,EACd,iBAAiB,EACjB,QAAQ,EACR,QAAQ,CACT,CAAC;QACF,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,oDAAoD;IAC7C,mBAAI,GAAX,UAAY,MAA0B;QACpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,sBAAsB;QACtB,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,eAAe;QACf,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACpB,CAAC;IAGD,sBAAW,wBAAM;QAWjB;;;;WAIG;aACH;YACE,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;QAnBD,oBAAoB;aACpB,UAAkB,MAAc;YAC9B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,0EAA0E;YAC1E,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBACjC,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;gBAC5C,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,CAAC;QACH,CAAC;;;OAAA;IAWD;;;OAGG;IACI,6BAAc,GAArB;QACE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;YAClC,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;YACvB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;SACzB,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACI,2CAA4B,GAAnC,UAAoC,sBAA+B;QACjE,IAAI,IAAI,CAAC,MAAM,YAAY,iBAAiB,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;YACnD,SAAoB,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,EAArD,KAAK,aAAE,MAAM,YAAwC,CAAC;YAC9D,IAAM,GAAG,GAAG,sBAAsB,IAAI,MAAM,CAAC,gBAAgB,IAAI,CAAC,CAAC;YACnE,IAAI,CAAC,oBAAoB,GAAG,GAAG,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,GAAG,KAAK,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC;YAClC,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,SAAS,EAAE,CAAC;YAEjB,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,EAAE,CAAC;gBACnC,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC;gBACnD,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,KAAK,GAAG,WAAW,CAAC;gBAC1C,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM,GAAG,WAAW,CAAC;YAC9C,CAAC;QACH,CAAC;IACH,CAAC;IAGD,sBAAW,wBAAM;QADjB,uDAAuD;aACvD;YACE,OAAO,IAAI,CAAC,GAAG,CAAC;QAClB,CAAC;;;OAAA;IAKD,sBAAW,gCAAc;QAHzB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACjD,CAAC;;;OAAA;IAGD,sBAAW,gCAAc;QADzB,2DAA2D;aAC3D;YACE,qDAAqD;YACrD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnC,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,IAAM,cAAc,GAAa,EAAE,CAAC;YACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBACxD,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC9D,CAAC;YACD,OAAO,cAAc,CAAC;QACxB,CAAC;;;OAAA;IAKD,sBAAW,mCAAiB;QAH5B;;WAEG;aACH;YACE,qDAAqD;YACrD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnC,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,IAAM,iBAAiB,GAAa,EAAE,CAAC;YACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3D,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACpE,CAAC;YACD,OAAO,iBAAiB,CAAC;QAC3B,CAAC;;;OAAA;IAED;;;;;OAKG;IACI,iCAAkB,GAAzB,UAA0B,IAAY;QACpC,wDAAwD;QACxD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;QACT,CAAC;QACD,IAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CACnD,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,KAAK,IAAI,EAAf,CAAe,CACvB,CAAC;QACF,OAAO,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,MAAM,CAAC;IAC9B,CAAC;IAED,6DAA6D;IACrD,kCAAmB,GAA3B,UACE,IAAY,EACZ,IAAY;QAEZ,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,CAAC,IAAI,CAAC,2CAAoC,IAAI,MAAG,CAAC,CAAC;YAC1D,OAAO;QACT,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO,CAAC,IAAI,CACV,kCAA2B,IAAI,0BAAgB,IAAI,gCAA6B,CACjF,CAAC;YACF,OAAO;QACT,CAAC;QACD,IAAM,KAAK,GAAgB,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAEjE,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,CAAC,IAAI,CACV,gDAAyC,IAAI,yBAAe,IAAI,MAAG,CACpE,CAAC;YACF,OAAO;QACT,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;OAKG;IACI,oCAAqB,GAA5B,UACE,SAAiB,EACjB,KAAc,EACd,IAAY;QAEZ,IAAM,KAAK,GAAgB,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QACrE,IAAI,CAAC,KAAK;YAAE,OAAO;QAEnB,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAqB,CAAC,OAAO,EAAE,CAAC;YACjD,KAAK,CAAC,MAAM,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC;QAC/B,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CACV,4BAAqB,SAAS,yBAAe,IAAI,uBAAoB,CACtE,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACI,mCAAoB,GAA3B,UAA4B,SAAiB,EAAE,KAAa,EAAE,IAAY;QACxE,IAAM,KAAK,GAAgB,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QACrE,IAAI,CAAC,KAAK;YAAE,OAAO;QAEnB,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAqB,CAAC,MAAM,EAAE,CAAC;YAChD,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CACV,4BAAqB,SAAS,yBAAe,IAAI,sBAAmB,CACrE,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;;OAIG;IACI,8BAAe,GAAtB,UAAuB,SAAiB,EAAE,IAAY;QACpD,IAAM,KAAK,GAAgB,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QACrE,IAAI,CAAC,KAAK;YAAE,OAAO;QAEnB,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAqB,CAAC,OAAO,EAAE,CAAC;YACjD,KAAK,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,CAAC;QAC3B,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CACV,4BAAqB,SAAS,yBAAe,IAAI,uBAAoB,CACtE,CAAC;QACJ,CAAC;IACH,CAAC;IAED,0EAA0E;IAClE,iCAAkB,GAA1B,UACE,IAAY,EACZ,IAAY;QAEZ,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,CAAC,IAAI,CAAC,0CAAmC,IAAI,MAAG,CAAC,CAAC;YACzD,OAAO;QACT,CAAC;QACD,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,CAAC,IAAI,CAAC,qCAA8B,IAAI,MAAG,CAAC,CAAC;YACpD,OAAO;QACT,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO,CAAC,IAAI,CACV,iCAA0B,IAAI,0BAAgB,IAAI,gCAA6B,CAChF,CAAC;YACF,OAAO;QACT,CAAC;QACD,IAAM,IAAI,GAAoB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACnE,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,CAAC,IAAI,CACV,4CAAqC,IAAI,yBAAe,IAAI,MAAG,CAChE,CAAC;YACF,OAAO;QACT,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACI,oCAAqB,GAA5B,UACE,QAAgB,EAChB,IAAY;QAEZ,IAAM,GAAG,GAAoB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACrE,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,OAAO,CAAC,IAAI,CACV,yCAAkC,QAAQ,yBAAe,IAAI,MAAG,CACjE,CAAC;YACF,OAAO;QACT,CAAC;QACD,OAAO,GAAG,CAAC,IAAI,CAAC;IAClB,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACI,oCAAqB,GAA5B,UAA6B,QAAgB,EAAE,KAAa,EAAE,IAAY;QACxE,IAAM,GAAG,GAAoB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACrE,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,OAAO,CAAC,IAAI,CACV,yCAAkC,QAAQ,yBAAe,IAAI,MAAG,CACjE,CAAC;YACF,OAAO;QACT,CAAC;QACD,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC;IACnB,CAAC;IAGD,sBAAW,0CAAwB;QADnC,0CAA0C;aAC1C;YACE,qDAAqD;YACrD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa;iBAC/B,MAAM,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,OAAO,EAAT,CAAS,CAAC;iBACxB,GAAG,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAC;QACxB,CAAC;;;OAAA;IAGD,sBAAW,uCAAqB;QADhC,4CAA4C;aAC5C;YACE,qDAAqD;YACrD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,OAAO,EAAT,CAAS,CAAC,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAC;QAC9E,CAAC;;;OAAA;IAGD,sBAAW,sCAAoB;QAD/B,2CAA2C;aAC3C;YACE,qDAAqD;YACrD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU;iBAC5B,MAAM,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,CAAC,OAAO,EAAV,CAAU,CAAC;iBACzB,GAAG,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAC;QACxB,CAAC;;;OAAA;IAMD,sBAAW,yCAAuB;QAJlC;;;WAGG;aACH;YACE,qDAAqD;YACrD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa;iBAC/B,MAAM,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,CAAC,OAAO,EAAV,CAAU,CAAC;iBACzB,GAAG,CAAC,UAAC,CAAC,IAAK,QAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAAC;QACxB,CAAC;;;OAAA;IAKD,sBAAW,2BAAS;QAHpB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QACjC,CAAC;;;OAAA;IAKD,sBAAW,0BAAQ;QAHnB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAChC,CAAC;;;OAAA;IAKD,sBAAW,2BAAS;QAHpB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QACjC,CAAC;;;OAAA;IAMD,sBAAW,wBAAM;QAJjB;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;QAC1D,CAAC;;;OAAA;IAED;;;;OAIG;IACI,iBAAE,GAAT,UAAU,IAAe,EAAE,QAAuB;QAChD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;YACpB,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,QAAQ;SACnB,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,kBAAG,GAAV,UAAW,IAAe,EAAE,QAAuB;QACjD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;YACvB,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,QAAQ;SACnB,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,0BAAW,GAAlB,UAAmB,IAAe,EAAE,QAAuB;QACzD,OAAO,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;QACzE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC3B,CAAC;IAED;;;;;OAKG;IACI,0CAA2B,GAAlC,UAAmC,IAAgB;QACjD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAED;;;;;;OAMG;IACI,6BAAc,GAArB,UAAsB,IAAgB;QACpC,OAAO,CAAC,IAAI,CACV,kFAAkF,CACnF,CAAC;QACF,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAED;;;;;;;;OAQG;IACI,4BAAa,GAApB;QACE,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACvC,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC;gBACtC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACzD,CAAC;iBAAM,CAAC;gBACN,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC5C,CAAC;YACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,6BAAc,GAArB;QACE,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzD,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;gBACvC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,CACtD,IAAI,CAAC,UAAU,CAChB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,cAAc,GAAG,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACI,+BAAgB,GAAvB,UAAwB,WAAyB;QAC/C,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,gCAAiB,GAAxB;QACE,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;IACnC,CAAC;IAKD,sBAAW,0BAAQ;QAHnB;;WAEG;aACH;YACE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjB,OAAO,SAAS,CAAC;YACnB,CAAC;YACD,IAAM,YAAY,GAAqB;gBACrC,SAAS,EAAE,EAAE;aACd,CAAC;YACF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBACnD,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBAC9C,IAAM,gBAAgB,GAAqB;oBACzC,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACnB,UAAU,EAAE,EAAE;oBACd,aAAa,EAAE,EAAE;iBAClB,CAAC;gBACF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;oBACnD,IAAM,SAAS,GAAG,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;oBAC/C,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACnD,CAAC;gBACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,iBAAiB,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;oBACtD,IAAM,YAAY,GAAG,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;oBACrD,IAAM,MAAI,GAAG,YAAY,CAAC,IAAI,CAAC;oBAC/B,IAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,CACpD,YAAY,EACZ,QAAQ,CACT,CAAC;oBACF,IAAM,aAAa,GAAgC,EAAE,CAAC;oBACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;wBAC/C,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBAChC,aAAa,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;oBAC7D,CAAC;oBACD,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC;wBAClC,IAAI,EAAE,MAAI;wBACV,MAAM,EAAE,aAAa;qBACtB,CAAC,CAAC;gBACL,CAAC;gBACD,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YACD,OAAO,YAAY,CAAC;QACtB,CAAC;;;OAAA;IAKD,sBAAW,wBAAM;QAHjB;;WAEG;aACH;YACE,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC3D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YACtC,CAAC;YACD,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;aAED,UAAkB,KAAa;YAC7B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACrB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,GAAG,YAAY,CAAC,YAAY,CAAC;YAC3D,CAAC;QACH,CAAC;;;OAPA;IAmBD,sBAAW,+BAAa;QAVxB;;;;;;;;;WASG;aACH;;YACE,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC7B,CAAC;YACD,OAAO,UAAI,CAAC,cAAc,mCAAI,CAAC,CAAC;QAClC,CAAC;aAED,UAAyB,KAAa;YACpC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAC5B,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;YAC9B,CAAC;QACH,CAAC;;;OAPA;IAmBD,sBAAW,gCAAc;QAVzB;;;;;;;;;WASG;aACH;;YACE,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC9B,CAAC;YACD,OAAO,UAAI,CAAC,eAAe,mCAAI,CAAC,CAAC;QACnC,CAAC;aAED,UAA0B,KAAa;YACrC,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAE7B,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;YAC/B,CAAC;QACH,CAAC;;;OARA;IAUD;;OAEG;IACI,gCAAiB,GAAxB;QACE,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC;YAClC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC1C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC9C,CAAC;aAAM,CAAC;YACN,6EAA6E;YAC7E,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;YAChC,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;QACnC,CAAC;IACH,CAAC;IAQD,sBAAW,sCAAoB;QAN/B;;;;;WAKG;aACH;YACE,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC,CAAC;aAED,UAAgC,KAAa;YAC3C,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACrC,CAAC;;;OAJA;IAMD;;OAEG;IACI,oCAAqB,GAA5B,UAA6B,iBAA2C;;QACtE,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACrC,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,eAAe,EAAE,CAAC;gBAC3D,iBAAiB,CAAC,+BAA+B,EAAE,CAAC;gBACpD,UAAI,CAAC,kBAAkB,0CAAE,OAAO,EAAE,CAAC;gBACnC,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;gBAC5C,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC3C,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,UAAC,YAAY;wBAC/C,mBAAY,CAAC,qBAAqB,CAAC,iBAAiB,CAAC;oBAArD,CAAqD,CACtD,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CACjC,iBAAiB,CAAC,eAAe,CAClC,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,sBAAW,mCAAiB;aAA5B;YACE,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACjC,CAAC;;;OAAA;IAEM,+BAAgB,GAAvB,UAAwB,KAAa;QACnC,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACpD,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;YACvB,OAAO,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC;QAClC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,8BAAe,GAAtB,UAAuB,IAAY;QACjC,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;YACvB,OAAO,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC;QAClC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,oBAAK,GAAZ;QACE,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;YAC7B,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACpC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,UAAC,QAAQ;gBACvC,OAAO,IAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAEM,+BAAgB,GAAvB;QACE,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpE,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IA7/CD,6CAA6C;IACrB,wBAAmB,GACzC,0CAA0C,CAAC;IAE7C,sCAAsC;IACd,wBAAmB,GACzC,4CAA4C,CAAC;IAw/CjD,WAAC;CAAA;AA3jDgB;AA6jDjB;IAGE,mBAAY,SAAuB;QACjC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC9B,CAAC;IAED,sBAAW,oCAAa;aAAxB;YACE,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;QACvC,CAAC;;;OAAA;IAED,sBAAW,2BAAI;aAAf;YACE,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QAC9B,CAAC;;;OAAA;IAEM,mCAAe,GAAtB,UAAuB,KAAa;QAClC,IAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QACxD,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;YACtB,OAAO,IAAI,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC/C,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,kCAAc,GAArB,UAAsB,IAAY;QAChC,IAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACtD,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;YACtB,OAAO,IAAI,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC/C,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,mCAAe,GAAtB;QACE,IAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC;QAC1D,IAAI,eAAe,KAAK,IAAI,EAAE,CAAC;YAC7B,OAAO,IAAI,iBAAiB,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QACtD,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,4BAAQ,GAAf;QACE,IAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;QACnD,IAAI,eAAe,KAAK,IAAI,EAAE,CAAC;YAC7B,OAAO,IAAI,iBAAiB,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QACtD,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,sBAAW,iCAAU;aAArB;YACE,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;QACzC,CAAC;;;OAAA;IAED,sBAAW,oCAAa;aAAxB;YACE,OAAO,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;QAC5C,CAAC;;;OAAA;IACH,gBAAC;AAAD,CAAC;;AAED;IAGE,kBAAY,QAAqB;QAC/B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,sBAAW,0BAAI;aAAf;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAC7B,CAAC;;;OAAA;IAED,sBAAW,4BAAM;aAAjB;YACE,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QAC/B,CAAC;;;OAAA;IACH,eAAC;AAAD,CAAC;;AAED,IAAK,YAOJ;AAPD,WAAK,YAAY;IACf,iCAAiB;IACjB,iCAAiB;IACjB,mCAAmB;IACnB,+BAAe;IACf,mCAAmB;IACnB,6BAAa;AACf,CAAC,EAPI,YAAY,KAAZ,YAAY,QAOhB;AAED;IAaE,2BACE,eAAqC,EACrC,MAAgC;QAZ1B,aAAQ,GAAwB,EAAE,CAAC;QAEnC,cAAS,GAAwB,EAAE,CAAC;QAEpC,wBAAmB,GAAmC,IAAI,GAAG,EAAE,CAAC;QAEhE,6BAAwB,GAA6B,EAAE,CAAC;QAExD,oBAAe,GAAG,CAAC,CAAC;QAM1B,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YACpB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAED,sBAAW,8CAAe;aAA1B;YACE,OAAO,IAAI,CAAC,gBAAgB,CAAC;QAC/B,CAAC;;;OAAA;IAEM,2CAAe,GAAtB;QACE,IAAI,IAAI,CAAC,wBAAwB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/C,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,UAAC,QAAQ;gBAC7C,QAAQ,CAAC,eAAe,EAAE,CAAC;YAC7B,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,UAAC,QAAQ;gBAC7C,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC1B,CAAC,CAAC,CAAC;QACL,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAC,KAAK,IAAK,YAAK,CAAC,eAAe,EAAE,EAAvB,CAAuB,CAAC,CAAC;IAC7D,CAAC;IAEM,qCAAS,GAAhB,UAAiB,MAAyB;QACxC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3B,IAAI,IAAI,CAAC,wBAAwB,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1E,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAEM,wCAAY,GAAnB,UAAoB,MAAyB;QAC3C,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACjB,IAAM,QAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACpC,QAAM,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;IAED;;OAEG;IACI,kDAAsB,GAA7B,UAA8B,QAAgC;QAA9D,iBASC;QARC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtD,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7C,IAAI,IAAI,CAAC,wBAAwB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAC,MAAM;oBAC3B,MAAM,CAAC,uBAAuB,CAAC,KAAI,CAAC,CAAC;gBACvC,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACI,uDAA2B,GAAlC,UAAmC,QAAgC;QAAnE,iBAcC;QAbC,IAAI,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrD,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAClE,UAAC,IAAI,IAAK,WAAI,KAAK,QAAQ,EAAjB,CAAiB,CAC5B,CAAC;YACF,IACE,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;gBAC3B,IAAI,CAAC,wBAAwB,CAAC,MAAM,KAAK,CAAC,EAC1C,CAAC;gBACD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAC,MAAM;oBAC3B,MAAM,CAAC,4BAA4B,CAAC,KAAI,CAAC,CAAC;gBAC5C,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACI,mDAAuB,GAA9B,UAA+B,QAA2B;QAA1D,iBAOC;QANC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAC,MAAM;gBAC3B,MAAM,CAAC,uBAAuB,CAAC,KAAI,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACI,wDAA4B,GAAnC,UAAoC,QAA2B;QAA/D,iBAYC;QAXC,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAC,KAAK,IAAK,YAAK,KAAK,QAAQ,EAAlB,CAAkB,CAAC,CAAC;YACtE,IACE,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;gBAC3B,IAAI,CAAC,wBAAwB,CAAC,MAAM,KAAK,CAAC,EAC1C,CAAC;gBACD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAC,MAAM;oBAC3B,MAAM,CAAC,4BAA4B,CAAC,KAAI,CAAC,CAAC;gBAC5C,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAEO,0CAAc,GAAtB;QACE,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,UAAC,QAAQ;YAC7C,QAAQ,CAAC,cAAc,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,4CAAgB,GAAxB,UACE,IAAY,EACZ,IAAkB;QAElB,IAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC,wBAAwB,CAAC,YAAY,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IAC9D,CAAC;IAEO,qDAAyB,GAAjC,UACE,YAAsB,EACtB,KAAa;QAEb,IAAM,iBAAiB,GAAG,IAAI,CAAC,yBAAyB,CACtD,YAAY,CAAC,KAAK,CAAC,CACpB,CAAC;QACF,IAAI,iBAAiB,KAAK,IAAI,EAAE,CAAC;YAC/B,IAAI,KAAK,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrC,OAAO,iBAAiB,CAAC;YAC3B,CAAC;iBAAM,CAAC;gBACN,OAAO,iBAAiB,CAAC,yBAAyB,CAChD,YAAY,EACZ,KAAK,EAAE,CACR,CAAC;YACJ,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,oDAAwB,GAAhC,UACE,YAAsB,EACtB,KAAa,EACb,IAAkB;;QAElB,IAAI,KAAK,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpC,IAAM,iBAAiB,GAAG,IAAI,CAAC,yBAAyB,CACtD,YAAY,CAAC,KAAK,CAAC,CACpB,CAAC;YACF,IAAI,iBAAiB,KAAK,IAAI,EAAE,CAAC;gBAC/B,OAAO,iBAAiB,CAAC,wBAAwB,CAC/C,YAAY,EACZ,KAAK,GAAG,CAAC,EACT,IAAI,CACL,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QACD,IAAI,QAAQ,GAAqC,IAAI,CAAC;QACtD,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,YAAY,CAAC,MAAM;gBACtB,QAAQ,GAAG,gBAAI,CAAC,gBAAgB,0CAAE,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,mCAAI,IAAI,CAAC;gBACtE,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;oBACtB,OAAO,IAAI,uBAAuB,CAChC,QAAsC,EACtC,IAAI,CACL,CAAC;gBACJ,CAAC;gBACD,MAAM;YACR,KAAK,YAAY,CAAC,MAAM;gBACtB,QAAQ,GAAG,gBAAI,CAAC,gBAAgB,0CAAE,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,mCAAI,IAAI,CAAC;gBACtE,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;oBACtB,OAAO,IAAI,uBAAuB,CAChC,QAAsC,EACtC,IAAI,CACL,CAAC;gBACJ,CAAC;gBACD,MAAM;YACR,KAAK,YAAY,CAAC,OAAO;gBACvB,QAAQ,GAAG,gBAAI,CAAC,gBAAgB,0CAAE,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,mCAAI,IAAI,CAAC;gBACvE,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;oBACtB,OAAO,IAAI,wBAAwB,CACjC,QAAuC,EACvC,IAAI,CACL,CAAC;gBACJ,CAAC;gBACD,MAAM;YACR,KAAK,YAAY,CAAC,KAAK;gBACrB,QAAQ,GAAG,gBAAI,CAAC,gBAAgB,0CAAE,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,mCAAI,IAAI,CAAC;gBACrE,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;oBACtB,OAAO,IAAI,sBAAsB,CAC/B,QAAqC,EACrC,IAAI,CACL,CAAC;gBACJ,CAAC;gBACD,MAAM;YACR,KAAK,YAAY,CAAC,OAAO;gBACvB,QAAQ,GAAG,gBAAI,CAAC,gBAAgB,0CAAE,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,mCAAI,IAAI,CAAC;gBACvE,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;oBACtB,OAAO,IAAI,wBAAwB,CACjC,QAAuC,EACvC,IAAI,CACL,CAAC;gBACJ,CAAC;gBACD,MAAM;YACR,KAAK,YAAY,CAAC,IAAI;gBACpB,QAAQ,GAAG,gBAAI,CAAC,gBAAgB,0CAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,mCAAI,IAAI,CAAC;gBACpE,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;oBACtB,OAAO,IAAI,qBAAqB,CAC9B,QAAoC,EACpC,IAAI,CACL,CAAC;gBACJ,CAAC;gBACD,MAAM;QACV,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,qDAAyB,GAAjC,UAAkC,IAAY;;QAC5C,IAAI,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;QAC7C,CAAC;QACD,IAAM,wBAAwB,GAAG,UAAI,CAAC,gBAAgB,0CAAE,SAAS,CAAC,IAAI,CAAC,CAAC;QACxE,IAAI,wBAAwB,KAAK,IAAI,EAAE,CAAC;YACtC,IAAM,iBAAiB,GAAG,IAAI,iBAAiB,CAC7C,wBAAyB,EACzB,IAAI,CACL,CAAC;YACF,iBAAiB,CAAC,+BAA+B,EAAE,CAAC;YACpD,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;YACtD,OAAO,iBAAiB,CAAC;QAC3B,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACI,kCAAM,GAAb,UAAc,IAAY;QACxB,IAAM,sBAAsB,GAAG,IAAI,CAAC,gBAAgB,CAClD,IAAI,EACJ,YAAY,CAAC,MAAM,CACpB,CAAC;QACF,OAAO,sBAAiD,CAAC;IAC3D,CAAC;IAED;;;;OAIG;IACI,kCAAM,GAAb,UAAc,IAAY;QACxB,IAAM,sBAAsB,GAAG,IAAI,CAAC,gBAAgB,CAClD,IAAI,EACJ,YAAY,CAAC,MAAM,CACpB,CAAC;QACF,OAAO,sBAAwD,CAAC;IAClE,CAAC;IAED;;;;OAIG;IACI,mCAAO,GAAd,UAAe,IAAY;QACzB,IAAM,sBAAsB,GAAG,IAAI,CAAC,gBAAgB,CAClD,IAAI,EACJ,YAAY,CAAC,OAAO,CACrB,CAAC;QACF,OAAO,sBAAyD,CAAC;IACnE,CAAC;IAED;;;;OAIG;IACI,iCAAK,GAAZ,UAAa,IAAY;QACvB,IAAM,sBAAsB,GAAG,IAAI,CAAC,gBAAgB,CAClD,IAAI,EACJ,YAAY,CAAC,KAAK,CACnB,CAAC;QACF,OAAO,sBAAuD,CAAC;IACjE,CAAC;IAED;;;;OAIG;IACI,mCAAO,GAAd,UAAe,IAAY;QACzB,IAAM,sBAAsB,GAAG,IAAI,CAAC,gBAAgB,CAClD,IAAI,EACJ,YAAY,CAAC,OAAO,CACrB,CAAC;QACF,OAAO,sBAAyD,CAAC;IACnE,CAAC;IAED;;;;OAIG;IACI,gCAAI,GAAX,UAAY,IAAY;QACtB,IAAM,sBAAsB,GAAG,IAAI,CAAC,gBAAgB,CAClD,IAAI,EACJ,YAAY,CAAC,IAAI,CAClB,CAAC;QACF,OAAO,sBAAsD,CAAC;IAChE,CAAC;IAED;;;;OAIG;IACI,qCAAS,GAAhB,UAAiB,IAAY;QAC3B,IAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACrC,IAAM,uBAAuB,GAC3B,YAAY,CAAC,MAAM,GAAG,CAAC;YACrB,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAC5B,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,EAC9C,CAAC,CACF;YACH,CAAC,CAAC,IAAI,CAAC;QACX,IAAI,uBAAuB,IAAI,IAAI,EAAE,CAAC;YACpC,OAAO,uBAAuB,CAAC,yBAAyB,CACtD,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CACtC,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,oDAAwB,GAA/B,UACE,IAAY,EACZ,KAAwB;;QAExB,IAAI,KAAK,CAAC,eAAe,KAAK,IAAI,EAAE,CAAC;YACnC,IAAM,MAAM,GACV,WAAI,CAAC,gBAAgB,0CAAE,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,eAAgB,CAAC;gBACrE,KAAK,CAAC;YACR,IAAI,MAAM,EAAE,CAAC;gBACX,KAAK,CAAC,+BAA+B,EAAE,CAAC;gBACxC,IAAM,aAAW,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;gBACzD,IAAI,aAAW,KAAK,IAAI,EAAE,CAAC;oBACzB,aAAW,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;oBAC/B,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAW,CAAC,EAAE,CAAC;wBACzC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CACpC,UAAC,KAAK,IAAK,YAAK,KAAK,aAAW,EAArB,CAAqB,CACjC,CAAC;oBACJ,CAAC;oBACD,aAAW,CAAC,OAAO,EAAE,CAAC;gBACxB,CAAC;gBACD,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC1C,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACxB,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;OAIG;IACI,4CAAgB,GAAvB,UAAwB,IAAY,EAAE,KAAwB;;QAC5D,IAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACrC,IAAM,iBAAiB,GACrB,YAAY,CAAC,MAAM,GAAG,CAAC;YACrB,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAC5B,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,EAC9C,CAAC,CACF;YACH,CAAC,CAAC,IAAI,CAAC;QACX,OAAO,CACL,uBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,wBAAwB,CACzC,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,EACrC,KAAK,CACN,mCAAI,KAAK,CACX,CAAC;IACJ,CAAC;IAED;;;OAGG;IACI,mDAAuB,GAA9B;;QACE,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,UAAI,CAAC,gBAAgB,0CAAE,uBAAuB,EAAE,CAAC;IACnD,CAAC;IAED;;;OAGG;IACI,mDAAuB,GAA9B;;QACE,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,UAAI,CAAC,gBAAgB,0CAAE,uBAAuB,EAAE,CAAC;IACnD,CAAC;IAED,sBAAW,yCAAU;aAArB;;YACE,OAAO,CACL,WAAI,CAAC,gBAAgB,0CAAE,aAAa,GAAG,GAAG,CAAC,UAAC,IAAI,IAAK,qBAAM,IAAI,EAAG,EAAb,CAAa,CAAC,KAAI,EAAE,CAC1E,CAAC;QACJ,CAAC;;;OAAA;IAEM,2DAA+B,GAAtC;QACE,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAEM,mCAAO,GAAd;QAAA,iBAqBC;QApBC,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,wBAAwB,GAAG,EAAE,CAAC;YACnC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,UAAC,KAAK;gBACrC,KAAK,CAAC,OAAO,EAAE,CAAC;YAClB,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;YACjC,IAAM,QAAQ,qBAAO,IAAI,CAAC,SAAS,OAAC,CAAC;YACrC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;YAC1B,IAAM,OAAO,qBAAO,IAAI,CAAC,QAAQ,OAAC,CAAC;YACnC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;YACzB,QAAQ,CAAC,OAAO,CAAC,UAAC,KAAK;gBACrB,KAAK,CAAC,YAAY,CAAC,KAAI,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,OAAO,CAAC,UAAC,MAAM;gBACrB,MAAM,CAAC,4BAA4B,CAAC,KAAI,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IACH,wBAAC;AAAD,CAAC;;AAED;IAIE,gCAAY,QAAmC,EAAE,MAAyB;QAFhE,cAAS,GAAoB,EAAE,CAAC;QAGxC,IAAI,CAAC,uBAAuB,GAAG,QAAQ,CAAC;QACxC,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;IACjC,CAAC;IAEM,mCAAE,GAAT,UAAU,QAAuB;QAC/B,kFAAkF;QAClF,qDAAqD;QACrD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,uBAAuB,CAAC,YAAY,EAAE,CAAC;QAC9C,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9B,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IACM,oCAAG,GAAV,UAAW,QAAwB;QACjC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAC,EAAE,IAAK,SAAE,KAAK,QAAQ,EAAf,CAAe,CAAC,CAAC;QAClE,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IACM,uDAAsB,GAA7B,UAA8B,QAAkB,IAAG,CAAC;IAE7C,gDAAe,GAAtB;QAAA,iBAMC;QALC,IAAI,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,CAAC;YAC5C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAC,QAAQ;gBAC9B,KAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEM,6CAAY,GAAnB;QACE,IAAI,CAAC,uBAAuB,CAAC,YAAY,EAAE,CAAC;IAC9C,CAAC;IAEM,+CAAc,GAArB;QACE,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IAC5B,CAAC;IAED,sBAAW,wCAAI;aAAf;YACE,OAAO,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;QAC3C,CAAC;;;OAAA;IACH,6BAAC;AAAD,CAAC;;AAED;IAA6C,2CAAsB;IACjE,iCAAY,QAAoC,EAAE,MAAyB;QACzE,aAAK,YAAC,QAAQ,EAAE,MAAM,CAAC,SAAC;IAC1B,CAAC;IAED,sBAAW,0CAAK;aAAhB;YACE,OAAQ,IAAI,CAAC,uBAAsD,CAAC,KAAK,CAAC;QAC5E,CAAC;aAED,UAAiB,GAAW;YACzB,IAAI,CAAC,uBAAsD,CAAC,KAAK,GAAG,GAAG,CAAC;QAC3E,CAAC;;;OAJA;IAKM,wDAAsB,GAA7B,UAA8B,QAAkB;QAC9C,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IACH,8BAAC;AAAD,CAAC,CAf4C,sBAAsB,GAelE;;AAED;IAA6C,2CAAsB;IACjE,iCAAY,QAAoC,EAAE,MAAyB;QACzE,aAAK,YAAC,QAAQ,EAAE,MAAM,CAAC,SAAC;IAC1B,CAAC;IAED,sBAAW,0CAAK;aAAhB;YACE,OAAQ,IAAI,CAAC,uBAAsD,CAAC,KAAK,CAAC;QAC5E,CAAC;aAED,UAAiB,GAAW;YACzB,IAAI,CAAC,uBAAsD,CAAC,KAAK,GAAG,GAAG,CAAC;QAC3E,CAAC;;;OAJA;IAKM,wDAAsB,GAA7B,UAA8B,QAAkB;QAC9C,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IACH,8BAAC;AAAD,CAAC,CAf4C,sBAAsB,GAelE;;AAED;IAA8C,4CAAsB;IAClE,kCACE,QAAqC,EACrC,MAAyB;QAEzB,aAAK,YAAC,QAAQ,EAAE,MAAM,CAAC,SAAC;IAC1B,CAAC;IAED,sBAAW,2CAAK;aAAhB;YACE,OAAQ,IAAI,CAAC,uBAAuD,CAAC,KAAK,CAAC;QAC7E,CAAC;aAED,UAAiB,GAAY;YAC1B,IAAI,CAAC,uBAAuD,CAAC,KAAK,GAAG,GAAG,CAAC;QAC5E,CAAC;;;OAJA;IAKM,yDAAsB,GAA7B,UAA8B,QAAkB;QAC9C,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IACH,+BAAC;AAAD,CAAC,CAlB6C,sBAAsB,GAkBnE;;AAED;IAA8C,4CAAsB;IAClE,kCACE,QAAqC,EACrC,MAAyB;QAEzB,aAAK,YAAC,QAAQ,EAAE,MAAM,CAAC,SAAC;IAC1B,CAAC;IAEM,0CAAO,GAAd;QACE,OACE,IAAI,CAAC,uBACN,CAAC,OAAO,EAAE,CAAC;IACd,CAAC;IAEM,yDAAsB,GAA7B,UAA8B,QAAkB;QAC9C,QAAQ,EAAE,CAAC;IACb,CAAC;IACH,+BAAC;AAAD,CAAC,CAjB6C,sBAAsB,GAiBnE;;AAED;IAA2C,yCAAsB;IAC/D,+BAAY,QAAkC,EAAE,MAAyB;QACvE,aAAK,YAAC,QAAQ,EAAE,MAAM,CAAC,SAAC;IAC1B,CAAC;IAED,sBAAW,wCAAK;aAAhB;YACE,OAAQ,IAAI,CAAC,uBAAoD,CAAC,KAAK,CAAC;QAC1E,CAAC;aAED,UAAiB,GAAW;YACzB,IAAI,CAAC,uBAAoD,CAAC,KAAK,GAAG,GAAG,CAAC;QACzE,CAAC;;;OAJA;IAMD,sBAAW,6CAAU;aAIrB;YACE,OAAQ,IAAI,CAAC,uBAAoD;iBAC9D,UAAU,CAAC;QAChB,CAAC;aAPD,UAAsB,GAAW;YAC9B,IAAI,CAAC,uBAAoD,CAAC,UAAU,GAAG,GAAG,CAAC;QAC9E,CAAC;;;OAAA;IAOD,sBAAW,yCAAM;aAAjB;YACE,OAAQ,IAAI,CAAC,uBAAoD,CAAC,MAAM,CAAC;QAC3E,CAAC;;;OAAA;IAEM,sDAAsB,GAA7B,UAA8B,QAAkB;QAC9C,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IACH,4BAAC;AAAD,CAAC,CA7B0C,sBAAsB,GA6BhE;;AAED;IAA4C,0CAAsB;IAChE,gCAAY,QAAmC,EAAE,MAAyB;QACxE,aAAK,YAAC,QAAQ,EAAE,MAAM,CAAC,SAAC;IAC1B,CAAC;IAED,sBAAW,yCAAK;aAAhB;YACE,OAAQ,IAAI,CAAC,uBAAqD,CAAC,KAAK,CAAC;QAC3E,CAAC;aAED,UAAiB,GAAW;YACzB,IAAI,CAAC,uBAAqD,CAAC,KAAK,GAAG,GAAG,CAAC;QAC1E,CAAC;;;OAJA;IAMM,oCAAG,GAAV,UAAW,CAAS,EAAE,CAAS,EAAE,CAAS;QACvC,IAAI,CAAC,uBAAqD,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3E,CAAC;IAEM,qCAAI,GAAX,UAAY,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QACnD,IAAI,CAAC,uBAAqD,CAAC,IAAI,CAC9D,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,CACF,CAAC;IACJ,CAAC;IAEM,qCAAI,GAAX,UAAY,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QACnD,IAAI,CAAC,uBAAqD,CAAC,IAAI,CAC9D,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,CACF,CAAC;IACJ,CAAC;IAED,iBAAiB;IACV,sCAAK,GAAZ,UAAa,CAAS;QACnB,IAAI,CAAC,uBAAqD,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACvE,CAAC;IAED,eAAe;IACR,wCAAO,GAAd,UAAe,CAAS;QACrB,IAAI,CAAC,uBAAqD,CAAC,KAAK,CAC/D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAC9C,CAAC;IACJ,CAAC;IACM,uDAAsB,GAA7B,UAA8B,QAAkB;QAC9C,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IACH,6BAAC;AAAD,CAAC,CAjD2C,sBAAsB,GAiDjE;;AAmCD,wCAAwC;AACxC,IAAM,YAAY,GAAG,UAAO,GAAW;;;;;gBAC/B,GAAG,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC;gBACjB,qBAAM,KAAK,CAAC,GAAG,CAAC;;gBAAtB,GAAG,GAAG,SAAgB;gBACb,qBAAM,GAAG,CAAC,WAAW,EAAE;;gBAAhC,MAAM,GAAG,SAAuB;gBACtC,sBAAO,MAAM,EAAC;;;KACf,CAAC;AAEF,aAAa;AAEb,4BAA4B;AAE5B;;GAEG;AACH,IAAM,gBAAgB,GAAG,UAAC,GAAmC;IAC3D,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,CAAC;IACf,CAAC;SAAM,IAAI,GAAG,YAAY,KAAK,EAAE,CAAC;QAChC,OAAO,GAAG,CAAC;IACb,CAAC;IACD,0CAA0C;IAC1C,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AAEF,aAAa;AAEb,4BAA4B;AAE5B,oCAAoC;AAC7B,IAAM,OAAO,GAAG;IACrB,YAAY,EAAE,YAAY;IAC1B,gBAAgB,EAAE,gBAAgB;CACnC,CAAC;AAEF,aAAa;AAEb,wBAAwB;AAExB;;;;;GAKG;AACI,IAAM,WAAW,GAAG,UAAC,KAAiB;IAC3C,OAAO,IAAI,OAAO,CAAW,UAAC,OAAO;QACnC,oBAAa,CAAC,WAAW,CAAC,UAAC,IAAmB;YAC5C,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACnC,CAAC,CAAC;IAFF,CAEE,CACH,CAAC;AACJ,CAAC,CAAC;AAEF;;;;;GAKG;AACI,IAAM,WAAW,GAAG,UAAC,KAAiB;IAC3C,OAAO,IAAI,OAAO,CAAW,UAAC,OAAO;QACnC,oBAAa,CAAC,WAAW,CAAC,UAAC,IAAmB;YAC5C,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACnC,CAAC,CAAC;IAFF,CAEE,CACH,CAAC;AACJ,CAAC,CAAC;AAEF;;;;;GAKG;AACI,IAAM,UAAU,GAAG,UAAC,KAAiB;IAC1C,OAAO,IAAI,OAAO,CAAU,UAAC,OAAO;QAClC,oBAAa,CAAC,WAAW,CAAC,UAAC,IAAmB;YAC5C,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAClC,CAAC,CAAC;IAFF,CAEE,CACH,CAAC;AACJ,CAAC,CAAC;AAEF,aAAa", "sources": ["webpack://rive/webpack/universalModuleDefinition", "webpack://rive/./npm/canvas_advanced_lite/canvas_advanced.mjs", "webpack://rive/./src/animation/index.ts", "webpack://rive/./src/animation/Animation.ts", "webpack://rive/./src/utils/index.ts", "webpack://rive/./src/utils/registerTouchInteractions.ts", "webpack://rive/./src/utils/sanitizeUrl.ts", "webpack://rive/webpack/bootstrap", "webpack://rive/webpack/runtime/define property getters", "webpack://rive/webpack/runtime/hasOwnProperty shorthand", "webpack://rive/webpack/runtime/make namespace object", "webpack://rive/./src/rive.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"rive\"] = factory();\n\telse\n\t\troot[\"rive\"] = factory();\n})(this, () => {\nreturn ", "\nvar Rive = (() => {\n  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;\n  \n  return (\nfunction(moduleArg = {}) {\n\nvar h = moduleArg, aa, ba;\nh.ready = new Promise((b, a) => {\n  aa = b;\n  ba = a;\n});\nfunction ca() {\n  function b(m) {\n    const k = d;\n    c = a = 0;\n    d = new Map();\n    k.forEach(n => {\n      try {\n        n(m);\n      } catch (l) {\n        console.error(l);\n      }\n    });\n    this.xa();\n    e && e.Ta();\n  }\n  let a = 0, c = 0, d = new Map(), e = null, f = null;\n  this.requestAnimationFrame = function(m) {\n    a || (a = requestAnimationFrame(b.bind(this)));\n    const k = ++c;\n    d.set(k, m);\n    return k;\n  };\n  this.cancelAnimationFrame = function(m) {\n    d.delete(m);\n    a && 0 == d.size && (cancelAnimationFrame(a), a = 0);\n  };\n  this.Ra = function(m) {\n    f && (document.body.remove(f), f = null);\n    m || (f = document.createElement(\"div\"), f.style.backgroundColor = \"black\", f.style.position = \"fixed\", f.style.right = 0, f.style.top = 0, f.style.color = \"white\", f.style.padding = \"4px\", f.innerHTML = \"RIVE FPS\", m = function(k) {\n      f.innerHTML = \"RIVE FPS \" + k.toFixed(1);\n    }, document.body.appendChild(f));\n    e = new function() {\n      let k = 0, n = 0;\n      this.Ta = function() {\n        var l = performance.now();\n        n ? (++k, l -= n, 1000 < l && (m(1000 * k / l), k = n = 0)) : (n = l, k = 0);\n      };\n    }();\n  };\n  this.Oa = function() {\n    f && (document.body.remove(f), f = null);\n    e = null;\n  };\n  this.xa = function() {\n  };\n}\nfunction da(b) {\n  console.assert(!0);\n  const a = new Map();\n  let c = -Infinity;\n  this.push = function(d) {\n    d = d + ((1 << b) - 1) >> b;\n    a.has(d) && clearTimeout(a.get(d));\n    a.set(d, setTimeout(function() {\n      a.delete(d);\n      0 == a.length ? c = -Infinity : d == c && (c = Math.max(...a.keys()), console.assert(c < d));\n    }, 1000));\n    c = Math.max(d, c);\n    return c << b;\n  };\n}\nconst ea = h.onRuntimeInitialized;\nh.onRuntimeInitialized = function() {\n  ea && ea();\n  let b = h.decodeAudio;\n  h.decodeAudio = function(e, f) {\n    e = b(e);\n    f(e);\n  };\n  let a = h.decodeFont;\n  h.decodeFont = function(e, f) {\n    e = a(e);\n    f(e);\n  };\n  const c = h.FileAssetLoader;\n  h.ptrToAsset = e => {\n    let f = h.ptrToFileAsset(e);\n    return f.isImage ? h.ptrToImageAsset(e) : f.isFont ? h.ptrToFontAsset(e) : f.isAudio ? h.ptrToAudioAsset(e) : f;\n  };\n  h.CustomFileAssetLoader = c.extend(\"CustomFileAssetLoader\", {__construct:function({loadContents:e}) {\n    this.__parent.__construct.call(this);\n    this.Ha = e;\n  }, loadContents:function(e, f) {\n    e = h.ptrToAsset(e);\n    return this.Ha(e, f);\n  },});\n  h.CDNFileAssetLoader = c.extend(\"CDNFileAssetLoader\", {__construct:function() {\n    this.__parent.__construct.call(this);\n  }, loadContents:function(e) {\n    let f = h.ptrToAsset(e);\n    e = f.cdnUuid;\n    if (\"\" === e) {\n      return !1;\n    }\n    (function(m, k) {\n      var n = new XMLHttpRequest();\n      n.responseType = \"arraybuffer\";\n      n.onreadystatechange = function() {\n        4 == n.readyState && 200 == n.status && k(n);\n      };\n      n.open(\"GET\", m, !0);\n      n.send(null);\n    })(f.cdnBaseUrl + \"/\" + e, m => {\n      f.decode(new Uint8Array(m.response));\n    });\n    return !0;\n  },});\n  h.FallbackFileAssetLoader = c.extend(\"FallbackFileAssetLoader\", {__construct:function() {\n    this.__parent.__construct.call(this);\n    this.wa = [];\n  }, addLoader:function(e) {\n    this.wa.push(e);\n  }, loadContents:function(e, f) {\n    for (let m of this.wa) {\n      if (m.loadContents(e, f)) {\n        return !0;\n      }\n    }\n    return !1;\n  },});\n  let d = h.computeAlignment;\n  h.computeAlignment = function(e, f, m, k, n = 1.0) {\n    return d.call(this, e, f, m, k, n);\n  };\n};\nconst fa = \"createConicGradient createImageData createLinearGradient createPattern createRadialGradient getContextAttributes getImageData getLineDash getTransform isContextLost isPointInPath isPointInStroke measureText\".split(\" \"), ha = new function() {\n  function b() {\n    if (!a) {\n      var g = document.createElement(\"canvas\"), r = {alpha:1, depth:0, stencil:0, antialias:0, premultipliedAlpha:1, preserveDrawingBuffer:0, powerPreference:\"high-performance\", failIfMajorPerformanceCaveat:0, enableExtensionsByDefault:1, explicitSwapControl:1, renderViaOffscreenBackBuffer:1,};\n      let q;\n      if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) {\n        if (q = g.getContext(\"webgl\", r), c = 1, !q) {\n          return console.log(\"No WebGL support. Image mesh will not be drawn.\"), !1;\n        }\n      } else {\n        if (q = g.getContext(\"webgl2\", r)) {\n          c = 2;\n        } else {\n          if (q = g.getContext(\"webgl\", r)) {\n            c = 1;\n          } else {\n            return console.log(\"No WebGL support. Image mesh will not be drawn.\"), !1;\n          }\n        }\n      }\n      q = new Proxy(q, {get(D, v) {\n        if (D.isContextLost()) {\n          if (n || (console.error(\"Cannot render the mesh because the GL Context was lost. Tried to invoke \", v), n = !0), \"function\" === typeof D[v]) {\n            return function() {\n            };\n          }\n        } else {\n          return \"function\" === typeof D[v] ? function(...I) {\n            return D[v].apply(D, I);\n          } : D[v];\n        }\n      }, set(D, v, I) {\n        if (D.isContextLost()) {\n          n || (console.error(\"Cannot render the mesh because the GL Context was lost. Tried to set property \" + v), n = !0);\n        } else {\n          return D[v] = I, !0;\n        }\n      },});\n      d = Math.min(q.getParameter(q.MAX_RENDERBUFFER_SIZE), q.getParameter(q.MAX_TEXTURE_SIZE));\n      function G(D, v, I) {\n        v = q.createShader(v);\n        q.shaderSource(v, I);\n        q.compileShader(v);\n        I = q.getShaderInfoLog(v);\n        if (0 < (I || \"\").length) {\n          throw I;\n        }\n        q.attachShader(D, v);\n      }\n      g = q.createProgram();\n      G(g, q.VERTEX_SHADER, \"attribute vec2 vertex;\\n                attribute vec2 uv;\\n                uniform vec4 mat;\\n                uniform vec2 translate;\\n                varying vec2 st;\\n                void main() {\\n                    st = uv;\\n                    gl_Position = vec4(mat2(mat) * vertex + translate, 0, 1);\\n                }\");\n      G(g, q.FRAGMENT_SHADER, \"precision highp float;\\n                uniform sampler2D image;\\n                varying vec2 st;\\n                void main() {\\n                    gl_FragColor = texture2D(image, st);\\n                }\");\n      q.bindAttribLocation(g, 0, \"vertex\");\n      q.bindAttribLocation(g, 1, \"uv\");\n      q.linkProgram(g);\n      r = q.getProgramInfoLog(g);\n      if (0 < (r || \"\").trim().length) {\n        throw r;\n      }\n      e = q.getUniformLocation(g, \"mat\");\n      f = q.getUniformLocation(g, \"translate\");\n      q.useProgram(g);\n      q.bindBuffer(q.ARRAY_BUFFER, q.createBuffer());\n      q.enableVertexAttribArray(0);\n      q.enableVertexAttribArray(1);\n      q.bindBuffer(q.ELEMENT_ARRAY_BUFFER, q.createBuffer());\n      q.uniform1i(q.getUniformLocation(g, \"image\"), 0);\n      q.pixelStorei(q.UNPACK_PREMULTIPLY_ALPHA_WEBGL, !0);\n      a = q;\n    }\n    return !0;\n  }\n  let a = null, c = 0, d = 0, e = null, f = null, m = 0, k = 0, n = !1;\n  b();\n  this.eb = function() {\n    b();\n    return d;\n  };\n  this.Ma = function(g) {\n    a.deleteTexture && a.deleteTexture(g);\n  };\n  this.La = function(g) {\n    if (!b()) {\n      return null;\n    }\n    const r = a.createTexture();\n    if (!r) {\n      return null;\n    }\n    a.bindTexture(a.TEXTURE_2D, r);\n    a.texImage2D(a.TEXTURE_2D, 0, a.RGBA, a.RGBA, a.UNSIGNED_BYTE, g);\n    a.texParameteri(a.TEXTURE_2D, a.TEXTURE_WRAP_S, a.CLAMP_TO_EDGE);\n    a.texParameteri(a.TEXTURE_2D, a.TEXTURE_WRAP_T, a.CLAMP_TO_EDGE);\n    a.texParameteri(a.TEXTURE_2D, a.TEXTURE_MAG_FILTER, a.LINEAR);\n    2 == c ? (a.texParameteri(a.TEXTURE_2D, a.TEXTURE_MIN_FILTER, a.LINEAR_MIPMAP_LINEAR), a.generateMipmap(a.TEXTURE_2D)) : a.texParameteri(a.TEXTURE_2D, a.TEXTURE_MIN_FILTER, a.LINEAR);\n    return r;\n  };\n  const l = new da(8), t = new da(8), w = new da(10), x = new da(10);\n  this.Qa = function(g, r, q, G, D) {\n    if (b()) {\n      var v = l.push(g), I = t.push(r);\n      if (a.canvas) {\n        if (a.canvas.width != v || a.canvas.height != I) {\n          a.canvas.width = v, a.canvas.height = I;\n        }\n        a.viewport(0, I - r, g, r);\n        a.disable(a.SCISSOR_TEST);\n        a.clearColor(0, 0, 0, 0);\n        a.clear(a.COLOR_BUFFER_BIT);\n        a.enable(a.SCISSOR_TEST);\n        q.sort((C, X) => X.Ba - C.Ba);\n        v = w.push(G);\n        m != v && (a.bufferData(a.ARRAY_BUFFER, 8 * v, a.DYNAMIC_DRAW), m = v);\n        v = 0;\n        for (var O of q) {\n          a.bufferSubData(a.ARRAY_BUFFER, v, O.ia), v += 4 * O.ia.length;\n        }\n        console.assert(v == 4 * G);\n        for (var Y of q) {\n          a.bufferSubData(a.ARRAY_BUFFER, v, Y.Ea), v += 4 * Y.Ea.length;\n        }\n        console.assert(v == 8 * G);\n        v = x.push(D);\n        k != v && (a.bufferData(a.ELEMENT_ARRAY_BUFFER, 2 * v, a.DYNAMIC_DRAW), k = v);\n        O = 0;\n        for (var ia of q) {\n          a.bufferSubData(a.ELEMENT_ARRAY_BUFFER, O, ia.indices), O += 2 * ia.indices.length;\n        }\n        console.assert(O == 2 * D);\n        ia = 0;\n        Y = !0;\n        v = O = 0;\n        for (const C of q) {\n          C.image.da != ia && (a.bindTexture(a.TEXTURE_2D, C.image.ca || null), ia = C.image.da);\n          C.hb ? (a.scissor(C.na, I - C.oa - C.va, C.sb, C.va), Y = !0) : Y && (a.scissor(0, I - r, g, r), Y = !1);\n          q = 2 / g;\n          const X = -2 / r;\n          a.uniform4f(e, C.N[0] * q * C.X, C.N[1] * X * C.Y, C.N[2] * q * C.X, C.N[3] * X * C.Y);\n          a.uniform2f(f, C.N[4] * q * C.X + q * (C.na - C.fb * C.X) - 1, C.N[5] * X * C.Y + X * (C.oa - C.gb * C.Y) + 1);\n          a.vertexAttribPointer(0, 2, a.FLOAT, !1, 0, v);\n          a.vertexAttribPointer(1, 2, a.FLOAT, !1, 0, v + 4 * G);\n          a.drawElements(a.TRIANGLES, C.indices.length, a.UNSIGNED_SHORT, O);\n          v += 4 * C.ia.length;\n          O += 2 * C.indices.length;\n        }\n        console.assert(v == 4 * G);\n        console.assert(O == 2 * D);\n      }\n    }\n  };\n  this.canvas = function() {\n    return b() && a.canvas;\n  };\n}(), la = h.onRuntimeInitialized;\nh.onRuntimeInitialized = function() {\n  function b(p) {\n    switch(p) {\n      case l.srcOver:\n        return \"source-over\";\n      case l.screen:\n        return \"screen\";\n      case l.overlay:\n        return \"overlay\";\n      case l.darken:\n        return \"darken\";\n      case l.lighten:\n        return \"lighten\";\n      case l.colorDodge:\n        return \"color-dodge\";\n      case l.colorBurn:\n        return \"color-burn\";\n      case l.hardLight:\n        return \"hard-light\";\n      case l.softLight:\n        return \"soft-light\";\n      case l.difference:\n        return \"difference\";\n      case l.exclusion:\n        return \"exclusion\";\n      case l.multiply:\n        return \"multiply\";\n      case l.hue:\n        return \"hue\";\n      case l.saturation:\n        return \"saturation\";\n      case l.color:\n        return \"color\";\n      case l.luminosity:\n        return \"luminosity\";\n    }\n  }\n  function a(p) {\n    return \"rgba(\" + ((16711680 & p) >>> 16) + \",\" + ((65280 & p) >>> 8) + \",\" + ((255 & p) >>> 0) + \",\" + ((4278190080 & p) >>> 24) / 255 + \")\";\n  }\n  function c() {\n    0 < I.length && (ha.Qa(v.drawWidth(), v.drawHeight(), I, O, Y), I = [], Y = O = 0, v.reset(512, 512));\n    for (const p of D) {\n      for (const u of p.u) {\n        u();\n      }\n      p.u = [];\n    }\n    D.clear();\n  }\n  la && la();\n  var d = h.RenderPaintStyle;\n  const e = h.RenderPath, f = h.RenderPaint, m = h.Renderer, k = h.StrokeCap, n = h.StrokeJoin, l = h.BlendMode, t = d.fill, w = d.stroke, x = h.FillRule.evenOdd;\n  let g = 1;\n  var r = h.RenderImage.extend(\"CanvasRenderImage\", {__construct:function({R:p, W:u} = {}) {\n    this.__parent.__construct.call(this);\n    this.da = g;\n    g = g + 1 & 2147483647 || 1;\n    this.R = p;\n    this.W = u;\n  }, __destruct:function() {\n    this.ca && (ha.Ma(this.ca), URL.revokeObjectURL(this.la));\n    this.__parent.__destruct.call(this);\n  }, decode:function(p) {\n    var u = this;\n    u.W && u.W(u);\n    var A = new Image();\n    u.la = URL.createObjectURL(new Blob([p], {type:\"image/png\",}));\n    A.onload = function() {\n      u.Ga = A;\n      u.ca = ha.La(A);\n      u.size(A.width, A.height);\n      u.R && u.R(u);\n    };\n    A.src = u.la;\n  },}), q = e.extend(\"CanvasRenderPath\", {__construct:function() {\n    this.__parent.__construct.call(this);\n    this.F = new Path2D();\n  }, rewind:function() {\n    this.F = new Path2D();\n  }, addPath:function(p, u, A, B, y, E, F) {\n    var H = this.F, T = H.addPath;\n    p = p.F;\n    const K = new DOMMatrix();\n    K.a = u;\n    K.b = A;\n    K.c = B;\n    K.d = y;\n    K.e = E;\n    K.f = F;\n    T.call(H, p, K);\n  }, fillRule:function(p) {\n    this.ka = p;\n  }, moveTo:function(p, u) {\n    this.F.moveTo(p, u);\n  }, lineTo:function(p, u) {\n    this.F.lineTo(p, u);\n  }, cubicTo:function(p, u, A, B, y, E) {\n    this.F.bezierCurveTo(p, u, A, B, y, E);\n  }, close:function() {\n    this.F.closePath();\n  },}), G = f.extend(\"CanvasRenderPaint\", {color:function(p) {\n    this.ma = a(p);\n  }, thickness:function(p) {\n    this.Ja = p;\n  }, join:function(p) {\n    switch(p) {\n      case n.miter:\n        this.ba = \"miter\";\n        break;\n      case n.round:\n        this.ba = \"round\";\n        break;\n      case n.bevel:\n        this.ba = \"bevel\";\n    }\n  }, cap:function(p) {\n    switch(p) {\n      case k.butt:\n        this.aa = \"butt\";\n        break;\n      case k.round:\n        this.aa = \"round\";\n        break;\n      case k.square:\n        this.aa = \"square\";\n    }\n  }, style:function(p) {\n    this.Ia = p;\n  }, blendMode:function(p) {\n    this.Fa = b(p);\n  }, clearGradient:function() {\n    this.P = null;\n  }, linearGradient:function(p, u, A, B) {\n    this.P = {Ca:p, Da:u, qa:A, ra:B, ga:[],};\n  }, radialGradient:function(p, u, A, B) {\n    this.P = {Ca:p, Da:u, qa:A, ra:B, ga:[], bb:!0,};\n  }, addStop:function(p, u) {\n    this.P.ga.push({color:p, stop:u,});\n  }, completeGradient:function() {\n  }, draw:function(p, u, A) {\n    let B = this.Ia;\n    var y = this.ma, E = this.P;\n    p.globalCompositeOperation = this.Fa;\n    if (null != E) {\n      y = E.Ca;\n      var F = E.Da;\n      const T = E.qa;\n      var H = E.ra;\n      const K = E.ga;\n      E.bb ? (E = T - y, H -= F, y = p.createRadialGradient(y, F, 0, y, F, Math.sqrt(E * E + H * H))) : y = p.createLinearGradient(y, F, T, H);\n      for (let Z = 0, N = K.length; Z < N; Z++) {\n        F = K[Z], y.addColorStop(F.stop, a(F.color));\n      }\n      this.ma = y;\n      this.P = null;\n    }\n    switch(B) {\n      case w:\n        p.strokeStyle = y;\n        p.lineWidth = this.Ja;\n        p.lineCap = this.aa;\n        p.lineJoin = this.ba;\n        p.stroke(u);\n        break;\n      case t:\n        p.fillStyle = y, p.fill(u, A);\n    }\n  },});\n  const D = new Set();\n  let v = null, I = [], O = 0, Y = 0;\n  var ia = h.CanvasRenderer = m.extend(\"Renderer\", {__construct:function(p) {\n    this.__parent.__construct.call(this);\n    this.D = [1, 0, 0, 1, 0, 0];\n    this.o = p.getContext(\"2d\");\n    this.ja = p;\n    this.u = [];\n  }, save:function() {\n    this.D.push(...this.D.slice(this.D.length - 6));\n    this.u.push(this.o.save.bind(this.o));\n  }, restore:function() {\n    const p = this.D.length - 6;\n    if (6 > p) {\n      throw \"restore() called without matching save().\";\n    }\n    this.D.splice(p);\n    this.u.push(this.o.restore.bind(this.o));\n  }, transform:function(p, u, A, B, y, E) {\n    const F = this.D, H = F.length - 6;\n    F.splice(H, 6, F[H] * p + F[H + 2] * u, F[H + 1] * p + F[H + 3] * u, F[H] * A + F[H + 2] * B, F[H + 1] * A + F[H + 3] * B, F[H] * y + F[H + 2] * E + F[H + 4], F[H + 1] * y + F[H + 3] * E + F[H + 5]);\n    this.u.push(this.o.transform.bind(this.o, p, u, A, B, y, E));\n  }, rotate:function(p) {\n    const u = Math.sin(p);\n    p = Math.cos(p);\n    this.transform(p, u, -u, p, 0, 0);\n  }, _drawPath:function(p, u) {\n    this.u.push(u.draw.bind(u, this.o, p.F, p.ka === x ? \"evenodd\" : \"nonzero\"));\n  }, _drawRiveImage:function(p, u, A) {\n    var B = p.Ga;\n    if (B) {\n      var y = this.o, E = b(u);\n      this.u.push(function() {\n        y.globalCompositeOperation = E;\n        y.globalAlpha = A;\n        y.drawImage(B, 0, 0);\n        y.globalAlpha = 1;\n      });\n    }\n  }, _getMatrix:function(p) {\n    const u = this.D, A = u.length - 6;\n    for (let B = 0; 6 > B; ++B) {\n      p[B] = u[A + B];\n    }\n  }, _drawImageMesh:function(p, u, A, B, y, E, F, H, T, K) {\n    var Z = this.o.canvas.width, N = this.o.canvas.height;\n    const tb = T - F, ub = K - H;\n    F = Math.max(F, 0);\n    H = Math.max(H, 0);\n    T = Math.min(T, Z);\n    K = Math.min(K, N);\n    const wa = T - F, xa = K - H;\n    console.assert(wa <= Math.min(tb, Z));\n    console.assert(xa <= Math.min(ub, N));\n    if (!(0 >= wa || 0 >= xa)) {\n      T = wa < tb || xa < ub;\n      Z = K = 1;\n      var ja = Math.ceil(wa * K), ka = Math.ceil(xa * Z);\n      N = ha.eb();\n      ja > N && (K *= N / ja, ja = N);\n      ka > N && (Z *= N / ka, ka = N);\n      v || (v = new h.DynamicRectanizer(N), v.reset(512, 512));\n      N = v.addRect(ja, ka);\n      0 > N && (c(), D.add(this), N = v.addRect(ja, ka), console.assert(0 <= N));\n      var vb = N & 65535, wb = N >> 16;\n      I.push({N:this.D.slice(this.D.length - 6), image:p, na:vb, oa:wb, fb:F, gb:H, sb:ja, va:ka, X:K, Y:Z, ia:new Float32Array(B), Ea:new Float32Array(y), indices:new Uint16Array(E), hb:T, Ba:p.da << 1 | (T ? 1 : 0),});\n      O += B.length;\n      Y += E.length;\n      var pa = this.o, oc = b(u);\n      this.u.push(function() {\n        pa.save();\n        pa.resetTransform();\n        pa.globalCompositeOperation = oc;\n        pa.globalAlpha = A;\n        const xb = ha.canvas();\n        xb && pa.drawImage(xb, vb, wb, ja, ka, F, H, wa, xa);\n        pa.restore();\n      });\n    }\n  }, _clipPath:function(p) {\n    this.u.push(this.o.clip.bind(this.o, p.F, p.ka === x ? \"evenodd\" : \"nonzero\"));\n  }, clear:function() {\n    D.add(this);\n    this.u.push(this.o.clearRect.bind(this.o, 0, 0, this.ja.width, this.ja.height));\n  }, flush:function() {\n  }, translate:function(p, u) {\n    this.transform(1, 0, 0, 1, p, u);\n  },});\n  h.makeRenderer = function(p) {\n    const u = new ia(p), A = u.o;\n    return new Proxy(u, {get(B, y) {\n      if (\"function\" === typeof B[y]) {\n        return function(...E) {\n          return B[y].apply(B, E);\n        };\n      }\n      if (\"function\" === typeof A[y]) {\n        if (-1 < fa.indexOf(y)) {\n          throw Error(\"RiveException: Method call to '\" + y + \"()' is not allowed, as the renderer cannot immediately pass through the return                 values of any canvas 2d context methods.\");\n        }\n        return function(...E) {\n          u.u.push(A[y].bind(A, ...E));\n        };\n      }\n      return B[y];\n    }, set(B, y, E) {\n      if (y in A) {\n        return u.u.push(() => {\n          A[y] = E;\n        }), !0;\n      }\n    },});\n  };\n  h.decodeImage = function(p, u) {\n    (new r({R:u})).decode(p);\n  };\n  h.renderFactory = {makeRenderPaint:function() {\n    return new G();\n  }, makeRenderPath:function() {\n    return new q();\n  }, makeRenderImage:function() {\n    let p = X;\n    return new r({W:() => {\n      p.total++;\n    }, R:() => {\n      p.loaded++;\n      if (p.loaded === p.total) {\n        const u = p.ready;\n        u && (u(), p.ready = null);\n      }\n    },});\n  },};\n  let C = h.load, X = null;\n  h.load = function(p, u, A = !0) {\n    const B = new h.FallbackFileAssetLoader();\n    void 0 !== u && B.addLoader(u);\n    A && (u = new h.CDNFileAssetLoader(), B.addLoader(u));\n    return new Promise(function(y) {\n      let E = null;\n      X = {total:0, loaded:0, ready:function() {\n        y(E);\n      },};\n      E = C(p, B);\n      0 == X.total && y(E);\n    });\n  };\n  let pc = h.RendererWrapper.prototype.align;\n  h.RendererWrapper.prototype.align = function(p, u, A, B, y = 1.0) {\n    pc.call(this, p, u, A, B, y);\n  };\n  d = new ca();\n  h.requestAnimationFrame = d.requestAnimationFrame.bind(d);\n  h.cancelAnimationFrame = d.cancelAnimationFrame.bind(d);\n  h.enableFPSCounter = d.Ra.bind(d);\n  h.disableFPSCounter = d.Oa;\n  d.xa = c;\n  h.resolveAnimationFrame = c;\n  h.cleanup = function() {\n    v && v.delete();\n  };\n};\nvar ma = Object.assign({}, h), na = \"./this.program\", oa = \"object\" == typeof window, qa = \"function\" == typeof importScripts, ra = \"\", sa, ta;\nif (oa || qa) {\n  qa ? ra = self.location.href : \"undefined\" != typeof document && document.currentScript && (ra = document.currentScript.src), _scriptDir && (ra = _scriptDir), 0 !== ra.indexOf(\"blob:\") ? ra = ra.substr(0, ra.replace(/[?#].*/, \"\").lastIndexOf(\"/\") + 1) : ra = \"\", qa && (ta = b => {\n    var a = new XMLHttpRequest();\n    a.open(\"GET\", b, !1);\n    a.responseType = \"arraybuffer\";\n    a.send(null);\n    return new Uint8Array(a.response);\n  }), sa = (b, a, c) => {\n    var d = new XMLHttpRequest();\n    d.open(\"GET\", b, !0);\n    d.responseType = \"arraybuffer\";\n    d.onload = () => {\n      200 == d.status || 0 == d.status && d.response ? a(d.response) : c();\n    };\n    d.onerror = c;\n    d.send(null);\n  };\n}\nvar ua = h.print || console.log.bind(console), va = h.printErr || console.error.bind(console);\nObject.assign(h, ma);\nma = null;\nh.thisProgram && (na = h.thisProgram);\nvar ya;\nh.wasmBinary && (ya = h.wasmBinary);\nvar noExitRuntime = h.noExitRuntime || !0;\n\"object\" != typeof WebAssembly && za(\"no native wasm support detected\");\nvar Aa, z, Ba = !1, Ca, J, Da, Ea, L, M, Fa, Ga;\nfunction Ha() {\n  var b = Aa.buffer;\n  h.HEAP8 = Ca = new Int8Array(b);\n  h.HEAP16 = Da = new Int16Array(b);\n  h.HEAP32 = L = new Int32Array(b);\n  h.HEAPU8 = J = new Uint8Array(b);\n  h.HEAPU16 = Ea = new Uint16Array(b);\n  h.HEAPU32 = M = new Uint32Array(b);\n  h.HEAPF32 = Fa = new Float32Array(b);\n  h.HEAPF64 = Ga = new Float64Array(b);\n}\nvar Ia, Ja = [], Ka = [], La = [];\nfunction Ma() {\n  var b = h.preRun.shift();\n  Ja.unshift(b);\n}\nvar Na = 0, Oa = null, Pa = null;\nfunction za(b) {\n  if (h.onAbort) {\n    h.onAbort(b);\n  }\n  b = \"Aborted(\" + b + \")\";\n  va(b);\n  Ba = !0;\n  b = new WebAssembly.RuntimeError(b + \". Build with -sASSERTIONS for more info.\");\n  ba(b);\n  throw b;\n}\nfunction Qa(b) {\n  return b.startsWith(\"data:application/octet-stream;base64,\");\n}\nvar Ra;\nRa = \"canvas_advanced.wasm\";\nif (!Qa(Ra)) {\n  var Sa = Ra;\n  Ra = h.locateFile ? h.locateFile(Sa, ra) : ra + Sa;\n}\nfunction Ta(b) {\n  if (b == Ra && ya) {\n    return new Uint8Array(ya);\n  }\n  if (ta) {\n    return ta(b);\n  }\n  throw \"both async and sync fetching of the wasm failed\";\n}\nfunction Ua(b) {\n  if (!ya && (oa || qa)) {\n    if (\"function\" == typeof fetch && !b.startsWith(\"file://\")) {\n      return fetch(b, {credentials:\"same-origin\"}).then(a => {\n        if (!a.ok) {\n          throw \"failed to load wasm binary file at '\" + b + \"'\";\n        }\n        return a.arrayBuffer();\n      }).catch(() => Ta(b));\n    }\n    if (sa) {\n      return new Promise((a, c) => {\n        sa(b, d => a(new Uint8Array(d)), c);\n      });\n    }\n  }\n  return Promise.resolve().then(() => Ta(b));\n}\nfunction Va(b, a, c) {\n  return Ua(b).then(d => WebAssembly.instantiate(d, a)).then(d => d).then(c, d => {\n    va(\"failed to asynchronously prepare wasm: \" + d);\n    za(d);\n  });\n}\nfunction Wa(b, a) {\n  var c = Ra;\n  return ya || \"function\" != typeof WebAssembly.instantiateStreaming || Qa(c) || c.startsWith(\"file://\") || \"function\" != typeof fetch ? Va(c, b, a) : fetch(c, {credentials:\"same-origin\"}).then(d => WebAssembly.instantiateStreaming(d, b).then(a, function(e) {\n    va(\"wasm streaming compile failed: \" + e);\n    va(\"falling back to ArrayBuffer instantiation\");\n    return Va(c, b, a);\n  }));\n}\nvar Xa = b => {\n  for (; 0 < b.length;) {\n    b.shift()(h);\n  }\n};\nfunction Ya(b) {\n  if (void 0 === b) {\n    return \"_unknown\";\n  }\n  b = b.replace(/[^a-zA-Z0-9_]/g, \"$\");\n  var a = b.charCodeAt(0);\n  return 48 <= a && 57 >= a ? `_${b}` : b;\n}\nfunction Za(b, a) {\n  b = Ya(b);\n  return {[b]:function() {\n    return a.apply(this, arguments);\n  }}[b];\n}\nfunction $a() {\n  this.G = [void 0];\n  this.ta = [];\n}\nvar P = new $a(), ab = void 0;\nfunction Q(b) {\n  throw new ab(b);\n}\nvar R = b => {\n  b || Q(\"Cannot use deleted val. handle = \" + b);\n  return P.get(b).value;\n}, S = b => {\n  switch(b) {\n    case void 0:\n      return 1;\n    case null:\n      return 2;\n    case !0:\n      return 3;\n    case !1:\n      return 4;\n    default:\n      return P.Za({Aa:1, value:b});\n  }\n};\nfunction bb(b) {\n  var a = Error, c = Za(b, function(d) {\n    this.name = b;\n    this.message = d;\n    d = Error(d).stack;\n    void 0 !== d && (this.stack = this.toString() + \"\\n\" + d.replace(/^Error(:[^\\n]*)?\\n/, \"\"));\n  });\n  c.prototype = Object.create(a.prototype);\n  c.prototype.constructor = c;\n  c.prototype.toString = function() {\n    return void 0 === this.message ? this.name : `${this.name}: ${this.message}`;\n  };\n  return c;\n}\nvar cb = void 0, db = void 0;\nfunction U(b) {\n  for (var a = \"\"; J[b];) {\n    a += db[J[b++]];\n  }\n  return a;\n}\nvar eb = [];\nfunction fb() {\n  for (; eb.length;) {\n    var b = eb.pop();\n    b.g.M = !1;\n    b[\"delete\"]();\n  }\n}\nvar gb = void 0, hb = {};\nfunction ib(b, a) {\n  for (void 0 === a && Q(\"ptr should not be undefined\"); b.l;) {\n    a = b.S(a), b = b.l;\n  }\n  return a;\n}\nvar jb = {};\nfunction kb(b) {\n  b = lb(b);\n  var a = U(b);\n  mb(b);\n  return a;\n}\nfunction nb(b, a) {\n  var c = jb[b];\n  void 0 === c && Q(a + \" has unknown type \" + kb(b));\n  return c;\n}\nfunction ob() {\n}\nvar pb = !1;\nfunction qb(b) {\n  --b.count.value;\n  0 === b.count.value && (b.s ? b.A.H(b.s) : b.j.h.H(b.i));\n}\nfunction rb(b, a, c) {\n  if (a === c) {\n    return b;\n  }\n  if (void 0 === c.l) {\n    return null;\n  }\n  b = rb(b, a, c.l);\n  return null === b ? null : c.Pa(b);\n}\nvar sb = {};\nfunction yb(b, a) {\n  a = ib(b, a);\n  return hb[a];\n}\nvar zb = void 0;\nfunction Ab(b) {\n  throw new zb(b);\n}\nfunction Bb(b, a) {\n  a.j && a.i || Ab(\"makeClassHandle requires ptr and ptrType\");\n  !!a.A !== !!a.s && Ab(\"Both smartPtrType and smartPtr must be specified\");\n  a.count = {value:1};\n  return Cb(Object.create(b, {g:{value:a,},}));\n}\nfunction Cb(b) {\n  if (\"undefined\" === typeof FinalizationRegistry) {\n    return Cb = a => a, b;\n  }\n  pb = new FinalizationRegistry(a => {\n    qb(a.g);\n  });\n  Cb = a => {\n    var c = a.g;\n    c.s && pb.register(a, {g:c}, a);\n    return a;\n  };\n  ob = a => {\n    pb.unregister(a);\n  };\n  return Cb(b);\n}\nvar Db = {};\nfunction Eb(b) {\n  for (; b.length;) {\n    var a = b.pop();\n    b.pop()(a);\n  }\n}\nfunction Fb(b) {\n  return this.fromWireType(L[b >> 2]);\n}\nvar Gb = {}, Hb = {};\nfunction V(b, a, c) {\n  function d(k) {\n    k = c(k);\n    k.length !== b.length && Ab(\"Mismatched type converter count\");\n    for (var n = 0; n < b.length; ++n) {\n      Ib(b[n], k[n]);\n    }\n  }\n  b.forEach(function(k) {\n    Hb[k] = a;\n  });\n  var e = Array(a.length), f = [], m = 0;\n  a.forEach((k, n) => {\n    jb.hasOwnProperty(k) ? e[n] = jb[k] : (f.push(k), Gb.hasOwnProperty(k) || (Gb[k] = []), Gb[k].push(() => {\n      e[n] = jb[k];\n      ++m;\n      m === f.length && d(e);\n    }));\n  });\n  0 === f.length && d(e);\n}\nfunction Jb(b) {\n  switch(b) {\n    case 1:\n      return 0;\n    case 2:\n      return 1;\n    case 4:\n      return 2;\n    case 8:\n      return 3;\n    default:\n      throw new TypeError(`Unknown type size: ${b}`);\n  }\n}\nfunction Kb(b, a, c = {}) {\n  var d = a.name;\n  b || Q(`type \"${d}\" must have a positive integer typeid pointer`);\n  if (jb.hasOwnProperty(b)) {\n    if (c.ab) {\n      return;\n    }\n    Q(`Cannot register type '${d}' twice`);\n  }\n  jb[b] = a;\n  delete Hb[b];\n  Gb.hasOwnProperty(b) && (a = Gb[b], delete Gb[b], a.forEach(e => e()));\n}\nfunction Ib(b, a, c = {}) {\n  if (!(\"argPackAdvance\" in a)) {\n    throw new TypeError(\"registerType registeredInstance requires argPackAdvance\");\n  }\n  Kb(b, a, c);\n}\nfunction Lb(b) {\n  Q(b.g.j.h.name + \" instance already deleted\");\n}\nfunction Mb() {\n}\nfunction Nb(b, a, c) {\n  if (void 0 === b[a].m) {\n    var d = b[a];\n    b[a] = function() {\n      b[a].m.hasOwnProperty(arguments.length) || Q(`Function '${c}' called with an invalid number of arguments (${arguments.length}) - expects one of (${b[a].m})!`);\n      return b[a].m[arguments.length].apply(this, arguments);\n    };\n    b[a].m = [];\n    b[a].m[d.L] = d;\n  }\n}\nfunction Ob(b, a, c) {\n  h.hasOwnProperty(b) ? ((void 0 === c || void 0 !== h[b].m && void 0 !== h[b].m[c]) && Q(`Cannot register public name '${b}' twice`), Nb(h, b, b), h.hasOwnProperty(c) && Q(`Cannot register multiple overloads of a function with the same number of arguments (${c})!`), h[b].m[c] = a) : (h[b] = a, void 0 !== c && (h[b].tb = c));\n}\nfunction Pb(b, a, c, d, e, f, m, k) {\n  this.name = b;\n  this.constructor = a;\n  this.B = c;\n  this.H = d;\n  this.l = e;\n  this.Ua = f;\n  this.S = m;\n  this.Pa = k;\n  this.ya = [];\n}\nfunction Qb(b, a, c) {\n  for (; a !== c;) {\n    a.S || Q(`Expected null or instance of ${c.name}, got an instance of ${a.name}`), b = a.S(b), a = a.l;\n  }\n  return b;\n}\nfunction Rb(b, a) {\n  if (null === a) {\n    return this.ea && Q(`null is not a valid ${this.name}`), 0;\n  }\n  a.g || Q(`Cannot pass \"${Sb(a)}\" as a ${this.name}`);\n  a.g.i || Q(`Cannot pass deleted object as a pointer of type ${this.name}`);\n  return Qb(a.g.i, a.g.j.h, this.h);\n}\nfunction Tb(b, a) {\n  if (null === a) {\n    this.ea && Q(`null is not a valid ${this.name}`);\n    if (this.V) {\n      var c = this.fa();\n      null !== b && b.push(this.H, c);\n      return c;\n    }\n    return 0;\n  }\n  a.g || Q(`Cannot pass \"${Sb(a)}\" as a ${this.name}`);\n  a.g.i || Q(`Cannot pass deleted object as a pointer of type ${this.name}`);\n  !this.U && a.g.j.U && Q(`Cannot convert argument of type ${a.g.A ? a.g.A.name : a.g.j.name} to parameter type ${this.name}`);\n  c = Qb(a.g.i, a.g.j.h, this.h);\n  if (this.V) {\n    switch(void 0 === a.g.s && Q(\"Passing raw pointer to smart pointer is illegal\"), this.nb) {\n      case 0:\n        a.g.A === this ? c = a.g.s : Q(`Cannot convert argument of type ${a.g.A ? a.g.A.name : a.g.j.name} to parameter type ${this.name}`);\n        break;\n      case 1:\n        c = a.g.s;\n        break;\n      case 2:\n        if (a.g.A === this) {\n          c = a.g.s;\n        } else {\n          var d = a.clone();\n          c = this.jb(c, S(function() {\n            d[\"delete\"]();\n          }));\n          null !== b && b.push(this.H, c);\n        }\n        break;\n      default:\n        Q(\"Unsupporting sharing policy\");\n    }\n  }\n  return c;\n}\nfunction Ub(b, a) {\n  if (null === a) {\n    return this.ea && Q(`null is not a valid ${this.name}`), 0;\n  }\n  a.g || Q(`Cannot pass \"${Sb(a)}\" as a ${this.name}`);\n  a.g.i || Q(`Cannot pass deleted object as a pointer of type ${this.name}`);\n  a.g.j.U && Q(`Cannot convert argument of type ${a.g.j.name} to parameter type ${this.name}`);\n  return Qb(a.g.i, a.g.j.h, this.h);\n}\nfunction Vb(b, a, c, d) {\n  this.name = b;\n  this.h = a;\n  this.ea = c;\n  this.U = d;\n  this.V = !1;\n  this.H = this.jb = this.fa = this.za = this.nb = this.ib = void 0;\n  void 0 !== a.l ? this.toWireType = Tb : (this.toWireType = d ? Rb : Ub, this.v = null);\n}\nfunction Wb(b, a, c) {\n  h.hasOwnProperty(b) || Ab(\"Replacing nonexistant public symbol\");\n  void 0 !== h[b].m && void 0 !== c ? h[b].m[c] = a : (h[b] = a, h[b].L = c);\n}\nvar Xb = [], Yb = b => {\n  var a = Xb[b];\n  a || (b >= Xb.length && (Xb.length = b + 1), Xb[b] = a = Ia.get(b));\n  return a;\n}, Zb = (b, a) => {\n  var c = [];\n  return function() {\n    c.length = 0;\n    Object.assign(c, arguments);\n    if (b.includes(\"j\")) {\n      var d = h[\"dynCall_\" + b];\n      d = c && c.length ? d.apply(null, [a].concat(c)) : d.call(null, a);\n    } else {\n      d = Yb(a).apply(null, c);\n    }\n    return d;\n  };\n};\nfunction W(b, a) {\n  b = U(b);\n  var c = b.includes(\"j\") ? Zb(b, a) : Yb(a);\n  \"function\" != typeof c && Q(`unknown function pointer with signature ${b}: ${a}`);\n  return c;\n}\nvar $b = void 0;\nfunction ac(b, a) {\n  function c(f) {\n    e[f] || jb[f] || (Hb[f] ? Hb[f].forEach(c) : (d.push(f), e[f] = !0));\n  }\n  var d = [], e = {};\n  a.forEach(c);\n  throw new $b(`${b}: ` + d.map(kb).join([\", \"]));\n}\nfunction bc(b, a, c, d, e) {\n  var f = a.length;\n  2 > f && Q(\"argTypes array size mismatch! Must at least get return value and 'this' types!\");\n  var m = null !== a[1] && null !== c, k = !1;\n  for (c = 1; c < a.length; ++c) {\n    if (null !== a[c] && void 0 === a[c].v) {\n      k = !0;\n      break;\n    }\n  }\n  var n = \"void\" !== a[0].name, l = f - 2, t = Array(l), w = [], x = [];\n  return function() {\n    arguments.length !== l && Q(`function ${b} called with ${arguments.length} arguments, expected ${l} args!`);\n    x.length = 0;\n    w.length = m ? 2 : 1;\n    w[0] = e;\n    if (m) {\n      var g = a[1].toWireType(x, this);\n      w[1] = g;\n    }\n    for (var r = 0; r < l; ++r) {\n      t[r] = a[r + 2].toWireType(x, arguments[r]), w.push(t[r]);\n    }\n    r = d.apply(null, w);\n    if (k) {\n      Eb(x);\n    } else {\n      for (var q = m ? 1 : 2; q < a.length; q++) {\n        var G = 1 === q ? g : t[q - 2];\n        null !== a[q].v && a[q].v(G);\n      }\n    }\n    g = n ? a[0].fromWireType(r) : void 0;\n    return g;\n  };\n}\nfunction cc(b, a) {\n  for (var c = [], d = 0; d < b; d++) {\n    c.push(M[a + 4 * d >> 2]);\n  }\n  return c;\n}\nfunction dc(b, a, c) {\n  b instanceof Object || Q(`${c} with invalid \"this\": ${b}`);\n  b instanceof a.h.constructor || Q(`${c} incompatible with \"this\" of type ${b.constructor.name}`);\n  b.g.i || Q(`cannot call emscripten binding method ${c} on deleted object`);\n  return Qb(b.g.i, b.g.j.h, a.h);\n}\nfunction ec(b) {\n  b >= P.ua && 0 === --P.get(b).Aa && P.$a(b);\n}\nfunction fc(b, a, c) {\n  switch(a) {\n    case 0:\n      return function(d) {\n        return this.fromWireType((c ? Ca : J)[d]);\n      };\n    case 1:\n      return function(d) {\n        return this.fromWireType((c ? Da : Ea)[d >> 1]);\n      };\n    case 2:\n      return function(d) {\n        return this.fromWireType((c ? L : M)[d >> 2]);\n      };\n    default:\n      throw new TypeError(\"Unknown integer type: \" + b);\n  }\n}\nfunction Sb(b) {\n  if (null === b) {\n    return \"null\";\n  }\n  var a = typeof b;\n  return \"object\" === a || \"array\" === a || \"function\" === a ? b.toString() : \"\" + b;\n}\nfunction gc(b, a) {\n  switch(a) {\n    case 2:\n      return function(c) {\n        return this.fromWireType(Fa[c >> 2]);\n      };\n    case 3:\n      return function(c) {\n        return this.fromWireType(Ga[c >> 3]);\n      };\n    default:\n      throw new TypeError(\"Unknown float type: \" + b);\n  }\n}\nfunction hc(b, a, c) {\n  switch(a) {\n    case 0:\n      return c ? function(d) {\n        return Ca[d];\n      } : function(d) {\n        return J[d];\n      };\n    case 1:\n      return c ? function(d) {\n        return Da[d >> 1];\n      } : function(d) {\n        return Ea[d >> 1];\n      };\n    case 2:\n      return c ? function(d) {\n        return L[d >> 2];\n      } : function(d) {\n        return M[d >> 2];\n      };\n    default:\n      throw new TypeError(\"Unknown integer type: \" + b);\n  }\n}\nvar ic = (b, a, c, d) => {\n  if (0 < d) {\n    d = c + d - 1;\n    for (var e = 0; e < b.length; ++e) {\n      var f = b.charCodeAt(e);\n      if (55296 <= f && 57343 >= f) {\n        var m = b.charCodeAt(++e);\n        f = 65536 + ((f & 1023) << 10) | m & 1023;\n      }\n      if (127 >= f) {\n        if (c >= d) {\n          break;\n        }\n        a[c++] = f;\n      } else {\n        if (2047 >= f) {\n          if (c + 1 >= d) {\n            break;\n          }\n          a[c++] = 192 | f >> 6;\n        } else {\n          if (65535 >= f) {\n            if (c + 2 >= d) {\n              break;\n            }\n            a[c++] = 224 | f >> 12;\n          } else {\n            if (c + 3 >= d) {\n              break;\n            }\n            a[c++] = 240 | f >> 18;\n            a[c++] = 128 | f >> 12 & 63;\n          }\n          a[c++] = 128 | f >> 6 & 63;\n        }\n        a[c++] = 128 | f & 63;\n      }\n    }\n    a[c] = 0;\n  }\n}, jc = b => {\n  for (var a = 0, c = 0; c < b.length; ++c) {\n    var d = b.charCodeAt(c);\n    127 >= d ? a++ : 2047 >= d ? a += 2 : 55296 <= d && 57343 >= d ? (a += 4, ++c) : a += 3;\n  }\n  return a;\n}, kc = \"undefined\" != typeof TextDecoder ? new TextDecoder(\"utf8\") : void 0, lc = (b, a, c) => {\n  var d = a + c;\n  for (c = a; b[c] && !(c >= d);) {\n    ++c;\n  }\n  if (16 < c - a && b.buffer && kc) {\n    return kc.decode(b.subarray(a, c));\n  }\n  for (d = \"\"; a < c;) {\n    var e = b[a++];\n    if (e & 128) {\n      var f = b[a++] & 63;\n      if (192 == (e & 224)) {\n        d += String.fromCharCode((e & 31) << 6 | f);\n      } else {\n        var m = b[a++] & 63;\n        e = 224 == (e & 240) ? (e & 15) << 12 | f << 6 | m : (e & 7) << 18 | f << 12 | m << 6 | b[a++] & 63;\n        65536 > e ? d += String.fromCharCode(e) : (e -= 65536, d += String.fromCharCode(55296 | e >> 10, 56320 | e & 1023));\n      }\n    } else {\n      d += String.fromCharCode(e);\n    }\n  }\n  return d;\n}, mc = \"undefined\" != typeof TextDecoder ? new TextDecoder(\"utf-16le\") : void 0, nc = (b, a) => {\n  var c = b >> 1;\n  for (var d = c + a / 2; !(c >= d) && Ea[c];) {\n    ++c;\n  }\n  c <<= 1;\n  if (32 < c - b && mc) {\n    return mc.decode(J.subarray(b, c));\n  }\n  c = \"\";\n  for (d = 0; !(d >= a / 2); ++d) {\n    var e = Da[b + 2 * d >> 1];\n    if (0 == e) {\n      break;\n    }\n    c += String.fromCharCode(e);\n  }\n  return c;\n}, qc = (b, a, c) => {\n  void 0 === c && (c = 2147483647);\n  if (2 > c) {\n    return 0;\n  }\n  c -= 2;\n  var d = a;\n  c = c < 2 * b.length ? c / 2 : b.length;\n  for (var e = 0; e < c; ++e) {\n    Da[a >> 1] = b.charCodeAt(e), a += 2;\n  }\n  Da[a >> 1] = 0;\n  return a - d;\n}, rc = b => 2 * b.length, sc = (b, a) => {\n  for (var c = 0, d = \"\"; !(c >= a / 4);) {\n    var e = L[b + 4 * c >> 2];\n    if (0 == e) {\n      break;\n    }\n    ++c;\n    65536 <= e ? (e -= 65536, d += String.fromCharCode(55296 | e >> 10, 56320 | e & 1023)) : d += String.fromCharCode(e);\n  }\n  return d;\n}, tc = (b, a, c) => {\n  void 0 === c && (c = 2147483647);\n  if (4 > c) {\n    return 0;\n  }\n  var d = a;\n  c = d + c - 4;\n  for (var e = 0; e < b.length; ++e) {\n    var f = b.charCodeAt(e);\n    if (55296 <= f && 57343 >= f) {\n      var m = b.charCodeAt(++e);\n      f = 65536 + ((f & 1023) << 10) | m & 1023;\n    }\n    L[a >> 2] = f;\n    a += 4;\n    if (a + 4 > c) {\n      break;\n    }\n  }\n  L[a >> 2] = 0;\n  return a - d;\n}, uc = b => {\n  for (var a = 0, c = 0; c < b.length; ++c) {\n    var d = b.charCodeAt(c);\n    55296 <= d && 57343 >= d && ++c;\n    a += 4;\n  }\n  return a;\n}, vc = {};\nfunction wc(b) {\n  var a = vc[b];\n  return void 0 === a ? U(b) : a;\n}\nvar xc = [];\nfunction yc(b) {\n  var a = xc.length;\n  xc.push(b);\n  return a;\n}\nfunction zc(b, a) {\n  for (var c = Array(b), d = 0; d < b; ++d) {\n    c[d] = nb(M[a + 4 * d >> 2], \"parameter \" + d);\n  }\n  return c;\n}\nvar Ac = [], Bc = {}, Dc = () => {\n  if (!Cc) {\n    var b = {USER:\"web_user\", LOGNAME:\"web_user\", PATH:\"/\", PWD:\"/\", HOME:\"/home/<USER>\", LANG:(\"object\" == typeof navigator && navigator.languages && navigator.languages[0] || \"C\").replace(\"-\", \"_\") + \".UTF-8\", _:na || \"./this.program\"}, a;\n    for (a in Bc) {\n      void 0 === Bc[a] ? delete b[a] : b[a] = Bc[a];\n    }\n    var c = [];\n    for (a in b) {\n      c.push(`${a}=${b[a]}`);\n    }\n    Cc = c;\n  }\n  return Cc;\n}, Cc, Ec = [null, [], []], Fc = b => 0 === b % 4 && (0 !== b % 100 || 0 === b % 400), Gc = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31], Hc = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\nfunction Ic(b) {\n  var a = Array(jc(b) + 1);\n  ic(b, a, 0, a.length);\n  return a;\n}\nvar Jc = (b, a, c, d) => {\n  function e(g, r, q) {\n    for (g = \"number\" == typeof g ? g.toString() : g || \"\"; g.length < r;) {\n      g = q[0] + g;\n    }\n    return g;\n  }\n  function f(g, r) {\n    return e(g, r, \"0\");\n  }\n  function m(g, r) {\n    function q(D) {\n      return 0 > D ? -1 : 0 < D ? 1 : 0;\n    }\n    var G;\n    0 === (G = q(g.getFullYear() - r.getFullYear())) && 0 === (G = q(g.getMonth() - r.getMonth())) && (G = q(g.getDate() - r.getDate()));\n    return G;\n  }\n  function k(g) {\n    switch(g.getDay()) {\n      case 0:\n        return new Date(g.getFullYear() - 1, 11, 29);\n      case 1:\n        return g;\n      case 2:\n        return new Date(g.getFullYear(), 0, 3);\n      case 3:\n        return new Date(g.getFullYear(), 0, 2);\n      case 4:\n        return new Date(g.getFullYear(), 0, 1);\n      case 5:\n        return new Date(g.getFullYear() - 1, 11, 31);\n      case 6:\n        return new Date(g.getFullYear() - 1, 11, 30);\n    }\n  }\n  function n(g) {\n    var r = g.J;\n    for (g = new Date((new Date(g.K + 1900, 0, 1)).getTime()); 0 < r;) {\n      var q = g.getMonth(), G = (Fc(g.getFullYear()) ? Gc : Hc)[q];\n      if (r > G - g.getDate()) {\n        r -= G - g.getDate() + 1, g.setDate(1), 11 > q ? g.setMonth(q + 1) : (g.setMonth(0), g.setFullYear(g.getFullYear() + 1));\n      } else {\n        g.setDate(g.getDate() + r);\n        break;\n      }\n    }\n    q = new Date(g.getFullYear() + 1, 0, 4);\n    r = k(new Date(g.getFullYear(), 0, 4));\n    q = k(q);\n    return 0 >= m(r, g) ? 0 >= m(q, g) ? g.getFullYear() + 1 : g.getFullYear() : g.getFullYear() - 1;\n  }\n  var l = L[d + 40 >> 2];\n  d = {qb:L[d >> 2], pb:L[d + 4 >> 2], Z:L[d + 8 >> 2], ha:L[d + 12 >> 2], $:L[d + 16 >> 2], K:L[d + 20 >> 2], C:L[d + 24 >> 2], J:L[d + 28 >> 2], ub:L[d + 32 >> 2], ob:L[d + 36 >> 2], rb:l ? l ? lc(J, l) : \"\" : \"\"};\n  c = c ? lc(J, c) : \"\";\n  l = {\"%c\":\"%a %b %d %H:%M:%S %Y\", \"%D\":\"%m/%d/%y\", \"%F\":\"%Y-%m-%d\", \"%h\":\"%b\", \"%r\":\"%I:%M:%S %p\", \"%R\":\"%H:%M\", \"%T\":\"%H:%M:%S\", \"%x\":\"%m/%d/%y\", \"%X\":\"%H:%M:%S\", \"%Ec\":\"%c\", \"%EC\":\"%C\", \"%Ex\":\"%m/%d/%y\", \"%EX\":\"%H:%M:%S\", \"%Ey\":\"%y\", \"%EY\":\"%Y\", \"%Od\":\"%d\", \"%Oe\":\"%e\", \"%OH\":\"%H\", \"%OI\":\"%I\", \"%Om\":\"%m\", \"%OM\":\"%M\", \"%OS\":\"%S\", \"%Ou\":\"%u\", \"%OU\":\"%U\", \"%OV\":\"%V\", \"%Ow\":\"%w\", \"%OW\":\"%W\", \"%Oy\":\"%y\",};\n  for (var t in l) {\n    c = c.replace(new RegExp(t, \"g\"), l[t]);\n  }\n  var w = \"Sunday Monday Tuesday Wednesday Thursday Friday Saturday\".split(\" \"), x = \"January February March April May June July August September October November December\".split(\" \");\n  l = {\"%a\":g => w[g.C].substring(0, 3), \"%A\":g => w[g.C], \"%b\":g => x[g.$].substring(0, 3), \"%B\":g => x[g.$], \"%C\":g => f((g.K + 1900) / 100 | 0, 2), \"%d\":g => f(g.ha, 2), \"%e\":g => e(g.ha, 2, \" \"), \"%g\":g => n(g).toString().substring(2), \"%G\":g => n(g), \"%H\":g => f(g.Z, 2), \"%I\":g => {\n    g = g.Z;\n    0 == g ? g = 12 : 12 < g && (g -= 12);\n    return f(g, 2);\n  }, \"%j\":g => {\n    for (var r = 0, q = 0; q <= g.$ - 1; r += (Fc(g.K + 1900) ? Gc : Hc)[q++]) {\n    }\n    return f(g.ha + r, 3);\n  }, \"%m\":g => f(g.$ + 1, 2), \"%M\":g => f(g.pb, 2), \"%n\":() => \"\\n\", \"%p\":g => 0 <= g.Z && 12 > g.Z ? \"AM\" : \"PM\", \"%S\":g => f(g.qb, 2), \"%t\":() => \"\\t\", \"%u\":g => g.C || 7, \"%U\":g => f(Math.floor((g.J + 7 - g.C) / 7), 2), \"%V\":g => {\n    var r = Math.floor((g.J + 7 - (g.C + 6) % 7) / 7);\n    2 >= (g.C + 371 - g.J - 2) % 7 && r++;\n    if (r) {\n      53 == r && (q = (g.C + 371 - g.J) % 7, 4 == q || 3 == q && Fc(g.K) || (r = 1));\n    } else {\n      r = 52;\n      var q = (g.C + 7 - g.J - 1) % 7;\n      (4 == q || 5 == q && Fc(g.K % 400 - 1)) && r++;\n    }\n    return f(r, 2);\n  }, \"%w\":g => g.C, \"%W\":g => f(Math.floor((g.J + 7 - (g.C + 6) % 7) / 7), 2), \"%y\":g => (g.K + 1900).toString().substring(2), \"%Y\":g => g.K + 1900, \"%z\":g => {\n    g = g.ob;\n    var r = 0 <= g;\n    g = Math.abs(g) / 60;\n    return (r ? \"+\" : \"-\") + String(\"0000\" + (g / 60 * 100 + g % 60)).slice(-4);\n  }, \"%Z\":g => g.rb, \"%%\":() => \"%\"};\n  c = c.replace(/%%/g, \"\\x00\\x00\");\n  for (t in l) {\n    c.includes(t) && (c = c.replace(new RegExp(t, \"g\"), l[t](d)));\n  }\n  c = c.replace(/\\0\\0/g, \"%\");\n  t = Ic(c);\n  if (t.length > a) {\n    return 0;\n  }\n  Ca.set(t, b);\n  return t.length - 1;\n};\nObject.assign($a.prototype, {get(b) {\n  return this.G[b];\n}, has(b) {\n  return void 0 !== this.G[b];\n}, Za(b) {\n  var a = this.ta.pop() || this.G.length;\n  this.G[a] = b;\n  return a;\n}, $a(b) {\n  this.G[b] = void 0;\n  this.ta.push(b);\n}});\nab = h.BindingError = class extends Error {\n  constructor(b) {\n    super(b);\n    this.name = \"BindingError\";\n  }\n};\nP.G.push({value:void 0}, {value:null}, {value:!0}, {value:!1},);\nP.ua = P.G.length;\nh.count_emval_handles = function() {\n  for (var b = 0, a = P.ua; a < P.G.length; ++a) {\n    void 0 !== P.G[a] && ++b;\n  }\n  return b;\n};\ncb = h.PureVirtualError = bb(\"PureVirtualError\");\nfor (var Kc = Array(256), Lc = 0; 256 > Lc; ++Lc) {\n  Kc[Lc] = String.fromCharCode(Lc);\n}\ndb = Kc;\nh.getInheritedInstanceCount = function() {\n  return Object.keys(hb).length;\n};\nh.getLiveInheritedInstances = function() {\n  var b = [], a;\n  for (a in hb) {\n    hb.hasOwnProperty(a) && b.push(hb[a]);\n  }\n  return b;\n};\nh.flushPendingDeletes = fb;\nh.setDelayFunction = function(b) {\n  gb = b;\n  eb.length && gb && gb(fb);\n};\nzb = h.InternalError = class extends Error {\n  constructor(b) {\n    super(b);\n    this.name = \"InternalError\";\n  }\n};\nMb.prototype.isAliasOf = function(b) {\n  if (!(this instanceof Mb && b instanceof Mb)) {\n    return !1;\n  }\n  var a = this.g.j.h, c = this.g.i, d = b.g.j.h;\n  for (b = b.g.i; a.l;) {\n    c = a.S(c), a = a.l;\n  }\n  for (; d.l;) {\n    b = d.S(b), d = d.l;\n  }\n  return a === d && c === b;\n};\nMb.prototype.clone = function() {\n  this.g.i || Lb(this);\n  if (this.g.O) {\n    return this.g.count.value += 1, this;\n  }\n  var b = Cb, a = Object, c = a.create, d = Object.getPrototypeOf(this), e = this.g;\n  b = b(c.call(a, d, {g:{value:{count:e.count, M:e.M, O:e.O, i:e.i, j:e.j, s:e.s, A:e.A,},}}));\n  b.g.count.value += 1;\n  b.g.M = !1;\n  return b;\n};\nMb.prototype[\"delete\"] = function() {\n  this.g.i || Lb(this);\n  this.g.M && !this.g.O && Q(\"Object already scheduled for deletion\");\n  ob(this);\n  qb(this.g);\n  this.g.O || (this.g.s = void 0, this.g.i = void 0);\n};\nMb.prototype.isDeleted = function() {\n  return !this.g.i;\n};\nMb.prototype.deleteLater = function() {\n  this.g.i || Lb(this);\n  this.g.M && !this.g.O && Q(\"Object already scheduled for deletion\");\n  eb.push(this);\n  1 === eb.length && gb && gb(fb);\n  this.g.M = !0;\n  return this;\n};\nVb.prototype.Va = function(b) {\n  this.za && (b = this.za(b));\n  return b;\n};\nVb.prototype.pa = function(b) {\n  this.H && this.H(b);\n};\nVb.prototype.argPackAdvance = 8;\nVb.prototype.readValueFromPointer = Fb;\nVb.prototype.deleteObject = function(b) {\n  if (null !== b) {\n    b[\"delete\"]();\n  }\n};\nVb.prototype.fromWireType = function(b) {\n  function a() {\n    return this.V ? Bb(this.h.B, {j:this.ib, i:c, A:this, s:b,}) : Bb(this.h.B, {j:this, i:b,});\n  }\n  var c = this.Va(b);\n  if (!c) {\n    return this.pa(b), null;\n  }\n  var d = yb(this.h, c);\n  if (void 0 !== d) {\n    if (0 === d.g.count.value) {\n      return d.g.i = c, d.g.s = b, d.clone();\n    }\n    d = d.clone();\n    this.pa(b);\n    return d;\n  }\n  d = this.h.Ua(c);\n  d = sb[d];\n  if (!d) {\n    return a.call(this);\n  }\n  d = this.U ? d.Ka : d.pointerType;\n  var e = rb(c, this.h, d.h);\n  return null === e ? a.call(this) : this.V ? Bb(d.h.B, {j:d, i:e, A:this, s:b,}) : Bb(d.h.B, {j:d, i:e,});\n};\n$b = h.UnboundTypeError = bb(\"UnboundTypeError\");\nvar Nc = {_embind_create_inheriting_constructor:function(b, a, c) {\n  b = U(b);\n  a = nb(a, \"wrapper\");\n  c = R(c);\n  var d = [].slice, e = a.h, f = e.B, m = e.l.B, k = e.l.constructor;\n  b = Za(b, function() {\n    e.l.ya.forEach(function(l) {\n      if (this[l] === m[l]) {\n        throw new cb(`Pure virtual function ${l} must be implemented in JavaScript`);\n      }\n    }.bind(this));\n    Object.defineProperty(this, \"__parent\", {value:f});\n    this.__construct.apply(this, d.call(arguments));\n  });\n  f.__construct = function() {\n    this === f && Q(\"Pass correct 'this' to __construct\");\n    var l = k.implement.apply(void 0, [this].concat(d.call(arguments)));\n    ob(l);\n    var t = l.g;\n    l.notifyOnDestruction();\n    t.O = !0;\n    Object.defineProperties(this, {g:{value:t}});\n    Cb(this);\n    l = t.i;\n    l = ib(e, l);\n    hb.hasOwnProperty(l) ? Q(`Tried to register registered instance: ${l}`) : hb[l] = this;\n  };\n  f.__destruct = function() {\n    this === f && Q(\"Pass correct 'this' to __destruct\");\n    ob(this);\n    var l = this.g.i;\n    l = ib(e, l);\n    hb.hasOwnProperty(l) ? delete hb[l] : Q(`Tried to unregister unregistered instance: ${l}`);\n  };\n  b.prototype = Object.create(f);\n  for (var n in c) {\n    b.prototype[n] = c[n];\n  }\n  return S(b);\n}, _embind_finalize_value_object:function(b) {\n  var a = Db[b];\n  delete Db[b];\n  var c = a.fa, d = a.H, e = a.sa, f = e.map(m => m.Ya).concat(e.map(m => m.lb));\n  V([b], f, m => {\n    var k = {};\n    e.forEach((n, l) => {\n      var t = m[l], w = n.Wa, x = n.Xa, g = m[l + e.length], r = n.kb, q = n.mb;\n      k[n.Sa] = {read:G => t.fromWireType(w(x, G)), write:(G, D) => {\n        var v = [];\n        r(q, G, g.toWireType(v, D));\n        Eb(v);\n      }};\n    });\n    return [{name:a.name, fromWireType:function(n) {\n      var l = {}, t;\n      for (t in k) {\n        l[t] = k[t].read(n);\n      }\n      d(n);\n      return l;\n    }, toWireType:function(n, l) {\n      for (var t in k) {\n        if (!(t in l)) {\n          throw new TypeError(`Missing field: \"${t}\"`);\n        }\n      }\n      var w = c();\n      for (t in k) {\n        k[t].write(w, l[t]);\n      }\n      null !== n && n.push(d, w);\n      return w;\n    }, argPackAdvance:8, readValueFromPointer:Fb, v:d,}];\n  });\n}, _embind_register_bigint:function() {\n}, _embind_register_bool:function(b, a, c, d, e) {\n  var f = Jb(c);\n  a = U(a);\n  Ib(b, {name:a, fromWireType:function(m) {\n    return !!m;\n  }, toWireType:function(m, k) {\n    return k ? d : e;\n  }, argPackAdvance:8, readValueFromPointer:function(m) {\n    if (1 === c) {\n      var k = Ca;\n    } else if (2 === c) {\n      k = Da;\n    } else if (4 === c) {\n      k = L;\n    } else {\n      throw new TypeError(\"Unknown boolean type size: \" + a);\n    }\n    return this.fromWireType(k[m >> f]);\n  }, v:null,});\n}, _embind_register_class:function(b, a, c, d, e, f, m, k, n, l, t, w, x) {\n  t = U(t);\n  f = W(e, f);\n  k && (k = W(m, k));\n  l && (l = W(n, l));\n  x = W(w, x);\n  var g = Ya(t);\n  Ob(g, function() {\n    ac(`Cannot construct ${t} due to unbound types`, [d]);\n  });\n  V([b, a, c], d ? [d] : [], function(r) {\n    r = r[0];\n    if (d) {\n      var q = r.h;\n      var G = q.B;\n    } else {\n      G = Mb.prototype;\n    }\n    r = Za(g, function() {\n      if (Object.getPrototypeOf(this) !== D) {\n        throw new ab(\"Use 'new' to construct \" + t);\n      }\n      if (void 0 === v.I) {\n        throw new ab(t + \" has no accessible constructor\");\n      }\n      var O = v.I[arguments.length];\n      if (void 0 === O) {\n        throw new ab(`Tried to invoke ctor of ${t} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(v.I).toString()}) parameters instead!`);\n      }\n      return O.apply(this, arguments);\n    });\n    var D = Object.create(G, {constructor:{value:r},});\n    r.prototype = D;\n    var v = new Pb(t, r, D, x, q, f, k, l);\n    v.l && (void 0 === v.l.T && (v.l.T = []), v.l.T.push(v));\n    q = new Vb(t, v, !0, !1);\n    G = new Vb(t + \"*\", v, !1, !1);\n    var I = new Vb(t + \" const*\", v, !1, !0);\n    sb[b] = {pointerType:G, Ka:I};\n    Wb(g, r);\n    return [q, G, I];\n  });\n}, _embind_register_class_class_function:function(b, a, c, d, e, f, m) {\n  var k = cc(c, d);\n  a = U(a);\n  f = W(e, f);\n  V([], [b], function(n) {\n    function l() {\n      ac(`Cannot call ${t} due to unbound types`, k);\n    }\n    n = n[0];\n    var t = `${n.name}.${a}`;\n    a.startsWith(\"@@\") && (a = Symbol[a.substring(2)]);\n    var w = n.h.constructor;\n    void 0 === w[a] ? (l.L = c - 1, w[a] = l) : (Nb(w, a, t), w[a].m[c - 1] = l);\n    V([], k, function(x) {\n      x = bc(t, [x[0], null].concat(x.slice(1)), null, f, m);\n      void 0 === w[a].m ? (x.L = c - 1, w[a] = x) : w[a].m[c - 1] = x;\n      if (n.h.T) {\n        for (const g of n.h.T) {\n          g.constructor.hasOwnProperty(a) || (g.constructor[a] = x);\n        }\n      }\n      return [];\n    });\n    return [];\n  });\n}, _embind_register_class_class_property:function(b, a, c, d, e, f, m, k) {\n  a = U(a);\n  f = W(e, f);\n  V([], [b], function(n) {\n    n = n[0];\n    var l = `${n.name}.${a}`, t = {get() {\n      ac(`Cannot access ${l} due to unbound types`, [c]);\n    }, enumerable:!0, configurable:!0};\n    t.set = k ? () => {\n      ac(`Cannot access ${l} due to unbound types`, [c]);\n    } : () => {\n      Q(`${l} is a read-only property`);\n    };\n    Object.defineProperty(n.h.constructor, a, t);\n    V([], [c], function(w) {\n      w = w[0];\n      var x = {get() {\n        return w.fromWireType(f(d));\n      }, enumerable:!0};\n      k && (k = W(m, k), x.set = g => {\n        var r = [];\n        k(d, w.toWireType(r, g));\n        Eb(r);\n      });\n      Object.defineProperty(n.h.constructor, a, x);\n      return [];\n    });\n    return [];\n  });\n}, _embind_register_class_constructor:function(b, a, c, d, e, f) {\n  var m = cc(a, c);\n  e = W(d, e);\n  V([], [b], function(k) {\n    k = k[0];\n    var n = `constructor ${k.name}`;\n    void 0 === k.h.I && (k.h.I = []);\n    if (void 0 !== k.h.I[a - 1]) {\n      throw new ab(`Cannot register multiple constructors with identical number of parameters (${a - 1}) for class '${k.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);\n    }\n    k.h.I[a - 1] = () => {\n      ac(`Cannot construct ${k.name} due to unbound types`, m);\n    };\n    V([], m, function(l) {\n      l.splice(1, 0, null);\n      k.h.I[a - 1] = bc(n, l, null, e, f);\n      return [];\n    });\n    return [];\n  });\n}, _embind_register_class_function:function(b, a, c, d, e, f, m, k) {\n  var n = cc(c, d);\n  a = U(a);\n  f = W(e, f);\n  V([], [b], function(l) {\n    function t() {\n      ac(`Cannot call ${w} due to unbound types`, n);\n    }\n    l = l[0];\n    var w = `${l.name}.${a}`;\n    a.startsWith(\"@@\") && (a = Symbol[a.substring(2)]);\n    k && l.h.ya.push(a);\n    var x = l.h.B, g = x[a];\n    void 0 === g || void 0 === g.m && g.className !== l.name && g.L === c - 2 ? (t.L = c - 2, t.className = l.name, x[a] = t) : (Nb(x, a, w), x[a].m[c - 2] = t);\n    V([], n, function(r) {\n      r = bc(w, r, l, f, m);\n      void 0 === x[a].m ? (r.L = c - 2, x[a] = r) : x[a].m[c - 2] = r;\n      return [];\n    });\n    return [];\n  });\n}, _embind_register_class_property:function(b, a, c, d, e, f, m, k, n, l) {\n  a = U(a);\n  e = W(d, e);\n  V([], [b], function(t) {\n    t = t[0];\n    var w = `${t.name}.${a}`, x = {get() {\n      ac(`Cannot access ${w} due to unbound types`, [c, m]);\n    }, enumerable:!0, configurable:!0};\n    x.set = n ? () => {\n      ac(`Cannot access ${w} due to unbound types`, [c, m]);\n    } : () => {\n      Q(w + \" is a read-only property\");\n    };\n    Object.defineProperty(t.h.B, a, x);\n    V([], n ? [c, m] : [c], function(g) {\n      var r = g[0], q = {get() {\n        var D = dc(this, t, w + \" getter\");\n        return r.fromWireType(e(f, D));\n      }, enumerable:!0};\n      if (n) {\n        n = W(k, n);\n        var G = g[1];\n        q.set = function(D) {\n          var v = dc(this, t, w + \" setter\"), I = [];\n          n(l, v, G.toWireType(I, D));\n          Eb(I);\n        };\n      }\n      Object.defineProperty(t.h.B, a, q);\n      return [];\n    });\n    return [];\n  });\n}, _embind_register_emval:function(b, a) {\n  a = U(a);\n  Ib(b, {name:a, fromWireType:function(c) {\n    var d = R(c);\n    ec(c);\n    return d;\n  }, toWireType:function(c, d) {\n    return S(d);\n  }, argPackAdvance:8, readValueFromPointer:Fb, v:null,});\n}, _embind_register_enum:function(b, a, c, d) {\n  function e() {\n  }\n  c = Jb(c);\n  a = U(a);\n  e.values = {};\n  Ib(b, {name:a, constructor:e, fromWireType:function(f) {\n    return this.constructor.values[f];\n  }, toWireType:function(f, m) {\n    return m.value;\n  }, argPackAdvance:8, readValueFromPointer:fc(a, c, d), v:null,});\n  Ob(a, e);\n}, _embind_register_enum_value:function(b, a, c) {\n  var d = nb(b, \"enum\");\n  a = U(a);\n  b = d.constructor;\n  d = Object.create(d.constructor.prototype, {value:{value:c}, constructor:{value:Za(`${d.name}_${a}`, function() {\n  })},});\n  b.values[c] = d;\n  b[a] = d;\n}, _embind_register_float:function(b, a, c) {\n  c = Jb(c);\n  a = U(a);\n  Ib(b, {name:a, fromWireType:function(d) {\n    return d;\n  }, toWireType:function(d, e) {\n    return e;\n  }, argPackAdvance:8, readValueFromPointer:gc(a, c), v:null,});\n}, _embind_register_function:function(b, a, c, d, e, f) {\n  var m = cc(a, c);\n  b = U(b);\n  e = W(d, e);\n  Ob(b, function() {\n    ac(`Cannot call ${b} due to unbound types`, m);\n  }, a - 1);\n  V([], m, function(k) {\n    Wb(b, bc(b, [k[0], null].concat(k.slice(1)), null, e, f), a - 1);\n    return [];\n  });\n}, _embind_register_integer:function(b, a, c, d, e) {\n  a = U(a);\n  -1 === e && (e = 4294967295);\n  e = Jb(c);\n  var f = k => k;\n  if (0 === d) {\n    var m = 32 - 8 * c;\n    f = k => k << m >>> m;\n  }\n  c = a.includes(\"unsigned\") ? function(k, n) {\n    return n >>> 0;\n  } : function(k, n) {\n    return n;\n  };\n  Ib(b, {name:a, fromWireType:f, toWireType:c, argPackAdvance:8, readValueFromPointer:hc(a, e, 0 !== d), v:null,});\n}, _embind_register_memory_view:function(b, a, c) {\n  function d(f) {\n    f >>= 2;\n    var m = M;\n    return new e(m.buffer, m[f + 1], m[f]);\n  }\n  var e = [Int8Array, Uint8Array, Int16Array, Uint16Array, Int32Array, Uint32Array, Float32Array, Float64Array,][a];\n  c = U(c);\n  Ib(b, {name:c, fromWireType:d, argPackAdvance:8, readValueFromPointer:d,}, {ab:!0,});\n}, _embind_register_std_string:function(b, a) {\n  a = U(a);\n  var c = \"std::string\" === a;\n  Ib(b, {name:a, fromWireType:function(d) {\n    var e = M[d >> 2], f = d + 4;\n    if (c) {\n      for (var m = f, k = 0; k <= e; ++k) {\n        var n = f + k;\n        if (k == e || 0 == J[n]) {\n          m = m ? lc(J, m, n - m) : \"\";\n          if (void 0 === l) {\n            var l = m;\n          } else {\n            l += String.fromCharCode(0), l += m;\n          }\n          m = n + 1;\n        }\n      }\n    } else {\n      l = Array(e);\n      for (k = 0; k < e; ++k) {\n        l[k] = String.fromCharCode(J[f + k]);\n      }\n      l = l.join(\"\");\n    }\n    mb(d);\n    return l;\n  }, toWireType:function(d, e) {\n    e instanceof ArrayBuffer && (e = new Uint8Array(e));\n    var f = \"string\" == typeof e;\n    f || e instanceof Uint8Array || e instanceof Uint8ClampedArray || e instanceof Int8Array || Q(\"Cannot pass non-string to std::string\");\n    var m = c && f ? jc(e) : e.length;\n    var k = Mc(4 + m + 1), n = k + 4;\n    M[k >> 2] = m;\n    if (c && f) {\n      ic(e, J, n, m + 1);\n    } else {\n      if (f) {\n        for (f = 0; f < m; ++f) {\n          var l = e.charCodeAt(f);\n          255 < l && (mb(n), Q(\"String has UTF-16 code units that do not fit in 8 bits\"));\n          J[n + f] = l;\n        }\n      } else {\n        for (f = 0; f < m; ++f) {\n          J[n + f] = e[f];\n        }\n      }\n    }\n    null !== d && d.push(mb, k);\n    return k;\n  }, argPackAdvance:8, readValueFromPointer:Fb, v:function(d) {\n    mb(d);\n  },});\n}, _embind_register_std_wstring:function(b, a, c) {\n  c = U(c);\n  if (2 === a) {\n    var d = nc;\n    var e = qc;\n    var f = rc;\n    var m = () => Ea;\n    var k = 1;\n  } else {\n    4 === a && (d = sc, e = tc, f = uc, m = () => M, k = 2);\n  }\n  Ib(b, {name:c, fromWireType:function(n) {\n    for (var l = M[n >> 2], t = m(), w, x = n + 4, g = 0; g <= l; ++g) {\n      var r = n + 4 + g * a;\n      if (g == l || 0 == t[r >> k]) {\n        x = d(x, r - x), void 0 === w ? w = x : (w += String.fromCharCode(0), w += x), x = r + a;\n      }\n    }\n    mb(n);\n    return w;\n  }, toWireType:function(n, l) {\n    \"string\" != typeof l && Q(`Cannot pass non-string to C++ string type ${c}`);\n    var t = f(l), w = Mc(4 + t + a);\n    M[w >> 2] = t >> k;\n    e(l, w + 4, t + a);\n    null !== n && n.push(mb, w);\n    return w;\n  }, argPackAdvance:8, readValueFromPointer:Fb, v:function(n) {\n    mb(n);\n  },});\n}, _embind_register_value_object:function(b, a, c, d, e, f) {\n  Db[b] = {name:U(a), fa:W(c, d), H:W(e, f), sa:[],};\n}, _embind_register_value_object_field:function(b, a, c, d, e, f, m, k, n, l) {\n  Db[b].sa.push({Sa:U(a), Ya:c, Wa:W(d, e), Xa:f, lb:m, kb:W(k, n), mb:l,});\n}, _embind_register_void:function(b, a) {\n  a = U(a);\n  Ib(b, {cb:!0, name:a, argPackAdvance:0, fromWireType:function() {\n  }, toWireType:function() {\n  },});\n}, _emscripten_get_now_is_monotonic:() => !0, _emval_as:function(b, a, c) {\n  b = R(b);\n  a = nb(a, \"emval::as\");\n  var d = [], e = S(d);\n  M[c >> 2] = e;\n  return a.toWireType(d, b);\n}, _emval_call_method:function(b, a, c, d, e) {\n  b = xc[b];\n  a = R(a);\n  c = wc(c);\n  var f = [];\n  M[d >> 2] = S(f);\n  return b(a, c, f, e);\n}, _emval_call_void_method:function(b, a, c, d) {\n  b = xc[b];\n  a = R(a);\n  c = wc(c);\n  b(a, c, null, d);\n}, _emval_decref:ec, _emval_get_method_caller:function(b, a) {\n  var c = zc(b, a), d = c[0];\n  a = d.name + \"_$\" + c.slice(1).map(function(m) {\n    return m.name;\n  }).join(\"_\") + \"$\";\n  var e = Ac[a];\n  if (void 0 !== e) {\n    return e;\n  }\n  var f = Array(b - 1);\n  e = yc((m, k, n, l) => {\n    for (var t = 0, w = 0; w < b - 1; ++w) {\n      f[w] = c[w + 1].readValueFromPointer(l + t), t += c[w + 1].argPackAdvance;\n    }\n    m = m[k].apply(m, f);\n    for (w = 0; w < b - 1; ++w) {\n      c[w + 1].Na && c[w + 1].Na(f[w]);\n    }\n    if (!d.cb) {\n      return d.toWireType(n, m);\n    }\n  });\n  return Ac[a] = e;\n}, _emval_get_module_property:function(b) {\n  b = wc(b);\n  return S(h[b]);\n}, _emval_get_property:function(b, a) {\n  b = R(b);\n  a = R(a);\n  return S(b[a]);\n}, _emval_incref:function(b) {\n  4 < b && (P.get(b).Aa += 1);\n}, _emval_new_array:function() {\n  return S([]);\n}, _emval_new_cstring:function(b) {\n  return S(wc(b));\n}, _emval_new_object:function() {\n  return S({});\n}, _emval_run_destructors:function(b) {\n  var a = R(b);\n  Eb(a);\n  ec(b);\n}, _emval_set_property:function(b, a, c) {\n  b = R(b);\n  a = R(a);\n  c = R(c);\n  b[a] = c;\n}, _emval_take_value:function(b, a) {\n  b = nb(b, \"_emval_take_value\");\n  b = b.readValueFromPointer(a);\n  return S(b);\n}, abort:() => {\n  za(\"\");\n}, emscripten_date_now:function() {\n  return Date.now();\n}, emscripten_get_now:() => performance.now(), emscripten_memcpy_big:(b, a, c) => J.copyWithin(b, a, a + c), emscripten_resize_heap:b => {\n  var a = J.length;\n  b >>>= 0;\n  if (2147483648 < b) {\n    return !1;\n  }\n  for (var c = 1; 4 >= c; c *= 2) {\n    var d = a * (1 + 0.2 / c);\n    d = Math.min(d, b + 100663296);\n    var e = Math;\n    d = Math.max(b, d);\n    a: {\n      e = e.min.call(e, 2147483648, d + (65536 - d % 65536) % 65536) - Aa.buffer.byteLength + 65535 >>> 16;\n      try {\n        Aa.grow(e);\n        Ha();\n        var f = 1;\n        break a;\n      } catch (m) {\n      }\n      f = void 0;\n    }\n    if (f) {\n      return !0;\n    }\n  }\n  return !1;\n}, environ_get:(b, a) => {\n  var c = 0;\n  Dc().forEach(function(d, e) {\n    var f = a + c;\n    e = M[b + 4 * e >> 2] = f;\n    for (f = 0; f < d.length; ++f) {\n      Ca[e++ >> 0] = d.charCodeAt(f);\n    }\n    Ca[e >> 0] = 0;\n    c += d.length + 1;\n  });\n  return 0;\n}, environ_sizes_get:(b, a) => {\n  var c = Dc();\n  M[b >> 2] = c.length;\n  var d = 0;\n  c.forEach(function(e) {\n    d += e.length + 1;\n  });\n  M[a >> 2] = d;\n  return 0;\n}, fd_close:() => 52, fd_seek:function() {\n  return 70;\n}, fd_write:(b, a, c, d) => {\n  for (var e = 0, f = 0; f < c; f++) {\n    var m = M[a >> 2], k = M[a + 4 >> 2];\n    a += 8;\n    for (var n = 0; n < k; n++) {\n      var l = J[m + n], t = Ec[b];\n      0 === l || 10 === l ? ((1 === b ? ua : va)(lc(t, 0)), t.length = 0) : t.push(l);\n    }\n    e += k;\n  }\n  M[d >> 2] = e;\n  return 0;\n}, strftime_l:(b, a, c, d) => Jc(b, a, c, d)};\n(function() {\n  function b(c) {\n    z = c = c.exports;\n    Aa = z.memory;\n    Ha();\n    Ia = z.__indirect_function_table;\n    Ka.unshift(z.__wasm_call_ctors);\n    Na--;\n    h.monitorRunDependencies && h.monitorRunDependencies(Na);\n    if (0 == Na && (null !== Oa && (clearInterval(Oa), Oa = null), Pa)) {\n      var d = Pa;\n      Pa = null;\n      d();\n    }\n    return c;\n  }\n  var a = {env:Nc, wasi_snapshot_preview1:Nc,};\n  Na++;\n  h.monitorRunDependencies && h.monitorRunDependencies(Na);\n  if (h.instantiateWasm) {\n    try {\n      return h.instantiateWasm(a, b);\n    } catch (c) {\n      va(\"Module.instantiateWasm callback failed with error: \" + c), ba(c);\n    }\n  }\n  Wa(a, function(c) {\n    b(c.instance);\n  }).catch(ba);\n  return {};\n})();\nvar mb = b => (mb = z.free)(b), Mc = b => (Mc = z.malloc)(b), lb = b => (lb = z.__getTypeName)(b);\nh.__embind_initialize_bindings = () => (h.__embind_initialize_bindings = z._embind_initialize_bindings)();\nh.dynCall_jiji = (b, a, c, d, e) => (h.dynCall_jiji = z.dynCall_jiji)(b, a, c, d, e);\nh.dynCall_viijii = (b, a, c, d, e, f, m) => (h.dynCall_viijii = z.dynCall_viijii)(b, a, c, d, e, f, m);\nh.dynCall_iiiiij = (b, a, c, d, e, f, m) => (h.dynCall_iiiiij = z.dynCall_iiiiij)(b, a, c, d, e, f, m);\nh.dynCall_iiiiijj = (b, a, c, d, e, f, m, k, n) => (h.dynCall_iiiiijj = z.dynCall_iiiiijj)(b, a, c, d, e, f, m, k, n);\nh.dynCall_iiiiiijj = (b, a, c, d, e, f, m, k, n, l) => (h.dynCall_iiiiiijj = z.dynCall_iiiiiijj)(b, a, c, d, e, f, m, k, n, l);\nvar Oc;\nPa = function Pc() {\n  Oc || Qc();\n  Oc || (Pa = Pc);\n};\nfunction Qc() {\n  function b() {\n    if (!Oc && (Oc = !0, h.calledRun = !0, !Ba)) {\n      Xa(Ka);\n      aa(h);\n      if (h.onRuntimeInitialized) {\n        h.onRuntimeInitialized();\n      }\n      if (h.postRun) {\n        for (\"function\" == typeof h.postRun && (h.postRun = [h.postRun]); h.postRun.length;) {\n          var a = h.postRun.shift();\n          La.unshift(a);\n        }\n      }\n      Xa(La);\n    }\n  }\n  if (!(0 < Na)) {\n    if (h.preRun) {\n      for (\"function\" == typeof h.preRun && (h.preRun = [h.preRun]); h.preRun.length;) {\n        Ma();\n      }\n    }\n    Xa(Ja);\n    0 < Na || (h.setStatus ? (h.setStatus(\"Running...\"), setTimeout(function() {\n      setTimeout(function() {\n        h.setStatus(\"\");\n      }, 1);\n      b();\n    }, 1)) : b());\n  }\n}\nif (h.preInit) {\n  for (\"function\" == typeof h.preInit && (h.preInit = [h.preInit]); 0 < h.preInit.length;) {\n    h.preInit.pop()();\n  }\n}\nQc();\n\n\n\n  return moduleArg.ready\n}\n\n);\n})();\nexport default Rive;", "export { Animation } from \"./Animation\";\n", "import type {\n  LinearAnimationInstance,\n  LinearAnimation,\n  Artboard,\n  RiveCanvas,\n} from \"./../rive_advanced.mjs\";\n\n/**\n * Represents an animation that can be played on an Artboard. \n * Wraps animations and instances from the runtime and keeps track of playback state.\n *\n * The `Animation` class manages the state and behavior of a single animation instance,\n * including its current time, loop count, and ability to scrub to a specific time.\n *\n * The class provides methods to advance the animation, apply its interpolated keyframe\n * values to the Artboard, and clean up the underlying animation instance when the\n * animation is no longer needed.\n */\nexport class Animation {\n  public loopCount = 0;\n  public readonly instance: LinearAnimationInstance;\n\n  /**\n   * The time to which the animation should move to on the next render.\n   * If not null, the animation will scrub to this time instead of advancing by the given time.\n   */\n  public scrubTo: number | null = null;\n\n  /**\n   * Constructs a new animation\n   * @constructor\n   * @param {any} animation: runtime animation object\n   * @param {any} instance: runtime animation instance object\n   */\n  constructor(\n    private animation: LinearAnimation,\n    private artboard: Artboard,\n    runtime: RiveCanvas,\n    public playing: boolean\n  ) {\n    this.instance = new runtime.LinearAnimationInstance(animation, artboard);\n  }\n\n  /**\n   * Returns the animation's name\n   */\n  public get name(): string {\n    return this.animation.name;\n  }\n\n  /**\n   * Returns the animation's name\n   */\n  public get time(): number {\n    return this.instance.time;\n  }\n\n  /**\n   * Sets the animation's current time\n   */\n  public set time(value: number) {\n    this.instance.time = value;\n  }\n\n  /**\n   * Returns the animation's loop type\n   */\n  public get loopValue(): number {\n    return this.animation.loopValue;\n  }\n\n  /**\n   * Indicates whether the animation needs to be scrubbed.\n   * @returns `true` if the animation needs to be scrubbed, `false` otherwise.\n   */\n  public get needsScrub(): boolean {\n    return this.scrubTo !== null;\n  }\n\n  /**\n   * Advances the animation by the give time. If the animation needs scrubbing,\n   * time is ignored and the stored scrub value is used.\n   * @param time the time to advance the animation by if no scrubbing required\n   */\n  public advance(time: number) {\n    if (this.scrubTo === null) {\n      this.instance.advance(time);\n    } else {\n      this.instance.time = 0;\n      this.instance.advance(this.scrubTo);\n      this.scrubTo = null;\n    }\n  }\n\n  /**\n   * Apply interpolated keyframe values to the artboard. This should be called after calling\n   * .advance() on an animation instance so that new values are applied to properties.\n   *\n   * Note: This does not advance the artboard, which updates all objects on the artboard\n   * @param mix - Mix value for the animation from 0 to 1\n   */\n  public apply(mix: number) {\n    this.instance.apply(mix);\n  }\n\n  /**\n   * Deletes the backing Wasm animation instance; once this is called, this\n   * animation is no more.\n   */\n  public cleanup() {\n    this.instance.delete();\n  }\n}\n", "export { registerTouchInteractions } from \"./registerTouchInteractions\";\nexport { BLANK_URL, sanitizeUrl } from './sanitizeUrl';\n", "import * as rc from \"../rive_advanced.mjs\";\n\nexport interface TouchInteractionsParams {\n  canvas: HTMLCanvasElement | OffscreenCanvas;\n  artboard: rc.Artboard;\n  stateMachines: rc.StateMachineInstance[];\n  renderer: rc.Renderer;\n  rive: rc.RiveCanvas;\n  fit: rc.Fit;\n  alignment: rc.Alignment;\n  isTouchScrollEnabled?: boolean;\n  layoutScaleFactor?: number;\n}\n\ninterface ClientCoordinates {\n  clientX: number;\n  clientY: number;\n}\n\n/**\n * Returns the clientX and clientY properties from touch or mouse events. Also\n * calls preventDefault() on the event if it is a touchstart or touchmove to prevent\n * scrolling the page on mobile devices\n * @param event - Either a TouchEvent or a MouseEvent\n * @returns - Coordinates of the clientX and clientY properties from the touch/mouse event\n */\nconst getClientCoordinates = (\n  event: MouseEvent | TouchEvent,\n  isTouchScrollEnabled: boolean,\n): ClientCoordinates => {\n  if (\n    [\"touchstart\", \"touchmove\"].indexOf(event.type) > -1 &&\n    (event as TouchEvent).touches?.length\n  ) {\n    // This flag, if false, prevents touch events on the canvas default behavior\n    // which may prevent scrolling if a drag motion on the canvas is performed\n    if (!isTouchScrollEnabled) {\n      event.preventDefault();\n    }\n    return {\n      clientX: (event as TouchEvent).touches[0].clientX,\n      clientY: (event as TouchEvent).touches[0].clientY,\n    };\n  } else if (\n    event.type === \"touchend\" &&\n    (event as TouchEvent).changedTouches?.length\n  ) {\n    return {\n      clientX: (event as TouchEvent).changedTouches[0].clientX,\n      clientY: (event as TouchEvent).changedTouches[0].clientY,\n    };\n  } else {\n    return {\n      clientX: (event as MouseEvent).clientX,\n      clientY: (event as MouseEvent).clientY,\n    };\n  }\n};\n\n/**\n * Registers mouse move/up/down callback handlers on the canvas to send meaningful coordinates to\n * the state machine pointer move/up/down functions based on cursor interaction\n */\nexport const registerTouchInteractions = ({\n  canvas,\n  artboard,\n  stateMachines = [],\n  renderer,\n  rive,\n  fit,\n  alignment,\n  isTouchScrollEnabled = false,\n  layoutScaleFactor = 1.0,\n}: TouchInteractionsParams) => {\n  if (\n    !canvas ||\n    !stateMachines.length ||\n    !renderer ||\n    !rive ||\n    !artboard ||\n    typeof window === \"undefined\"\n  ) {\n    return null;\n  }\n  /**\n   * After a touchend event, some browsers may fire synthetic mouse events\n   * (mouseover, mousedown, mousemove, mouseup) if the touch interaction did not cause\n   * any default action (such as scrolling).\n   *\n   * This is done to simulate the behavior of a mouse for applications that do not support\n   * touch events.\n   *\n   * We're keeping track of the previous event to not send the synthetic mouse events if the\n   * touch event was a click (touchstart -> touchend).\n   *\n   * This is only needed when `isTouchScrollEnabled` is false\n   * When true, `preventDefault()` is called which prevents this behaviour.\n   **/\n  let _prevEventType: string | null = null;\n  let _syntheticEventsActive = false;\n\n  const processEventCallback = (event: MouseEvent | TouchEvent) => {\n    // Exit early out of all synthetic mouse events\n    // https://stackoverflow.com/questions/9656990/how-to-prevent-simulated-mouse-events-in-mobile-browsers\n    // https://stackoverflow.com/questions/25572070/javascript-touchend-versus-click-dilemma\n    if (_syntheticEventsActive && event instanceof MouseEvent) {\n      // Synthetic event finished\n      if (event.type == \"mouseup\") {\n        _syntheticEventsActive = false;\n      }\n\n      return;\n    }\n\n    // Test if it's a \"touch click\". This could cause the browser to send\n    // synthetic mouse events.\n    _syntheticEventsActive =\n      isTouchScrollEnabled &&\n      event.type === \"touchend\" &&\n      _prevEventType === \"touchstart\";\n\n    _prevEventType = event.type;\n\n    const boundingRect = (\n      event.currentTarget as HTMLCanvasElement\n    ).getBoundingClientRect();\n\n    const { clientX, clientY } = getClientCoordinates(\n      event,\n      isTouchScrollEnabled,\n    );\n    if (!clientX && !clientY) {\n      return;\n    }\n    const canvasX = clientX - boundingRect.left;\n    const canvasY = clientY - boundingRect.top;\n    const forwardMatrix = rive.computeAlignment(\n      fit,\n      alignment,\n      {\n        minX: 0,\n        minY: 0,\n        maxX: boundingRect.width,\n        maxY: boundingRect.height,\n      },\n      artboard.bounds,\n      layoutScaleFactor,\n    );\n    const invertedMatrix = new rive.Mat2D();\n    forwardMatrix.invert(invertedMatrix);\n    const canvasCoordinatesVector = new rive.Vec2D(canvasX, canvasY);\n    const transformedVector = rive.mapXY(\n      invertedMatrix,\n      canvasCoordinatesVector,\n    );\n    const transformedX = transformedVector.x();\n    const transformedY = transformedVector.y();\n\n    transformedVector.delete();\n    invertedMatrix.delete();\n    canvasCoordinatesVector.delete();\n    forwardMatrix.delete();\n\n    switch (event.type) {\n      /**\n       * There's a 2px buffer for a hitRadius when translating the pointer coordinates\n       * down to the state machine. In cases where the hitbox is about that much away\n       * from the Artboard border, we don't have exact precision on determining pointer\n       * exit. We're therefore adding to the translated coordinates on mouseout of a canvas\n       * to ensure that we report the mouse has truly exited the hitarea.\n       * https://github.com/rive-app/rive-cpp/blob/master/src/animation/state_machine_instance.cpp#L336\n       *\n       * We add/subtract 10000 to account for when the graphic goes beyond the canvas bound\n       * due to for example, a fit: 'cover'. Not perfect, but helps reliably (for now) ensure\n       * we report going out of bounds when the mouse is out of the canvas\n       */\n      case \"mouseout\":\n        for (const stateMachine of stateMachines) {\n          stateMachine.pointerMove(transformedX, transformedY);\n        }\n        break;\n\n      // Pointer moving/hovering on the canvas\n      case \"touchmove\":\n      case \"mouseover\":\n      case \"mousemove\": {\n        for (const stateMachine of stateMachines) {\n          stateMachine.pointerMove(transformedX, transformedY);\n        }\n        break;\n      }\n      // Pointer click initiated but not released yet on the canvas\n      case \"touchstart\":\n      case \"mousedown\": {\n        for (const stateMachine of stateMachines) {\n          stateMachine.pointerDown(transformedX, transformedY);\n        }\n        break;\n      }\n      // Pointer click released on the canvas\n      case \"touchend\":\n      case \"mouseup\": {\n        for (const stateMachine of stateMachines) {\n          stateMachine.pointerUp(transformedX, transformedY);\n        }\n        break;\n      }\n      default:\n    }\n  };\n  const callback = processEventCallback.bind(this);\n  canvas.addEventListener(\"mouseover\", callback);\n  canvas.addEventListener(\"mouseout\", callback);\n  canvas.addEventListener(\"mousemove\", callback);\n  canvas.addEventListener(\"mousedown\", callback);\n  canvas.addEventListener(\"mouseup\", callback);\n  canvas.addEventListener(\"touchmove\", callback, {\n    passive: isTouchScrollEnabled,\n  });\n  canvas.addEventListener(\"touchstart\", callback, {\n    passive: isTouchScrollEnabled,\n  });\n  canvas.addEventListener(\"touchend\", callback);\n  return () => {\n    canvas.removeEventListener(\"mouseover\", callback);\n    canvas.removeEventListener(\"mouseout\", callback);\n    canvas.removeEventListener(\"mousemove\", callback);\n    canvas.removeEventListener(\"mousedown\", callback);\n    canvas.removeEventListener(\"mouseup\", callback);\n    canvas.removeEventListener(\"touchmove\", callback);\n    canvas.removeEventListener(\"touchstart\", callback);\n    canvas.removeEventListener(\"touchend\", callback);\n  };\n};\n", "// Reference: https://github.com/braintree/sanitize-url/tree/main\nconst invalidProtocolRegex = /^([^\\w]*)(javascript|data|vbscript)/im;\nconst htmlEntitiesRegex = /&#(\\w+)(^\\w|;)?/g;\nconst htmlCtrlEntityRegex = /&(newline|tab);/gi;\nconst ctrlCharactersRegex =\n  /[\\u0000-\\u001F\\u007F-\\u009F\\u2000-\\u200D\\uFEFF]/gim;\nconst urlSchemeRegex = /^.+(:|&colon;)/gim;\nconst relativeFirstCharacters = [\".\", \"/\"];\nexport const BLANK_URL = \"about:blank\";\n\nfunction isRelativeUrlWithoutProtocol(url: string): boolean {\n  return relativeFirstCharacters.indexOf(url[0]) > -1;\n}\n\n// adapted from https://stackoverflow.com/a/29824550/2601552\nfunction decodeHtmlCharacters(str: string) {\n  const removedNullByte = str.replace(ctrlCharactersRegex, \"\");\n  return removedNullByte.replace(htmlEntitiesRegex, (match, dec) => {\n    return String.fromCharCode(dec);\n  });\n}\n\nexport function sanitizeUrl(url?: string): string {\n  if (!url) {\n    return BLANK_URL;\n  }\n\n  const sanitizedUrl = decodeHtmlCharacters(url)\n    .replace(htmlCtrlEntityRegex, \"\")\n    .replace(ctrlCharactersRegex, \"\")\n    .trim();\n\n  if (!sanitizedUrl) {\n    return BLANK_URL;\n  }\n\n  if (isRelativeUrlWithoutProtocol(sanitizedUrl)) {\n    return sanitizedUrl;\n  }\n\n  const urlSchemeParseResults = sanitizedUrl.match(urlSchemeRegex);\n\n  if (!urlSchemeParseResults) {\n    return sanitizedUrl;\n  }\n\n  const urlScheme = urlSchemeParseResults[0];\n\n  if (invalidProtocolRegex.test(urlScheme)) {\n    return BLANK_URL;\n  }\n\n  return sanitizedUrl;\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import * as rc from \"./rive_advanced.mjs\";\nimport packageData from \"package.json\";\nimport { Animation } from \"./animation\";\nimport { registerTouchInteractions, sanitizeUrl, BLANK_URL } from \"./utils\";\n\nclass RiveError extends Error {\n  public isHandledError = true;\n}\n\n// Note: Re-exporting a few types from rive_advanced.mjs to expose for high-level\n// API usage without re-defining their type definition here. May want to revisit\n// and see if we want to expose both types from rive.ts and rive_advanced.mjs in\n// the future\nexport type {\n  FileAsset,\n  AudioAsset,\n  FontAsset,\n  ImageAsset,\n} from \"./rive_advanced.mjs\";\n\n/**\n * Generic type for a parameterless void callback\n */\nexport type VoidCallback = () => void;\nexport type AssetLoadCallback = (\n  asset: rc.FileAsset,\n  bytes: Uint8Array,\n) => Boolean;\n\ninterface SetupRiveListenersOptions {\n  isTouchScrollEnabled?: boolean;\n}\n\n/**\n * Type for artboard bounds\n */\nexport type Bounds = rc.AABB;\n\n// #regions helpers\nconst resolveErrorMessage = (error: any): string =>\n  error && error.isHandledError\n    ? error.message\n    : \"Problem loading file; may be corrupt!\";\n\n// #region layout\n\n// Fit options for the canvas\nexport enum Fit {\n  Cover = \"cover\",\n  Contain = \"contain\",\n  Fill = \"fill\",\n  FitWidth = \"fitWidth\",\n  FitHeight = \"fitHeight\",\n  None = \"none\",\n  ScaleDown = \"scaleDown\",\n  Layout = \"layout\",\n}\n\n// Alignment options for the canvas\nexport enum Alignment {\n  Center = \"center\",\n  TopLeft = \"topLeft\",\n  TopCenter = \"topCenter\",\n  TopRight = \"topRight\",\n  CenterLeft = \"centerLeft\",\n  CenterRight = \"centerRight\",\n  BottomLeft = \"bottomLeft\",\n  BottomCenter = \"bottomCenter\",\n  BottomRight = \"bottomRight\",\n}\n\n// Interface for the Layout static method contructor\nexport interface LayoutParameters {\n  fit?: Fit;\n  alignment?: Alignment;\n  layoutScaleFactor?: number;\n  minX?: number;\n  minY?: number;\n  maxX?: number;\n  maxY?: number;\n}\n\n// Alignment options for Rive animations in a HTML canvas\nexport class Layout {\n  // Runtime fit and alignment are accessed every frame, so we cache their\n  // values to save cycles\n  private cachedRuntimeFit: rc.Fit;\n  private cachedRuntimeAlignment: rc.Alignment;\n\n  public readonly fit: Fit;\n  public readonly alignment: Alignment;\n  public readonly layoutScaleFactor: number;\n  public readonly minX: number;\n  public readonly minY: number;\n  public readonly maxX: number;\n  public readonly maxY: number;\n\n  constructor(params?: LayoutParameters) {\n    this.fit = params?.fit ?? Fit.Contain;\n    this.alignment = params?.alignment ?? Alignment.Center;\n    this.layoutScaleFactor = params?.layoutScaleFactor ?? 1;\n    this.minX = params?.minX ?? 0;\n    this.minY = params?.minY ?? 0;\n    this.maxX = params?.maxX ?? 0;\n    this.maxY = params?.maxY ?? 0;\n  }\n\n  // Alternative constructor to build a Layout from an interface/object\n  static new({\n    fit,\n    alignment,\n    minX,\n    minY,\n    maxX,\n    maxY,\n  }: LayoutParameters): Layout {\n    console.warn(\n      \"This function is deprecated: please use `new Layout({})` instead\",\n    );\n    return new Layout({ fit, alignment, minX, minY, maxX, maxY });\n  }\n\n  /**\n   * Makes a copy of the layout, replacing any specified parameters\n   */\n  public copyWith({\n    fit,\n    alignment,\n    layoutScaleFactor,\n    minX,\n    minY,\n    maxX,\n    maxY,\n  }: LayoutParameters): Layout {\n    return new Layout({\n      fit: fit ?? this.fit,\n      alignment: alignment ?? this.alignment,\n      layoutScaleFactor: layoutScaleFactor ?? this.layoutScaleFactor,\n      minX: minX ?? this.minX,\n      minY: minY ?? this.minY,\n      maxX: maxX ?? this.maxX,\n      maxY: maxY ?? this.maxY,\n    });\n  }\n\n  // Returns fit for the Wasm runtime format\n  public runtimeFit(rive: rc.RiveCanvas): rc.Fit {\n    if (this.cachedRuntimeFit) return this.cachedRuntimeFit;\n\n    let fit;\n    if (this.fit === Fit.Cover) fit = rive.Fit.cover;\n    else if (this.fit === Fit.Contain) fit = rive.Fit.contain;\n    else if (this.fit === Fit.Fill) fit = rive.Fit.fill;\n    else if (this.fit === Fit.FitWidth) fit = rive.Fit.fitWidth;\n    else if (this.fit === Fit.FitHeight) fit = rive.Fit.fitHeight;\n    else if (this.fit === Fit.ScaleDown) fit = rive.Fit.scaleDown;\n    else if (this.fit === Fit.Layout) fit = rive.Fit.layout;\n    else fit = rive.Fit.none;\n\n    this.cachedRuntimeFit = fit;\n    return fit;\n  }\n\n  // Returns alignment for the Wasm runtime format\n  public runtimeAlignment(rive: rc.RiveCanvas): rc.Alignment {\n    if (this.cachedRuntimeAlignment) return this.cachedRuntimeAlignment;\n\n    let alignment;\n    if (this.alignment === Alignment.TopLeft)\n      alignment = rive.Alignment.topLeft;\n    else if (this.alignment === Alignment.TopCenter)\n      alignment = rive.Alignment.topCenter;\n    else if (this.alignment === Alignment.TopRight)\n      alignment = rive.Alignment.topRight;\n    else if (this.alignment === Alignment.CenterLeft)\n      alignment = rive.Alignment.centerLeft;\n    else if (this.alignment === Alignment.CenterRight)\n      alignment = rive.Alignment.centerRight;\n    else if (this.alignment === Alignment.BottomLeft)\n      alignment = rive.Alignment.bottomLeft;\n    else if (this.alignment === Alignment.BottomCenter)\n      alignment = rive.Alignment.bottomCenter;\n    else if (this.alignment === Alignment.BottomRight)\n      alignment = rive.Alignment.bottomRight;\n    else alignment = rive.Alignment.center;\n\n    this.cachedRuntimeAlignment = alignment;\n    return alignment;\n  }\n}\n\n// #endregion\n\n// #region runtime\n\n// Callback type when looking for a runtime instance\nexport type RuntimeCallback = (rive: rc.RiveCanvas) => void;\n\n// Runtime singleton; use getInstance to provide a callback that returns the\n// Rive runtime\nexport class RuntimeLoader {\n  // Singleton helpers\n  private static runtime: rc.RiveCanvas;\n  // Flag to indicate that loading has started/completed\n  private static isLoading = false;\n  // List of callbacks for the runtime that come in while loading\n  private static callBackQueue: RuntimeCallback[] = [];\n  // Instance of the Rive runtime\n  private static rive: rc.RiveCanvas;\n  // Path to the Wasm file; default path works for testing only;\n  // if embedded wasm is used then this is never used.\n  private static wasmURL = `https://unpkg.com/${packageData.name}@${packageData.version}/rive.wasm`;\n\n  // Class is never instantiated\n  private constructor() {}\n\n  // Loads the runtime\n  private static loadRuntime(): void {\n    rc.default({\n      // Loads Wasm bundle\n      locateFile: () => RuntimeLoader.wasmURL,\n    })\n      .then((rive: rc.RiveCanvas) => {\n        RuntimeLoader.runtime = rive;\n        // Fire all the callbacks\n        while (RuntimeLoader.callBackQueue.length > 0) {\n          RuntimeLoader.callBackQueue.shift()?.(RuntimeLoader.runtime);\n        }\n      })\n      .catch((error) => {\n        // Capture specific error details\n        const errorDetails = {\n          message: error?.message || \"Unknown error\",\n          type: error?.name || \"Error\",\n          // Some browsers may provide additional WebAssembly-specific details\n          wasmError:\n            error instanceof WebAssembly.CompileError ||\n            error instanceof WebAssembly.RuntimeError,\n          originalError: error,\n        };\n\n        // Log detailed error for debugging\n        console.debug(\"Rive WASM load error details:\", errorDetails);\n\n        // In case unpkg fails, or the wasm was not supported, we try to load the fallback module from jsdelivr.\n        // This `rive_fallback.wasm` is compiled to support older architecture.\n        // TODO: (Gordon): preemptively test browser support and load the correct wasm file. Then use jsdelvr only if unpkg fails.\n        const backupJsdelivrUrl = `https://cdn.jsdelivr.net/npm/${packageData.name}@${packageData.version}/rive_fallback.wasm`;\n        if (RuntimeLoader.wasmURL.toLowerCase() !== backupJsdelivrUrl) {\n          console.warn(\n            `Failed to load WASM from ${RuntimeLoader.wasmURL} (${errorDetails.message}), trying jsdelivr as a backup`,\n          );\n          RuntimeLoader.setWasmUrl(backupJsdelivrUrl);\n          RuntimeLoader.loadRuntime();\n        } else {\n          const errorMessage = [\n            `Could not load Rive WASM file from ${RuntimeLoader.wasmURL} or ${backupJsdelivrUrl}.`,\n            \"Possible reasons:\",\n            \"- Network connection is down\",\n            \"- WebAssembly is not supported in this environment\",\n            \"- The WASM file is corrupted or incompatible\",\n            \"\\nError details:\",\n            `- Type: ${errorDetails.type}`,\n            `- Message: ${errorDetails.message}`,\n            `- WebAssembly-specific error: ${errorDetails.wasmError}`,\n            \"\\nTo resolve, you may need to:\",\n            \"1. Check your network connection\",\n            \"2. Set a new WASM source via RuntimeLoader.setWasmUrl()\",\n            \"3. Call RuntimeLoader.loadRuntime() again\",\n          ].join(\"\\n\");\n\n          console.error(errorMessage);\n        }\n      });\n  }\n\n  // Provides a runtime instance via a callback\n  public static getInstance(callback: RuntimeCallback): void {\n    // If it's not loading, start loading runtime\n    if (!RuntimeLoader.isLoading) {\n      RuntimeLoader.isLoading = true;\n      RuntimeLoader.loadRuntime();\n    }\n    if (!RuntimeLoader.runtime) {\n      RuntimeLoader.callBackQueue.push(callback);\n    } else {\n      callback(RuntimeLoader.runtime);\n    }\n  }\n\n  // Provides a runtime instance via a promise\n  public static awaitInstance(): Promise<rc.RiveCanvas> {\n    return new Promise<rc.RiveCanvas>((resolve) =>\n      RuntimeLoader.getInstance((rive: rc.RiveCanvas): void => resolve(rive)),\n    );\n  }\n\n  // Manually sets the wasm url\n  public static setWasmUrl(url: string): void {\n    RuntimeLoader.wasmURL = url;\n  }\n\n  // Gets the current wasm url\n  public static getWasmUrl(): string {\n    return RuntimeLoader.wasmURL;\n  }\n}\n\n// #endregion\n\n// #region state machines\n\nexport enum StateMachineInputType {\n  Number = 56,\n  Trigger = 58,\n  Boolean = 59,\n}\n\n/**\n * An input for a state machine\n */\nexport class StateMachineInput {\n  constructor(\n    public readonly type: StateMachineInputType,\n    private runtimeInput: rc.SMIInput,\n  ) {}\n\n  /**\n   * Returns the name of the input\n   */\n  public get name(): string {\n    return this.runtimeInput.name;\n  }\n\n  /**\n   * Returns the current value of the input\n   */\n  public get value(): number | boolean {\n    return this.runtimeInput.value;\n  }\n\n  /**\n   * Sets the value of the input\n   */\n  public set value(value: number | boolean) {\n    this.runtimeInput.value = value;\n  }\n\n  /**\n   * Fires a trigger; does nothing on Number or Boolean input types\n   */\n  public fire(): void {\n    if (this.type === StateMachineInputType.Trigger) {\n      this.runtimeInput.fire();\n    }\n  }\n\n  /**\n   * Deletes the input\n   */\n  public delete(): void {\n    this.runtimeInput = null;\n  }\n}\n\nexport enum RiveEventType {\n  General = 128,\n  OpenUrl = 131,\n}\n\nclass StateMachine {\n  /**\n   * Caches the inputs from the runtime\n   */\n  public readonly inputs: StateMachineInput[] = [];\n\n  /**\n   * Runtime state machine instance\n   */\n  public readonly instance: rc.StateMachineInstance;\n\n  /**\n   * @constructor\n   * @param stateMachine runtime state machine object\n   * @param instance runtime state machine instance object\n   */\n  constructor(\n    private stateMachine: rc.StateMachine,\n    runtime: rc.RiveCanvas,\n    public playing: boolean,\n    private artboard: rc.Artboard,\n  ) {\n    this.instance = new runtime.StateMachineInstance(stateMachine, artboard);\n    this.initInputs(runtime);\n  }\n\n  public get name(): string {\n    return this.stateMachine.name;\n  }\n\n  /**\n   * Returns a list of state names that have changed on this frame\n   */\n  public get statesChanged(): string[] {\n    const names: string[] = [];\n    for (let i = 0; i < this.instance.stateChangedCount(); i++) {\n      names.push(this.instance.stateChangedNameByIndex(i));\n    }\n    return names;\n  }\n\n  /**\n   * Advances the state machine instance by a given time.\n   * @param time - the time to advance the animation by in seconds\n   */\n  public advance(time: number) {\n    this.instance.advance(time);\n  }\n\n  /**\n   * Advances the state machine instance by a given time and apply changes to artboard.\n   * @param time - the time to advance the animation by in seconds\n   */\n  public advanceAndApply(time: number) {\n    this.instance.advanceAndApply(time);\n  }\n\n  /**\n   * Returns the number of events reported from the last advance call\n   * @returns Number of events reported\n   */\n  public reportedEventCount(): number {\n    return this.instance.reportedEventCount();\n  }\n\n  /**\n   * Returns a RiveEvent object emitted from the last advance call at the given index\n   * of a list of potentially multiple events. If an event at the index is not found,\n   * undefined is returned.\n   * @param i index of the event reported in a list of potentially multiple events\n   * @returns RiveEvent or extended RiveEvent object returned, or undefined\n   */\n  reportedEventAt(i: number): rc.OpenUrlEvent | rc.RiveEvent | undefined {\n    return this.instance.reportedEventAt(i);\n  }\n\n  /**\n   * Fetches references to the state machine's inputs and caches them\n   * @param runtime an instance of the runtime; needed for the SMIInput types\n   */\n  private initInputs(runtime: rc.RiveCanvas): void {\n    // Fetch the inputs from the runtime if we don't have them\n    for (let i = 0; i < this.instance.inputCount(); i++) {\n      const input = this.instance.input(i);\n      this.inputs.push(this.mapRuntimeInput(input, runtime));\n    }\n  }\n\n  /**\n   * Maps a runtime input to it's appropriate type\n   * @param input\n   */\n  private mapRuntimeInput(\n    input: rc.SMIInput,\n    runtime: rc.RiveCanvas,\n  ): StateMachineInput {\n    if (input.type === runtime.SMIInput.bool) {\n      return new StateMachineInput(\n        StateMachineInputType.Boolean,\n        input.asBool(),\n      );\n    } else if (input.type === runtime.SMIInput.number) {\n      return new StateMachineInput(\n        StateMachineInputType.Number,\n        input.asNumber(),\n      );\n    } else if (input.type === runtime.SMIInput.trigger) {\n      return new StateMachineInput(\n        StateMachineInputType.Trigger,\n        input.asTrigger(),\n      );\n    }\n  }\n\n  /**\n   * Deletes the backing Wasm state machine instance; once this is called, this\n   * state machine is no more.\n   */\n  public cleanup() {\n    this.inputs.forEach((input) => {\n      input.delete();\n    });\n    this.inputs.length = 0;\n    this.instance.delete();\n  }\n\n  public bindViewModelInstance(viewModelInstance: ViewModelInstance) {\n    if (viewModelInstance.runtimeInstance != null) {\n      this.instance.bindViewModelInstance(viewModelInstance.runtimeInstance);\n    }\n  }\n}\n\n// #endregion\n\n// #region animator\n\n/**\n * Manages animation\n */\nclass Animator {\n  /**\n   * Constructs a new animator\n   * @constructor\n   * @param runtime Rive runtime; needed to instance animations & state machines\n   * @param artboard the artboard that holds all animations and state machines\n   * @param animations optional list of animations\n   * @param stateMachines optional list of state machines\n   */\n  constructor(\n    private runtime: rc.RiveCanvas,\n    private artboard: rc.Artboard,\n    private eventManager: EventManager,\n    public readonly animations: Animation[] = [],\n    public readonly stateMachines: StateMachine[] = [],\n  ) {}\n\n  /**\n   * Adds animations and state machines by their names. If names are shared\n   * between animations & state machines, then the first one found will be\n   * created. Best not to use the same names for these in your Rive file.\n   * @param animatable the name(s) of animations and state machines to add\n   * @returns a list of names of the playing animations and state machines\n   */\n  public add(\n    animatables: string | string[],\n    playing: boolean,\n    fireEvent = true,\n  ): string[] {\n    animatables = mapToStringArray(animatables);\n    // If animatables is empty, play or pause everything\n    if (animatables.length === 0) {\n      this.animations.forEach((a) => (a.playing = playing));\n      this.stateMachines.forEach((m) => (m.playing = playing));\n    } else {\n      // Play/pause already instanced items, or create new instances\n      const instancedAnimationNames = this.animations.map((a) => a.name);\n      const instancedMachineNames = this.stateMachines.map((m) => m.name);\n      for (let i = 0; i < animatables.length; i++) {\n        const aIndex = instancedAnimationNames.indexOf(animatables[i]);\n        const mIndex = instancedMachineNames.indexOf(animatables[i]);\n        if (aIndex >= 0 || mIndex >= 0) {\n          if (aIndex >= 0) {\n            // Animation is instanced, play/pause it\n            this.animations[aIndex].playing = playing;\n          } else {\n            // State machine is instanced, play/pause it\n            this.stateMachines[mIndex].playing = playing;\n          }\n        } else {\n          // Try to create a new animation instance\n          const anim = this.artboard.animationByName(animatables[i]);\n          if (anim) {\n            const newAnimation = new Animation(\n              anim,\n              this.artboard,\n              this.runtime,\n              playing,\n            );\n            // Display the first frame of the specified animation\n            newAnimation.advance(0);\n            newAnimation.apply(1.0);\n            this.animations.push(newAnimation);\n          } else {\n            // Try to create a new state machine instance\n            const sm = this.artboard.stateMachineByName(animatables[i]);\n            if (sm) {\n              const newStateMachine = new StateMachine(\n                sm,\n                this.runtime,\n                playing,\n                this.artboard,\n              );\n              this.stateMachines.push(newStateMachine);\n            }\n          }\n        }\n      }\n    }\n    // Fire play/paused events for animations\n    if (fireEvent) {\n      if (playing) {\n        this.eventManager.fire({\n          type: EventType.Play,\n          data: this.playing,\n        });\n      } else {\n        this.eventManager.fire({\n          type: EventType.Pause,\n          data: this.paused,\n        });\n      }\n    }\n\n    return playing ? this.playing : this.paused;\n  }\n\n  /**\n   * Adds linear animations by their names.\n   * @param animatables the name(s) of animations to add\n   * @param playing whether animations should play on instantiation\n   */\n  public initLinearAnimations(animatables: string[], playing: boolean) {\n    // Play/pause already instanced items, or create new instances\n    // This validation is kept to maintain compatibility with current behavior.\n    // But given that it this is called during artboard initialization\n    // it should probably be safe to remove.\n    const instancedAnimationNames = this.animations.map((a) => a.name);\n    for (let i = 0; i < animatables.length; i++) {\n      const aIndex = instancedAnimationNames.indexOf(animatables[i]);\n      if (aIndex >= 0) {\n        this.animations[aIndex].playing = playing;\n      } else {\n        // Try to create a new animation instance\n        const anim = this.artboard.animationByName(animatables[i]);\n        if (anim) {\n          const newAnimation = new Animation(\n            anim,\n            this.artboard,\n            this.runtime,\n            playing,\n          );\n          // Display the first frame of the specified animation\n          newAnimation.advance(0);\n          newAnimation.apply(1.0);\n          this.animations.push(newAnimation);\n        } else {\n          console.error(`Animation with name ${animatables[i]} not found.`);\n        }\n      }\n    }\n  }\n\n  /**\n   * Adds state machines by their names.\n   * @param animatables the name(s) of state machines to add\n   * @param playing whether state machines should play on instantiation\n   */\n  public initStateMachines(animatables: string[], playing: boolean) {\n    // Play/pause already instanced items, or create new instances\n    // This validation is kept to maintain compatibility with current behavior.\n    // But given that it this is called during artboard initialization\n    // it should probably be safe to remove.\n    const instancedStateMachineNames = this.stateMachines.map((a) => a.name);\n    for (let i = 0; i < animatables.length; i++) {\n      const aIndex = instancedStateMachineNames.indexOf(animatables[i]);\n      if (aIndex >= 0) {\n        this.stateMachines[aIndex].playing = playing;\n      } else {\n        // Try to create a new state machine instance\n        const sm = this.artboard.stateMachineByName(animatables[i]);\n        if (sm) {\n          const newStateMachine = new StateMachine(\n            sm,\n            this.runtime,\n            playing,\n            this.artboard,\n          );\n          this.stateMachines.push(newStateMachine);\n          if (!playing) {\n            newStateMachine.advanceAndApply(0);\n          }\n        } else {\n          console.warn(`State Machine with name ${animatables[i]} not found.`);\n          // In order to maintain compatibility with current behavior, if a state machine is not found\n          // we look for an animation with the same name\n          this.initLinearAnimations([animatables[i]], playing);\n        }\n      }\n    }\n  }\n\n  /**\n   * Play the named animations/state machines\n   * @param animatables the names of the animations/machines to play; plays all if empty\n   * @returns a list of the playing items\n   */\n  public play(animatables: string | string[]): string[] {\n    return this.add(animatables, true);\n  }\n\n  /**\n   * Pauses named animations and state machines, or everything if nothing is\n   * specified\n   * @param animatables names of the animations and state machines to pause\n   * @returns a list of names of the animations and state machines paused\n   */\n  public pause(animatables: string[]): string[] {\n    return this.add(animatables, false);\n  }\n\n  /**\n   * Set time of named animations\n   * @param animations names of the animations to scrub\n   * @param value time scrub value, a floating point number to which the playhead is jumped\n   * @returns a list of names of the animations that were scrubbed\n   */\n  public scrub(animatables: string[], value: number): string[] {\n    const forScrubbing = this.animations.filter((a) =>\n      animatables.includes(a.name),\n    );\n    forScrubbing.forEach((a) => (a.scrubTo = value));\n    return forScrubbing.map((a) => a.name);\n  }\n\n  /**\n   * Returns a list of names of all animations and state machines currently\n   * playing\n   */\n  public get playing(): string[] {\n    return this.animations\n      .filter((a) => a.playing)\n      .map((a) => a.name)\n      .concat(this.stateMachines.filter((m) => m.playing).map((m) => m.name));\n  }\n\n  /**\n   * Returns a list of names of all animations and state machines currently\n   * paused\n   */\n  public get paused(): string[] {\n    return this.animations\n      .filter((a) => !a.playing)\n      .map((a) => a.name)\n      .concat(this.stateMachines.filter((m) => !m.playing).map((m) => m.name));\n  }\n\n  /**\n   * Stops and removes all named animations and state machines\n   * @param animatables animations and state machines to remove\n   * @returns a list of names of removed items\n   */\n  public stop(animatables?: string[] | string): string[] {\n    animatables = mapToStringArray(animatables);\n\n    // If nothing's specified, wipe them out, all of them\n    let removedNames: string[] = [];\n    // Stop everything\n    if (animatables.length === 0) {\n      removedNames = this.animations\n        .map((a) => a.name)\n        .concat(this.stateMachines.map((m) => m.name));\n      // Clean up before emptying the arrays\n      this.animations.forEach((a) => a.cleanup());\n      this.stateMachines.forEach((m) => m.cleanup());\n      // Empty out the arrays\n      this.animations.splice(0, this.animations.length);\n      this.stateMachines.splice(0, this.stateMachines.length);\n    } else {\n      // Remove only the named animations/state machines\n      const animationsToRemove = this.animations.filter((a) =>\n        animatables.includes(a.name),\n      );\n\n      animationsToRemove.forEach((a) => {\n        a.cleanup();\n        this.animations.splice(this.animations.indexOf(a), 1);\n      });\n      const machinesToRemove = this.stateMachines.filter((m) =>\n        animatables.includes(m.name),\n      );\n      machinesToRemove.forEach((m) => {\n        m.cleanup();\n        this.stateMachines.splice(this.stateMachines.indexOf(m), 1);\n      });\n      removedNames = animationsToRemove\n        .map((a) => a.name)\n        .concat(machinesToRemove.map((m) => m.name));\n    }\n\n    this.eventManager.fire({\n      type: EventType.Stop,\n      data: removedNames,\n    });\n\n    // Return the list of animations removed\n    return removedNames;\n  }\n\n  /**\n   * Returns true if at least one animation is active\n   */\n  public get isPlaying(): boolean {\n    return (\n      this.animations.reduce((acc, curr) => acc || curr.playing, false) ||\n      this.stateMachines.reduce((acc, curr) => acc || curr.playing, false)\n    );\n  }\n\n  /**\n   * Returns true if all animations are paused and there's at least one animation\n   */\n  public get isPaused(): boolean {\n    return (\n      !this.isPlaying &&\n      (this.animations.length > 0 || this.stateMachines.length > 0)\n    );\n  }\n\n  /**\n   * Returns true if there are no playing or paused animations/state machines\n   */\n  public get isStopped(): boolean {\n    return this.animations.length === 0 && this.stateMachines.length === 0;\n  }\n\n  /**\n   * If there are no animations or state machines, add the first one found\n   * @returns the name of the animation or state machine instanced\n   */\n  public atLeastOne(playing: boolean, fireEvent = true): string {\n    let instancedName: string;\n    if (this.animations.length === 0 && this.stateMachines.length === 0) {\n      if (this.artboard.animationCount() > 0) {\n        // Add the first animation\n        this.add(\n          [(instancedName = this.artboard.animationByIndex(0).name)],\n          playing,\n          fireEvent,\n        );\n      } else if (this.artboard.stateMachineCount() > 0) {\n        // Add the first state machine\n        this.add(\n          [(instancedName = this.artboard.stateMachineByIndex(0).name)],\n          playing,\n          fireEvent,\n        );\n      }\n    }\n    return instancedName;\n  }\n\n  /**\n   * Checks if any animations have looped and if so, fire the appropriate event\n   */\n  public handleLooping() {\n    for (const animation of this.animations.filter((a) => a.playing)) {\n      // Emit if the animation looped\n      if (animation.loopValue === 0 && animation.loopCount) {\n        animation.loopCount = 0;\n        // This is a one-shot; if it has ended, delete the instance\n        this.stop(animation.name);\n      } else if (animation.loopValue === 1 && animation.loopCount) {\n        this.eventManager.fire({\n          type: EventType.Loop,\n          data: { animation: animation.name, type: LoopType.Loop },\n        });\n        animation.loopCount = 0;\n      }\n      // Wasm indicates a loop at each time the animation\n      // changes direction, so a full loop/lap occurs every\n      // two loop counts\n      else if (animation.loopValue === 2 && animation.loopCount > 1) {\n        this.eventManager.fire({\n          type: EventType.Loop,\n          data: { animation: animation.name, type: LoopType.PingPong },\n        });\n        animation.loopCount = 0;\n      }\n    }\n  }\n\n  /**\n   * Checks if states have changed in state machines and fires a statechange\n   * event\n   */\n  public handleStateChanges() {\n    const statesChanged: string[] = [];\n    for (const stateMachine of this.stateMachines.filter((sm) => sm.playing)) {\n      statesChanged.push(...stateMachine.statesChanged);\n    }\n    if (statesChanged.length > 0) {\n      this.eventManager.fire({\n        type: EventType.StateChange,\n        data: statesChanged,\n      });\n    }\n  }\n\n  public handleAdvancing(time: number) {\n    this.eventManager.fire({\n      type: EventType.Advance,\n      data: time,\n    });\n  }\n}\n\n// #endregion\n\n// #region events\n\n/**\n * Supported event types triggered in Rive\n */\nexport enum EventType {\n  Load = \"load\",\n  LoadError = \"loaderror\",\n  Play = \"play\",\n  Pause = \"pause\",\n  Stop = \"stop\",\n  Loop = \"loop\",\n  Draw = \"draw\",\n  Advance = \"advance\",\n  StateChange = \"statechange\",\n  RiveEvent = \"riveevent\",\n  AudioStatusChange = \"audiostatuschange\", // internal event. TODO: split\n}\n\nexport type RiveEventPayload = rc.RiveEvent | rc.OpenUrlEvent;\n\n// Event reported by Rive for significant events during animation playback (i.e. play, pause, stop, etc.),\n// as well as for custom Rive events reported from the state machine defined at design-time.\nexport interface Event {\n  type: EventType;\n  data?: string | string[] | LoopEvent | number | RiveEventPayload | RiveFile;\n}\n\n/**\n * Looping types: one-shot, loop, and ping-pong\n */\nexport enum LoopType {\n  OneShot = \"oneshot\", // has value 0 in runtime\n  Loop = \"loop\", // has value 1 in runtime\n  PingPong = \"pingpong\", // has value 2 in runtime\n}\n\n/**\n * Loop events are returned through onloop callbacks\n */\nexport interface LoopEvent {\n  animation: string;\n  type: LoopType;\n}\n\n/**\n * Loop events are returned through onloop callbacks\n */\nexport type EventCallback = (event: Event) => void;\n\n/**\n * Event listeners registered with the event manager\n */\nexport interface EventListener {\n  type: EventType;\n  callback: EventCallback;\n}\n\n/**\n * FPS Reporting through callbacks sent to the WASM runtime\n */\nexport type FPSCallback = (fps: number) => void;\n\n// Manages Rive events and listeners\nclass EventManager {\n  constructor(private listeners: EventListener[] = []) {}\n\n  // Gets listeners of specified type\n  private getListeners(type: EventType): EventListener[] {\n    return this.listeners.filter((e) => e.type === type);\n  }\n\n  // Adds a listener\n  public add(listener: EventListener): void {\n    if (!this.listeners.includes(listener)) {\n      this.listeners.push(listener);\n    }\n  }\n\n  /**\n   * Removes a listener\n   * @param listener the listener with the callback to be removed\n   */\n  public remove(listener: EventListener): void {\n    // We can't simply look for the listener as it'll be a different instance to\n    // one originally subscribed. Find all the listeners of the right type and\n    // then check their callbacks which should match.\n    for (let i = 0; i < this.listeners.length; i++) {\n      const currentListener = this.listeners[i];\n      if (currentListener.type === listener.type) {\n        if (currentListener.callback === listener.callback) {\n          this.listeners.splice(i, 1);\n          break;\n        }\n      }\n    }\n  }\n\n  /**\n   * Clears all listeners of specified type, or every listener if no type is\n   * specified\n   * @param type the type of listeners to clear, or all listeners if not\n   * specified\n   */\n  public removeAll(type?: EventType) {\n    if (!type) {\n      this.listeners.splice(0, this.listeners.length);\n    } else {\n      this.listeners\n        .filter((l) => l.type === type)\n        .forEach((l) => this.remove(l));\n    }\n  }\n\n  // Fires an event\n  public fire(event: Event): void {\n    const eventListeners = this.getListeners(event.type);\n    eventListeners.forEach((listener) => listener.callback(event));\n  }\n}\n\n// #endregion\n\n// #region Manages a queue of tasks\n\n// A task in the queue; will fire the action when the queue is processed; will\n// also optionally fire an event.\nexport interface Task {\n  action?: VoidCallback;\n  event?: Event;\n}\n\n// Manages a queue of tasks\nclass TaskQueueManager {\n  private queue: Task[] = [];\n\n  constructor(private eventManager: EventManager) {}\n\n  // Adds a task top the queue\n  public add(task: Task): void {\n    this.queue.push(task);\n  }\n\n  // Processes all tasks in the queue\n  public process(): void {\n    while (this.queue.length > 0) {\n      const task = this.queue.shift();\n      if (task?.action) {\n        task.action();\n      }\n      if (task?.event) {\n        this.eventManager.fire(task.event);\n      }\n    }\n  }\n}\n\n// #endregion\n\n// #region Audio\n\nenum SystemAudioStatus {\n  AVAILABLE,\n  UNAVAILABLE,\n}\n\n// Class to handle audio context availability and status changes\nclass AudioManager extends EventManager {\n  private _started: boolean = false;\n  private _enabled: boolean = false;\n\n  private _status: SystemAudioStatus = SystemAudioStatus.UNAVAILABLE;\n  private _audioContext: AudioContext;\n\n  private async delay(time: number) {\n    return new Promise((resolve) => setTimeout(resolve, time));\n  }\n\n  private async timeout() {\n    return new Promise((_, reject) => setTimeout(reject, 50));\n  }\n\n  // Alerts animations on status changes and removes the listeners to avoid alerting twice.\n  private reportToListeners() {\n    this.fire({ type: EventType.AudioStatusChange });\n    this.removeAll();\n  }\n\n  /**\n   * The audio context has been resolved.\n   * Alert any listeners that we can now play audio.\n   * Rive will now play audio at the configured volume.\n   */\n  private async enableAudio() {\n    if (!this._enabled) {\n      this._enabled = true;\n      this._status = SystemAudioStatus.AVAILABLE;\n      this.reportToListeners();\n    }\n  }\n\n  /**\n   * Check if we are able to play audio.\n   *\n   * We currently check the audio context, when resume() returns before a timeout we know that the\n   * audio context is running and we can enable audio.\n   */\n  private async testAudio() {\n    if (\n      this._status === SystemAudioStatus.UNAVAILABLE &&\n      this._audioContext !== null\n    ) {\n      // if the audio context is not available, it will never resume,\n      // so the timeout will throw after 50ms and a new cycle will start\n      try {\n        await Promise.race([this._audioContext.resume(), this.timeout()]);\n        this.enableAudio();\n      } catch {\n        // we expect the promise race to timeout, which we ignore.\n      }\n    }\n  }\n\n  /**\n   * Establish audio for use with rive.\n   * We both test if we can use audio intermittently and listen for user interaction.\n   * The aim is to enable audio playback as soon as the browser allows this.\n   */\n  private async _establishAudio() {\n    if (!this._started) {\n      this._started = true;\n      // If window doesn't exist we assume they are not in a browser context\n      // so audio will not be blocked\n      if (typeof window == \"undefined\") {\n        this.enableAudio();\n      } else {\n        this._audioContext = new AudioContext();\n        this.listenForUserAction();\n        while (this._status === SystemAudioStatus.UNAVAILABLE) {\n          await this.testAudio();\n          await this.delay(1000);\n        }\n      }\n    }\n  }\n\n  private listenForUserAction() {\n    // NOTE: AudioContexts are ready immediately if requested in a ui callback\n    // we *could* re request one in this listener.\n    const _clickListener = async () => {\n      // note this has \"better\" results than calling `await this.testAudio()`\n      // as we force audio to be enabled in the current thread, rather than chancing\n      // the thread to be passed over for some other async context\n\n      this.enableAudio();\n    };\n    // NOTE: we should test this on mobile/pads\n    document.addEventListener(\"pointerdown\", _clickListener, {\n      once: true,\n    });\n  }\n\n  /**\n   * Establish the audio context for rive, this lets rive know that we can play audio.\n   */\n  public async establishAudio() {\n    this._establishAudio();\n  }\n\n  public get systemVolume() {\n    if (this._status === SystemAudioStatus.UNAVAILABLE) {\n      // We do an immediate test to avoid depending on the delay of the running test\n      this.testAudio();\n      return 0;\n    }\n    return 1;\n  }\n\n  public get status(): SystemAudioStatus {\n    return this._status;\n  }\n}\n\nconst audioManager = new AudioManager();\n\n// #endregion\n\n// #region Observers\n\ntype ObservedObject = {\n  onResize: Function;\n  element: HTMLCanvasElement;\n};\n\ntype MyResizeObserverType = {\n  observe: Function;\n  unobserve: Function;\n  disconnect: Function;\n};\n\nclass FakeResizeObserver {\n  observe() {}\n  unobserve() {}\n  disconnect() {}\n}\n\nconst MyResizeObserver = globalThis.ResizeObserver || FakeResizeObserver;\n\n/**\n * This class takes care of any observers that will be attached to an animation.\n * It should be treated as a singleton because observers are much more performant\n * when used for observing multiple elements by a single instance.\n */\n\nclass ObjectObservers {\n  private _elementsMap: Map<HTMLCanvasElement, ObservedObject> = new Map();\n\n  private _resizeObserver: MyResizeObserverType;\n\n  constructor() {\n    this._resizeObserver = new MyResizeObserver(this._onObserved);\n  }\n\n  /**\n   * Resize observers trigger both when the element changes its size and also when the\n   * element is added or removed from the document.\n   */\n  private _onObservedEntry = (entry: ResizeObserverEntry) => {\n    const observed = this._elementsMap.get(entry.target as HTMLCanvasElement);\n    if (observed !== null) {\n      observed.onResize(\n        entry.target.clientWidth == 0 || entry.target.clientHeight == 0,\n      );\n    } else {\n      this._resizeObserver.unobserve(entry.target);\n    }\n  };\n\n  private _onObserved = (entries: ResizeObserverEntry[]) => {\n    entries.forEach(this._onObservedEntry);\n  };\n\n  // Adds an observable element\n  public add(element: HTMLCanvasElement, onResize: Function) {\n    let observed: ObservedObject = {\n      onResize,\n      element,\n    };\n    this._elementsMap.set(element, observed);\n    this._resizeObserver.observe(element);\n    return observed;\n  }\n\n  // Removes an observable element\n  public remove(observed: ObservedObject) {\n    this._resizeObserver.unobserve(observed.element);\n    this._elementsMap.delete(observed.element);\n  }\n}\n\nconst observers = new ObjectObservers();\n\n// #endregion\n\n// #region Rive\n\n// Interface for the Rive static method contructor\nexport interface RiveParameters {\n  canvas: HTMLCanvasElement | OffscreenCanvas; // canvas is required\n  src?: string; // one of src or buffer or file is required\n  buffer?: ArrayBuffer; // one of src or buffer or file is required\n  riveFile?: RiveFile;\n  artboard?: string;\n  animations?: string | string[];\n  stateMachines?: string | string[];\n  layout?: Layout;\n  autoplay?: boolean;\n  useOffscreenRenderer?: boolean;\n  /**\n   * Allow the runtime to automatically load assets hosted in Rive's CDN.\n   * enabled by default.\n   */\n  enableRiveAssetCDN?: boolean;\n  /**\n   * Turn off Rive Listeners. This means state machines that have Listeners\n   * will not be invoked, and also, no event listeners pertaining to Listeners\n   * will be attached to the <canvas> element\n   */\n  shouldDisableRiveListeners?: boolean;\n  /**\n   * For Rive Listeners, allows scrolling behavior to still occur on canvas elements\n   * when a touch/drag action is performed on touch-enabled devices. Otherwise,\n   * scroll behavior may be prevented on touch/drag actions on the canvas by default.\n   */\n  isTouchScrollEnabled?: boolean;\n  /**\n   * Enable Rive Events to be handled by the runtime. This means any special Rive Event may have\n   * a side effect that takes place implicitly.\n   *\n   * For example, if during the render loop an OpenUrlEvent is detected, the\n   * browser may try to open the specified URL in the payload.\n   *\n   * This flag is false by default to prevent any unwanted behaviors from taking place.\n   * This means any special Rive Event will have to be handled manually by subscribing to\n   * EventType.RiveEvent\n   */\n  automaticallyHandleEvents?: boolean;\n  /**\n   * Rive will look for a default view model and view model instance to bind to the artboard\n   */\n  autoBind?: boolean;\n  onLoad?: EventCallback;\n  onLoadError?: EventCallback;\n  onPlay?: EventCallback;\n  onPause?: EventCallback;\n  onStop?: EventCallback;\n  onLoop?: EventCallback;\n  onStateChange?: EventCallback;\n  onAdvance?: EventCallback;\n  assetLoader?: AssetLoadCallback;\n  /**\n   * @deprecated Use `onLoad()` instead\n   */\n  onload?: EventCallback;\n  /**\n   * @deprecated Use `onLoadError()` instead\n   */\n  onloaderror?: EventCallback;\n  /**\n   * @deprecated Use `onPoad()` instead\n   */\n  onplay?: EventCallback;\n  /**\n   * @deprecated Use `onPause()` instead\n   */\n  onpause?: EventCallback;\n  /**\n   * @deprecated Use `onStop()` instead\n   */\n  onstop?: EventCallback;\n  /**\n   * @deprecated Use `onLoop()` instead\n   */\n  onloop?: EventCallback;\n  /**\n   * @deprecated Use `onStateChange()` instead\n   */\n  onstatechange?: EventCallback;\n}\n\n// Interface to Rive.load function\nexport interface RiveLoadParameters {\n  src?: string;\n  buffer?: ArrayBuffer;\n  riveFile?: RiveFile;\n  autoplay?: boolean;\n  autoBind?: boolean;\n  artboard?: string;\n  animations?: string | string[];\n  stateMachines?: string | string[];\n  useOffscreenRenderer?: boolean;\n  shouldDisableRiveListeners?: boolean;\n}\n\n// Interface ot Rive.reset function\nexport interface RiveResetParameters {\n  artboard?: string;\n  animations?: string | string[];\n  stateMachines?: string | string[];\n  autoplay?: boolean;\n  autoBind?: boolean;\n}\n// Interface to RiveFile.load function\nexport interface RiveFileParameters {\n  src?: string;\n  buffer?: ArrayBuffer;\n  assetLoader?: AssetLoadCallback;\n  enableRiveAssetCDN?: boolean;\n  onLoad?: EventCallback;\n  onLoadError?: EventCallback;\n}\n\nexport class RiveFile {\n  // Error message for missing source or buffer\n  private static readonly missingErrorMessage: string =\n    \"Rive source file or data buffer required\";\n\n  // Error message for file load error\n  private static readonly fileLoadErrorMessage: string =\n    \"The file failed to load\";\n\n  // A url to a Rive file; may be undefined if a buffer is specified\n  private src: string;\n\n  // Raw Rive file data; may be undefined if a src is specified\n  private buffer: ArrayBuffer;\n\n  // Wasm runtime\n  private runtime: rc.RiveCanvas;\n\n  // Runtime file\n  private file: rc.File;\n\n  // AssetLoadCallback: allows customizing asset loading for images and fonts.\n  private assetLoader: AssetLoadCallback;\n\n  // Allow the runtime to automatically load assets hosted in Rive's runtime.\n  private enableRiveAssetCDN: boolean = true;\n\n  // Holds event listeners\n  private eventManager: EventManager;\n\n  private referenceCount: number = 0;\n\n  private destroyed: boolean = false;\n\n  constructor(params: RiveFileParameters) {\n    this.src = params.src;\n    this.buffer = params.buffer;\n\n    if (params.assetLoader) this.assetLoader = params.assetLoader;\n    this.enableRiveAssetCDN =\n      typeof params.enableRiveAssetCDN == \"boolean\"\n        ? params.enableRiveAssetCDN\n        : true;\n\n    // New event management system\n    this.eventManager = new EventManager();\n    if (params.onLoad) this.on(EventType.Load, params.onLoad);\n    if (params.onLoadError) this.on(EventType.LoadError, params.onLoadError);\n  }\n\n  private async initData() {\n    if (this.src) {\n      this.buffer = await loadRiveFile(this.src);\n    }\n    if (this.destroyed) {\n      return;\n    }\n    let loader;\n    if (this.assetLoader) {\n      loader = new this.runtime.CustomFileAssetLoader({\n        loadContents: this.assetLoader,\n      });\n    }\n    // Load the Rive file\n    this.file = await this.runtime.load(\n      new Uint8Array(this.buffer),\n      loader,\n      this.enableRiveAssetCDN,\n    );\n    if (this.destroyed) {\n      this.file?.delete();\n      this.file = null;\n      return;\n    }\n    if (this.file !== null) {\n      this.eventManager.fire({\n        type: EventType.Load,\n        data: this,\n      });\n    } else {\n      this.eventManager.fire({\n        type: EventType.LoadError,\n        data: null,\n      });\n      throw new Error(RiveFile.fileLoadErrorMessage);\n    }\n  }\n\n  public async init() {\n    // If no source file url specified, it's a bust\n    if (!this.src && !this.buffer) {\n      throw new Error(RiveFile.missingErrorMessage);\n    }\n    this.runtime = await RuntimeLoader.awaitInstance();\n\n    if (this.destroyed) {\n      return;\n    }\n    await this.initData();\n  }\n\n  /**\n   * Subscribe to Rive-generated events\n   * @param type the type of event to subscribe to\n   * @param callback callback to fire when the event occurs\n   */\n  public on(type: EventType, callback: EventCallback) {\n    this.eventManager.add({\n      type: type,\n      callback: callback,\n    });\n  }\n\n  /**\n   * Unsubscribes from a Rive-generated event\n   * @param type the type of event to unsubscribe from\n   * @param callback the callback to unsubscribe\n   */\n  public off(type: EventType, callback: EventCallback) {\n    this.eventManager.remove({\n      type: type,\n      callback: callback,\n    });\n  }\n\n  public cleanup() {\n    this.referenceCount -= 1;\n    if (this.referenceCount <= 0) {\n      this.removeAllRiveEventListeners();\n      this.file?.delete();\n      this.file = null;\n      this.destroyed = true;\n    }\n  }\n\n  /**\n   * Unsubscribes all Rive listeners from an event type, or everything if no type is\n   * given\n   * @param type the type of event to unsubscribe from, or all types if\n   * undefined\n   */\n  public removeAllRiveEventListeners(type?: EventType) {\n    this.eventManager.removeAll(type);\n  }\n\n  public getInstance(): rc.File {\n    if (this.file !== null) {\n      this.referenceCount += 1;\n      return this.file;\n    }\n  }\n}\n\nexport class Rive {\n  // Canvas in which to render the artboard\n  private readonly canvas: HTMLCanvasElement | OffscreenCanvas;\n\n  // A url to a Rive file; may be undefined if a buffer is specified\n  private src: string;\n\n  // Raw Rive file data; may be undefined if a src is specified\n  private buffer: ArrayBuffer;\n\n  // The layout for rendering in the canvas\n  private _layout: Layout;\n\n  // The runtime renderer\n  private renderer: rc.WrappedRenderer;\n\n  // Tracks if a Rive file is loaded\n  private loaded = false;\n\n  // Tracks if a Rive file is destroyed\n  private destroyed = false;\n\n  // Reference of an object that handles any observers for the animation\n  private _observed: ObservedObject | null = null;\n\n  /**\n   * Tracks if a Rive file is loaded; we need this in addition to loaded as some\n   * commands (e.g. contents) can be called as soon as the file is loaded.\n   * However, playback commands need to be queued and run in order once initial\n   * animations and autoplay has been sorted out. This applies to play, pause,\n   * and start.\n   */\n  private readyForPlaying = false;\n\n  // Wasm runtime\n  private runtime: rc.RiveCanvas;\n\n  // Runtime artboard\n  private artboard: rc.Artboard | null = null;\n\n  // place to clear up event listeners\n  private eventCleanup: VoidCallback | null = null;\n\n  // Runtime file\n  private file: rc.File;\n\n  // Rive file instance\n  private riveFile: RiveFile;\n\n  // Holds event listeners\n  private eventManager: EventManager;\n\n  // Manages the loading task queue\n  private taskQueue: TaskQueueManager;\n\n  // Animator: manages animations and state machines\n  private animator: Animator;\n\n  // AssetLoadCallback: allows customizing asset loading for images and fonts.\n  private assetLoader: AssetLoadCallback;\n\n  // Error message for missing source or buffer\n  private static readonly missingErrorMessage: string =\n    \"Rive source file or data buffer required\";\n\n  // Error message for removed rive file\n  private static readonly cleanupErrorMessage: string =\n    \"Attempt to use file after calling cleanup.\";\n\n  private shouldDisableRiveListeners = false;\n\n  private automaticallyHandleEvents = false;\n\n  // Allow the runtime to automatically load assets hosted in Rive's runtime.\n  private enableRiveAssetCDN = true;\n\n  // Keep a local value of the set volume to update it asynchronously\n  private _volume = 1;\n\n  // Keep a local value of the set width to update it asynchronously\n  private _artboardWidth: number | undefined = undefined;\n\n  // Keep a local value of the set height to update it asynchronously\n  private _artboardHeight: number | undefined = undefined;\n\n  // Keep a local value of the device pixel ratio used in rendering and canvas/artboard resizing\n  private _devicePixelRatioUsed = 1;\n\n  // Whether the canvas element's size is 0\n  private _hasZeroSize = false;\n\n  // Audio event listener\n  private _audioEventListener: EventListener | null = null;\n\n  // draw method bound to the class\n  private _boundDraw: (t: number) => void | null = null;\n\n  private _viewModelInstance: ViewModelInstance | null = null;\n  private _dataEnums: DataEnum[] | null = null;\n\n  // Durations to generate a frame for the last second. Used for performance profiling.\n  public durations: number[] = [];\n  public frameTimes: number[] = [];\n  public frameCount = 0;\n  public isTouchScrollEnabled = false;\n\n  constructor(params: RiveParameters) {\n    this._boundDraw = this.draw.bind(this);\n    this.canvas = params.canvas;\n    if (params.canvas.constructor === HTMLCanvasElement) {\n      this._observed = observers.add(\n        this.canvas as HTMLCanvasElement,\n        this.onCanvasResize,\n      );\n    }\n    this.src = params.src;\n    this.buffer = params.buffer;\n    this.riveFile = params.riveFile;\n    this.layout = params.layout ?? new Layout();\n    this.shouldDisableRiveListeners = !!params.shouldDisableRiveListeners;\n    this.isTouchScrollEnabled = !!params.isTouchScrollEnabled;\n    this.automaticallyHandleEvents = !!params.automaticallyHandleEvents;\n    this.enableRiveAssetCDN =\n      params.enableRiveAssetCDN === undefined\n        ? true\n        : params.enableRiveAssetCDN;\n\n    // New event management system\n    this.eventManager = new EventManager();\n    if (params.onLoad) this.on(EventType.Load, params.onLoad);\n    if (params.onLoadError) this.on(EventType.LoadError, params.onLoadError);\n    if (params.onPlay) this.on(EventType.Play, params.onPlay);\n    if (params.onPause) this.on(EventType.Pause, params.onPause);\n    if (params.onStop) this.on(EventType.Stop, params.onStop);\n    if (params.onLoop) this.on(EventType.Loop, params.onLoop);\n    if (params.onStateChange)\n      this.on(EventType.StateChange, params.onStateChange);\n    if (params.onAdvance) this.on(EventType.Advance, params.onAdvance);\n\n    /**\n     * @deprecated Use camelCase'd versions instead.\n     */\n    if (params.onload && !params.onLoad) this.on(EventType.Load, params.onload);\n    if (params.onloaderror && !params.onLoadError)\n      this.on(EventType.LoadError, params.onloaderror);\n    if (params.onplay && !params.onPlay) this.on(EventType.Play, params.onplay);\n    if (params.onpause && !params.onPause)\n      this.on(EventType.Pause, params.onpause);\n    if (params.onstop && !params.onStop) this.on(EventType.Stop, params.onstop);\n    if (params.onloop && !params.onLoop) this.on(EventType.Loop, params.onloop);\n    if (params.onstatechange && !params.onStateChange)\n      this.on(EventType.StateChange, params.onstatechange);\n\n    /**\n     * Asset loading\n     */\n    if (params.assetLoader) this.assetLoader = params.assetLoader;\n\n    // Hook up the task queue\n    this.taskQueue = new TaskQueueManager(this.eventManager);\n\n    this.init({\n      src: this.src,\n      buffer: this.buffer,\n      riveFile: this.riveFile,\n      autoplay: params.autoplay,\n      autoBind: params.autoBind,\n      animations: params.animations,\n      stateMachines: params.stateMachines,\n      artboard: params.artboard,\n      useOffscreenRenderer: params.useOffscreenRenderer,\n    });\n  }\n\n  public get viewModelCount(): number {\n    return this.file.viewModelCount();\n  }\n\n  // Alternative constructor to build a Rive instance from an interface/object\n  public static new(params: RiveParameters): Rive {\n    console.warn(\n      \"This function is deprecated: please use `new Rive({})` instead\",\n    );\n    return new Rive(params);\n  }\n\n  // Event handler for when audio context becomes available\n  private onSystemAudioChanged() {\n    this.volume = this._volume;\n  }\n\n  private onCanvasResize = (hasZeroSize: boolean) => {\n    const toggledDisplay = this._hasZeroSize !== hasZeroSize;\n    this._hasZeroSize = hasZeroSize;\n    if (!hasZeroSize) {\n      if (toggledDisplay) {\n        this.resizeDrawingSurfaceToCanvas();\n      }\n    } else if (!this._layout.maxX || !this._layout.maxY) {\n      this.resizeToCanvas();\n    }\n  };\n\n  // Initializes the Rive object either from constructor or load()\n  private init({\n    src,\n    buffer,\n    riveFile,\n    animations,\n    stateMachines,\n    artboard,\n    autoplay = false,\n    useOffscreenRenderer = false,\n    autoBind = false,\n  }: RiveLoadParameters): void {\n    if (this.destroyed) {\n      return;\n    }\n    this.src = src;\n    this.buffer = buffer;\n    this.riveFile = riveFile;\n\n    // If no source file url specified, it's a bust\n    if (!this.src && !this.buffer && !this.riveFile) {\n      throw new RiveError(Rive.missingErrorMessage);\n    }\n\n    // List of animations that should be initialized.\n    const startingAnimationNames = mapToStringArray(animations);\n\n    // List of state machines that should be initialized\n    const startingStateMachineNames = mapToStringArray(stateMachines);\n\n    // Ensure loaded is marked as false if loading new file\n    this.loaded = false;\n    this.readyForPlaying = false;\n\n    // Ensure the runtime is loaded\n    RuntimeLoader.awaitInstance()\n      .then((runtime) => {\n        if (this.destroyed) {\n          return;\n        }\n        this.runtime = runtime;\n\n        this.removeRiveListeners();\n        this.deleteRiveRenderer();\n\n        // Get the canvas where you want to render the animation and create a renderer\n        this.renderer = this.runtime.makeRenderer(\n          this.canvas,\n          useOffscreenRenderer,\n        );\n\n        // Initial size adjustment based on devicePixelRatio if no width/height are\n        // specified explicitly\n        if (!(this.canvas.width || this.canvas.height)) {\n          this.resizeDrawingSurfaceToCanvas();\n        }\n\n        // Load Rive data from a source uri or a data buffer\n        this.initData(\n          artboard,\n          startingAnimationNames,\n          startingStateMachineNames,\n          autoplay,\n          autoBind,\n        )\n          .then(() => this.setupRiveListeners())\n          .catch((e) => {\n            console.error(e);\n          });\n      })\n      .catch((e) => {\n        console.error(e);\n      });\n  }\n\n  /**\n   * Setup Rive Listeners on the canvas\n   * @param riveListenerOptions - Enables TouchEvent events on the canvas. Set to true to allow\n   * touch scrolling on the canvas element on touch-enabled devices\n   * i.e. { isTouchScrollEnabled: true }\n   */\n  public setupRiveListeners(\n    riveListenerOptions?: SetupRiveListenersOptions,\n  ): void {\n    if (this.eventCleanup) {\n      this.eventCleanup();\n    }\n    if (!this.shouldDisableRiveListeners) {\n      const activeStateMachines = (this.animator.stateMachines || [])\n        .filter((sm) => sm.playing && this.runtime.hasListeners(sm.instance))\n        .map((sm) => sm.instance);\n      let touchScrollEnabledOption = this.isTouchScrollEnabled;\n      if (\n        riveListenerOptions &&\n        \"isTouchScrollEnabled\" in riveListenerOptions\n      ) {\n        touchScrollEnabledOption = riveListenerOptions.isTouchScrollEnabled;\n      }\n      this.eventCleanup = registerTouchInteractions({\n        canvas: this.canvas,\n        artboard: this.artboard,\n        stateMachines: activeStateMachines,\n        renderer: this.renderer,\n        rive: this.runtime,\n        fit: this._layout.runtimeFit(this.runtime),\n        alignment: this._layout.runtimeAlignment(this.runtime),\n        isTouchScrollEnabled: touchScrollEnabledOption,\n        layoutScaleFactor: this._layout.layoutScaleFactor,\n      });\n    }\n  }\n\n  /**\n   * Remove Rive Listeners setup on the canvas\n   */\n  public removeRiveListeners(): void {\n    if (this.eventCleanup) {\n      this.eventCleanup();\n      this.eventCleanup = null;\n    }\n  }\n\n  /**\n   * If the instance has audio and the system audio is not ready\n   * we hook the instance to the audio manager\n   */\n  private initializeAudio() {\n    // Initialize audio if needed\n    if (audioManager.status == SystemAudioStatus.UNAVAILABLE) {\n      if (this.artboard?.hasAudio && this._audioEventListener === null) {\n        this._audioEventListener = {\n          type: EventType.AudioStatusChange,\n          callback: () => this.onSystemAudioChanged(),\n        };\n        audioManager.add(this._audioEventListener);\n        audioManager.establishAudio();\n      }\n    }\n  }\n\n  private initArtboardSize() {\n    if (!this.artboard) return;\n\n    // Use preset values if they are not undefined\n    this._artboardWidth = this.artboard.width =\n      this._artboardWidth || this.artboard.width;\n    this._artboardHeight = this.artboard.height =\n      this._artboardHeight || this.artboard.height;\n  }\n\n  // Initializes runtime with Rive data and preps for playing\n  private async initData(\n    artboardName: string,\n    animationNames: string[],\n    stateMachineNames: string[],\n    autoplay: boolean,\n    autoBind: boolean,\n  ): Promise<void> {\n    try {\n      if (this.riveFile == null) {\n        this.riveFile = new RiveFile({\n          src: this.src,\n          buffer: this.buffer,\n          enableRiveAssetCDN: this.enableRiveAssetCDN,\n          assetLoader: this.assetLoader,\n        });\n        await this.riveFile.init();\n      }\n      // Check for riveFile in case it has been cleaned up while initializing;\n      if (!this.riveFile) {\n        throw new RiveError(Rive.cleanupErrorMessage);\n      }\n      this.file = this.riveFile.getInstance();\n      // Initialize and draw frame\n      this.initArtboard(\n        artboardName,\n        animationNames,\n        stateMachineNames,\n        autoplay,\n        autoBind,\n      );\n\n      // Initialize the artboard size\n      this.initArtboardSize();\n\n      // Check for audio\n      this.initializeAudio();\n\n      // Everything's set up, emit a load event\n      this.loaded = true;\n      this.eventManager.fire({\n        type: EventType.Load,\n        data: this.src ?? \"buffer\",\n      });\n\n      // Flag ready for playback commands and clear the task queue; this order\n      // is important or it may infinitely recurse\n      this.readyForPlaying = true;\n      this.taskQueue.process();\n\n      this.drawFrame();\n\n      return Promise.resolve();\n    } catch (error) {\n      const msg = resolveErrorMessage(error);\n      console.warn(msg);\n      this.eventManager.fire({ type: EventType.LoadError, data: msg });\n      return Promise.reject(msg);\n    }\n  }\n\n  // Initialize for playback\n  private initArtboard(\n    artboardName: string,\n    animationNames: string[],\n    stateMachineNames: string[],\n    autoplay: boolean,\n    autoBind: boolean,\n  ): void {\n    if (!this.file) {\n      return;\n    }\n    // Fetch the artboard\n    const rootArtboard = artboardName\n      ? this.file.artboardByName(artboardName)\n      : this.file.defaultArtboard();\n\n    // Check we have a working artboard\n    if (!rootArtboard) {\n      const msg = \"Invalid artboard name or no default artboard\";\n      console.warn(msg);\n      this.eventManager.fire({ type: EventType.LoadError, data: msg });\n      return;\n    }\n\n    this.artboard = rootArtboard;\n    rootArtboard.volume = this._volume * audioManager.systemVolume;\n\n    // Check that the artboard has at least 1 animation\n    if (this.artboard.animationCount() < 1) {\n      const msg = \"Artboard has no animations\";\n      this.eventManager.fire({ type: EventType.LoadError, data: msg });\n      throw msg;\n    }\n\n    // Initialize the animator\n    this.animator = new Animator(\n      this.runtime,\n      this.artboard,\n      this.eventManager,\n    );\n\n    // Initialize the animations; as loaded hasn't happened yet, we need to\n    // suppress firing the play/pause events until the load event has fired. To\n    // do this we tell the animator to suppress firing events, and add event\n    // firing to the task queue.\n    let instanceNames: string[];\n    if (animationNames.length > 0 || stateMachineNames.length > 0) {\n      instanceNames = animationNames.concat(stateMachineNames);\n      this.animator.initLinearAnimations(animationNames, autoplay);\n      this.animator.initStateMachines(stateMachineNames, autoplay);\n    } else {\n      instanceNames = [this.animator.atLeastOne(autoplay, false)];\n    }\n    // Queue up firing the playback events\n    this.taskQueue.add({\n      event: {\n        type: autoplay ? EventType.Play : EventType.Pause,\n        data: instanceNames,\n      },\n    });\n\n    if (autoBind) {\n      const viewModel = this.file.defaultArtboardViewModel(rootArtboard);\n      if (viewModel !== null) {\n        const runtimeInstance = viewModel.defaultInstance();\n        if (runtimeInstance !== null) {\n          const viewModelInstance = new ViewModelInstance(\n            runtimeInstance,\n            null,\n          );\n          this.bindViewModelInstance(viewModelInstance);\n        }\n      }\n    }\n  }\n\n  // Draws the current artboard frame\n  public drawFrame() {\n    if (document?.timeline?.currentTime) {\n      if (this.loaded && this.artboard && !this.frameRequestId) {\n        this._boundDraw(document!.timeline!.currentTime as number);\n        this.runtime?.resolveAnimationFrame();\n      }\n    } else {\n      this.startRendering();\n    }\n  }\n\n  // Tracks the last timestamp at which the animation was rendered. Used only in\n  // draw().\n  private lastRenderTime: number;\n\n  // Tracks the current animation frame request\n  private frameRequestId: number | null;\n\n  /**\n   * Used be draw to track when a second of active rendering time has passed.\n   * Used for debugging purposes\n   */\n  private renderSecondTimer = 0;\n\n  /**\n   * Draw rendering loop; renders animation frames at the correct time interval.\n   * @param time the time at which to render a frame\n   */\n  private draw(time: number, onSecond?: VoidCallback): void {\n    // Clear the frameRequestId, as we're now rendering a fresh frame\n    this.frameRequestId = null;\n\n    const before = performance.now();\n\n    // On the first pass, make sure lastTime has a valid value\n    if (!this.lastRenderTime) {\n      this.lastRenderTime = time;\n    }\n\n    // Handle the onSecond callback\n    this.renderSecondTimer += time - this.lastRenderTime;\n    if (this.renderSecondTimer > 5000) {\n      this.renderSecondTimer = 0;\n      onSecond?.();\n    }\n\n    // Calculate the elapsed time between frames in seconds\n    const elapsedTime = (time - this.lastRenderTime) / 1000;\n    this.lastRenderTime = time;\n\n    // - Advance non-paused animations by the elapsed number of seconds\n    // - Advance any animations that require scrubbing\n    // - Advance to the first frame even when autoplay is false\n    const activeAnimations = this.animator.animations\n      .filter((a) => a.playing || a.needsScrub)\n      // The scrubbed animations must be applied first to prevent weird artifacts\n      // if the playing animations conflict with the scrubbed animating attribuates.\n      .sort((first) => (first.needsScrub ? -1 : 1));\n    for (const animation of activeAnimations) {\n      animation.advance(elapsedTime);\n      if (animation.instance.didLoop) {\n        animation.loopCount += 1;\n      }\n      animation.apply(1.0);\n    }\n\n    // - Advance non-paused state machines by the elapsed number of seconds\n    // - Advance to the first frame even when autoplay is false\n    const activeStateMachines = this.animator.stateMachines.filter(\n      (a) => a.playing,\n    );\n    for (const stateMachine of activeStateMachines) {\n      // Check for events before the current frame's state machine advance\n      const numEventsReported = stateMachine.reportedEventCount();\n      if (numEventsReported) {\n        for (let i = 0; i < numEventsReported; i++) {\n          const event = stateMachine.reportedEventAt(i);\n\n          if (event) {\n            if (event.type === RiveEventType.OpenUrl) {\n              this.eventManager.fire({\n                type: EventType.RiveEvent,\n                data: event as rc.OpenUrlEvent,\n              });\n              // Handle the event side effect if explicitly enabled\n              if (this.automaticallyHandleEvents) {\n                const newAnchorTag = document.createElement(\"a\");\n                const { url, target } = event as rc.OpenUrlEvent;\n\n                const sanitizedUrl = sanitizeUrl(url);\n                url && newAnchorTag.setAttribute(\"href\", sanitizedUrl);\n                target && newAnchorTag.setAttribute(\"target\", target);\n                if (sanitizedUrl && sanitizedUrl !== BLANK_URL) {\n                  newAnchorTag.click();\n                }\n              }\n            } else {\n              this.eventManager.fire({\n                type: EventType.RiveEvent,\n                data: event as rc.RiveEvent,\n              });\n            }\n          }\n        }\n      }\n      stateMachine.advanceAndApply(elapsedTime);\n      // stateMachine.instance.apply(this.artboard);\n    }\n\n    // Once the animations have been applied to the artboard, advance it\n    // by the elapsed time.\n    if (this.animator.stateMachines.length == 0) {\n      this.artboard.advance(elapsedTime);\n    }\n\n    const { renderer } = this;\n    // Canvas must be wiped to prevent artifacts\n    renderer.clear();\n    renderer.save();\n\n    // Update the renderer alignment if necessary\n    this.alignRenderer();\n\n    // Do not draw on 0 canvas size\n    if (!this._hasZeroSize) {\n      this.artboard.draw(renderer);\n    }\n\n    renderer.restore();\n    renderer.flush();\n\n    // Check for any animations that looped\n    this.animator.handleLooping();\n\n    // Check for any state machines that had a state change\n    this.animator.handleStateChanges();\n\n    // Report advanced time\n    this.animator.handleAdvancing(elapsedTime);\n\n    // Add duration to create frame to durations array\n    this.frameCount++;\n    const after = performance.now();\n    this.frameTimes.push(after);\n    this.durations.push(after - before);\n    while (this.frameTimes[0] <= after - 1000) {\n      this.frameTimes.shift();\n      this.durations.shift();\n    }\n\n    this._viewModelInstance?.handleCallbacks();\n\n    // Calling requestAnimationFrame will rerun draw() at the correct rate:\n    // https://developer.mozilla.org/en-US/docs/Web/API/Canvas_API/Tutorial/Basic_animations\n    if (this.animator.isPlaying) {\n      // Request a new rendering frame\n      this.startRendering();\n    } else if (this.animator.isPaused) {\n      // Reset the end time so on playback it starts at the correct frame\n      this.lastRenderTime = 0;\n    } else if (this.animator.isStopped) {\n      // Reset animation instances, artboard and time\n      // TODO: implement this properly when we have instancing\n      // this.initArtboard();\n      // this.drawFrame();\n      this.lastRenderTime = 0;\n    }\n  }\n\n  /**\n   * Align the renderer\n   */\n  private alignRenderer(): void {\n    const { renderer, runtime, _layout, artboard } = this;\n    // Align things up safe in the knowledge we can restore if changed\n    renderer.align(\n      _layout.runtimeFit(runtime),\n      _layout.runtimeAlignment(runtime),\n      {\n        minX: _layout.minX,\n        minY: _layout.minY,\n        maxX: _layout.maxX,\n        maxY: _layout.maxY,\n      },\n      artboard.bounds,\n      this._devicePixelRatioUsed * _layout.layoutScaleFactor,\n    );\n  }\n\n  public get fps() {\n    return this.durations.length;\n  }\n\n  public get frameTime() {\n    if (this.durations.length === 0) {\n      return 0;\n    }\n    return (\n      this.durations.reduce((a, b) => a + b, 0) / this.durations.length\n    ).toFixed(4);\n  }\n\n  /**\n   * Cleans up all Wasm-generated objects that need to be manually destroyed:\n   * artboard instances, animation instances, state machine instances,\n   * renderer instance, file and runtime.\n   *\n   * Once this is called, you will need to initialise a new instance of the\n   * Rive class\n   */\n  public cleanup() {\n    this.destroyed = true;\n    // Stop the renderer if it hasn't already been stopped.\n    this.stopRendering();\n    // Clean up any artboard, animation or state machine instances.\n    this.cleanupInstances();\n    // Remove from observer\n    if (this._observed !== null) {\n      observers.remove(this._observed);\n    }\n    this.removeRiveListeners();\n    if (this.file) {\n      this.riveFile?.cleanup();\n      this.file = null;\n    }\n    this.riveFile = null;\n    this.deleteRiveRenderer();\n    if (this._audioEventListener !== null) {\n      audioManager.remove(this._audioEventListener);\n      this._audioEventListener = null;\n    }\n    this._viewModelInstance?.cleanup();\n    this._viewModelInstance = null;\n    this._dataEnums = null;\n  }\n\n  /**\n   * Cleans up the Renderer object. Only call this API if you no longer\n   * need to render Rive content in your session.\n   */\n  public deleteRiveRenderer() {\n    this.renderer?.delete();\n    this.renderer = null;\n  }\n\n  /**\n   * Cleans up any Wasm-generated objects that need to be manually destroyed:\n   * artboard instances, animation instances, state machine instances.\n   *\n   * Once this is called, things will need to be reinitialized or bad things\n   * might happen.\n   */\n  public cleanupInstances() {\n    if (this.eventCleanup !== null) {\n      this.eventCleanup();\n    }\n    // Delete all animation and state machine instances\n    this.stop();\n    if (this.artboard) {\n      this.artboard.delete();\n      this.artboard = null;\n    }\n  }\n\n  /**\n   * Tries to query the setup Artboard for a text run node with the given name.\n   *\n   * @param textRunName - Name of the text run node associated with a text object\n   * @returns - TextValueRun node or undefined if the text run cannot be queried\n   */\n  private retrieveTextRun(textRunName: string): rc.TextValueRun | undefined {\n    if (!textRunName) {\n      console.warn(\"No text run name provided\");\n      return;\n    }\n    if (!this.artboard) {\n      console.warn(\"Tried to access text run, but the Artboard is null\");\n      return;\n    }\n    const textRun: rc.TextValueRun = this.artboard.textRun(textRunName);\n    if (!textRun) {\n      console.warn(\n        `Could not access a text run with name '${textRunName}' in the '${this.artboard?.name}' Artboard. Note that you must rename a text run node in the Rive editor to make it queryable at runtime.`,\n      );\n      return;\n    }\n    return textRun;\n  }\n\n  /**\n   * Returns a string from a given text run node name, or undefined if the text run\n   * cannot be queried.\n   *\n   * @param textRunName - Name of the text run node associated with a text object\n   * @returns - String value of the text run node or undefined\n   */\n  public getTextRunValue(textRunName: string): string | undefined {\n    const textRun = this.retrieveTextRun(textRunName);\n    return textRun ? textRun.text : undefined;\n  }\n\n  /**\n   * Sets a text value for a given text run node name if possible\n   *\n   * @param textRunName - Name of the text run node associated with a text object\n   * @param textRunValue - String value to set on the text run node\n   */\n  public setTextRunValue(textRunName: string, textRunValue: string): void {\n    const textRun = this.retrieveTextRun(textRunName);\n    if (textRun) {\n      textRun.text = textRunValue;\n    }\n  }\n\n  // Plays specified animations; if none specified, it unpauses everything.\n  public play(animationNames?: string | string[], autoplay?: true): void {\n    animationNames = mapToStringArray(animationNames);\n\n    // If the file's not loaded, queue up the play\n    if (!this.readyForPlaying) {\n      this.taskQueue.add({\n        action: () => this.play(animationNames, autoplay),\n      });\n      return;\n    }\n    this.animator.play(animationNames);\n    if (this.eventCleanup) {\n      this.eventCleanup();\n    }\n    this.setupRiveListeners();\n    this.startRendering();\n  }\n\n  // Pauses specified animations; if none specified, pauses all.\n  public pause(animationNames?: string | string[]): void {\n    animationNames = mapToStringArray(animationNames);\n\n    // If the file's not loaded, early out, nothing to pause\n    if (!this.readyForPlaying) {\n      this.taskQueue.add({\n        action: () => this.pause(animationNames),\n      });\n      return;\n    }\n    if (this.eventCleanup) {\n      this.eventCleanup();\n    }\n    this.animator.pause(animationNames);\n  }\n\n  public scrub(animationNames?: string | string[], value?: number): void {\n    animationNames = mapToStringArray(animationNames);\n\n    // If the file's not loaded, early out, nothing to pause\n    if (!this.readyForPlaying) {\n      this.taskQueue.add({\n        action: () => this.scrub(animationNames, value),\n      });\n      return;\n    }\n\n    // Scrub the animation time; we draw a single frame here so that if\n    // nothing's currently playing, the scrubbed animation is still rendered/\n    this.animator.scrub(animationNames, value || 0);\n    this.drawFrame();\n  }\n\n  // Stops specified animations; if none specifies, stops them all.\n  public stop(animationNames?: string | string[] | undefined): void {\n    animationNames = mapToStringArray(animationNames);\n    // If the file's not loaded, early out, nothing to pause\n    if (!this.readyForPlaying) {\n      this.taskQueue.add({\n        action: () => this.stop(animationNames),\n      });\n      return;\n    }\n    // If there is no artboard, this.animator will be undefined\n    if (this.animator) {\n      this.animator.stop(animationNames);\n    }\n    if (this.eventCleanup) {\n      this.eventCleanup();\n    }\n  }\n\n  /**\n   * Resets the animation\n   * @param artboard the name of the artboard, or default if none given\n   * @param animations the names of animations for playback\n   * @param stateMachines the names of state machines for playback\n   * @param autoplay whether to autoplay when reset, defaults to false\n   *\n   */\n  public reset(params?: RiveResetParameters): void {\n    // Get the current artboard, animations, state machines, and playback states\n    const artBoardName = params?.artboard;\n    const animationNames = mapToStringArray(params?.animations);\n    const stateMachineNames = mapToStringArray(params?.stateMachines);\n    const autoplay = params?.autoplay ?? false;\n    const autoBind = params?.autoBind ?? false;\n\n    // Stop everything and clean up\n    this.cleanupInstances();\n\n    // Reinitialize an artboard instance with the state\n    this.initArtboard(\n      artBoardName,\n      animationNames,\n      stateMachineNames,\n      autoplay,\n      autoBind,\n    );\n    this.taskQueue.process();\n  }\n\n  // Loads a new Rive file, keeping listeners in place\n  public load(params: RiveLoadParameters): void {\n    this.file = null;\n    // Stop all animations\n    this.stop();\n    // Reinitialize\n    this.init(params);\n  }\n\n  // Sets a new layout\n  public set layout(layout: Layout) {\n    this._layout = layout;\n    // If the maxX or maxY are 0, then set them to the canvas width and height\n    if (!layout.maxX || !layout.maxY) {\n      this.resizeToCanvas();\n    }\n    if (this.loaded && !this.animator.isPlaying) {\n      this.drawFrame();\n    }\n  }\n\n  /**\n   * Returns the current layout. Note that layout should be treated as\n   * immutable. If you want to change the layout, create a new one use the\n   * layout setter\n   */\n  public get layout() {\n    return this._layout;\n  }\n\n  /**\n   * Sets the layout bounds to the current canvas size; this is typically called\n   * when the canvas is resized\n   */\n  public resizeToCanvas() {\n    this._layout = this.layout.copyWith({\n      minX: 0,\n      minY: 0,\n      maxX: this.canvas.width,\n      maxY: this.canvas.height,\n    });\n  }\n\n  /**\n   * Accounts for devicePixelRatio as a multiplier to render the size of the canvas drawing surface.\n   * Uses the size of the backing canvas to set new width/height attributes. Need to re-render\n   * and resize the layout to match the new drawing surface afterwards.\n   * Useful function for consumers to include in a window resize listener.\n   *\n   * This method will set the {@link devicePixelRatioUsed} property.\n   *\n   * Optionally, you can provide a {@link customDevicePixelRatio} to provide a\n   * custom value.\n   */\n  public resizeDrawingSurfaceToCanvas(customDevicePixelRatio?: number) {\n    if (this.canvas instanceof HTMLCanvasElement && !!window) {\n      const { width, height } = this.canvas.getBoundingClientRect();\n      const dpr = customDevicePixelRatio || window.devicePixelRatio || 1;\n      this.devicePixelRatioUsed = dpr;\n      this.canvas.width = dpr * width;\n      this.canvas.height = dpr * height;\n      this.resizeToCanvas();\n      this.drawFrame();\n\n      if (this.layout.fit === Fit.Layout) {\n        const scaleFactor = this._layout.layoutScaleFactor;\n        this.artboard.width = width / scaleFactor;\n        this.artboard.height = height / scaleFactor;\n      }\n    }\n  }\n\n  // Returns the animation source, which may be undefined\n  public get source(): string {\n    return this.src;\n  }\n\n  /**\n   * Returns the name of the active artboard\n   */\n  public get activeArtboard(): string {\n    return this.artboard ? this.artboard.name : \"\";\n  }\n\n  // Returns a list of animation names on the chosen artboard\n  public get animationNames(): string[] {\n    // If the file's not loaded, we got nothing to return\n    if (!this.loaded || !this.artboard) {\n      return [];\n    }\n    const animationNames: string[] = [];\n    for (let i = 0; i < this.artboard.animationCount(); i++) {\n      animationNames.push(this.artboard.animationByIndex(i).name);\n    }\n    return animationNames;\n  }\n\n  /**\n   * Returns a list of state machine names from the current artboard\n   */\n  public get stateMachineNames(): string[] {\n    // If the file's not loaded, we got nothing to return\n    if (!this.loaded || !this.artboard) {\n      return [];\n    }\n    const stateMachineNames: string[] = [];\n    for (let i = 0; i < this.artboard.stateMachineCount(); i++) {\n      stateMachineNames.push(this.artboard.stateMachineByIndex(i).name);\n    }\n    return stateMachineNames;\n  }\n\n  /**\n   * Returns the inputs for the specified instanced state machine, or an empty\n   * list if the name is invalid or the state machine is not instanced\n   * @param name the state machine name\n   * @returns the inputs for the named state machine\n   */\n  public stateMachineInputs(name: string): StateMachineInput[] {\n    // If the file's not loaded, early out, nothing to pause\n    if (!this.loaded) {\n      return;\n    }\n    const stateMachine = this.animator.stateMachines.find(\n      (m) => m.name === name,\n    );\n    return stateMachine?.inputs;\n  }\n\n  // Returns the input with the provided name at the given path\n  private retrieveInputAtPath(\n    name: string,\n    path: string,\n  ): rc.SMIInput | undefined {\n    if (!name) {\n      console.warn(`No input name provided for path '${path}'`);\n      return;\n    }\n    if (!this.artboard) {\n      console.warn(\n        `Tried to access input: '${name}', at path: '${path}', but the Artboard is null`,\n      );\n      return;\n    }\n    const input: rc.SMIInput = this.artboard.inputByPath(name, path);\n\n    if (!input) {\n      console.warn(\n        `Could not access an input with name: '${name}', at path:'${path}'`,\n      );\n      return;\n    }\n    return input;\n  }\n\n  /**\n   * Set the boolean input with the provided name at the given path with value\n   * @param input the state machine input name\n   * @param value the value to set the input to\n   * @param path the path the input is located at an artboard level\n   */\n  public setBooleanStateAtPath(\n    inputName: string,\n    value: boolean,\n    path: string,\n  ) {\n    const input: rc.SMIInput = this.retrieveInputAtPath(inputName, path);\n    if (!input) return;\n\n    if (input.type === StateMachineInputType.Boolean) {\n      input.asBool().value = value;\n    } else {\n      console.warn(\n        `Input with name: '${inputName}', at path:'${path}' is not a boolean`,\n      );\n    }\n  }\n\n  /**\n   * Set the number input with the provided name at the given path with value\n   * @param input the state machine input name\n   * @param value the value to set the input to\n   * @param path the path the input is located at an artboard level\n   */\n  public setNumberStateAtPath(inputName: string, value: number, path: string) {\n    const input: rc.SMIInput = this.retrieveInputAtPath(inputName, path);\n    if (!input) return;\n\n    if (input.type === StateMachineInputType.Number) {\n      input.asNumber().value = value;\n    } else {\n      console.warn(\n        `Input with name: '${inputName}', at path:'${path}' is not a number`,\n      );\n    }\n  }\n\n  /**\n   * Fire the trigger with the provided name at the given path\n   * @param input the state machine input name\n   * @param path the path the input is located at an artboard level\n   */\n  public fireStateAtPath(inputName: string, path: string) {\n    const input: rc.SMIInput = this.retrieveInputAtPath(inputName, path);\n    if (!input) return;\n\n    if (input.type === StateMachineInputType.Trigger) {\n      input.asTrigger().fire();\n    } else {\n      console.warn(\n        `Input with name: '${inputName}', at path:'${path}' is not a trigger`,\n      );\n    }\n  }\n\n  // Returns the TextValueRun object for the provided name at the given path\n  private retrieveTextAtPath(\n    name: string,\n    path: string,\n  ): rc.TextValueRun | undefined {\n    if (!name) {\n      console.warn(`No text name provided for path '${path}'`);\n      return;\n    }\n    if (!path) {\n      console.warn(`No path provided for text '${name}'`);\n      return;\n    }\n    if (!this.artboard) {\n      console.warn(\n        `Tried to access text: '${name}', at path: '${path}', but the Artboard is null`,\n      );\n      return;\n    }\n    const text: rc.TextValueRun = this.artboard.textByPath(name, path);\n    if (!text) {\n      console.warn(\n        `Could not access text with name: '${name}', at path:'${path}'`,\n      );\n      return;\n    }\n    return text;\n  }\n\n  /**\n   * Retrieves the text value for a specified text run at a given path\n   * @param textName The name of the text run\n   * @param path The path to the text run within the artboard\n   * @returns The text value of the text run, or undefined if not found\n   *\n   * @example\n   * // Get the text value for a text run named \"title\" at one nested artboard deep\n   * const titleText = riveInstance.getTextRunValueAtPath(\"title\", \"artboard1\");\n   *\n   * @example\n   * // Get the text value for a text run named \"subtitle\" within a nested group two artboards deep\n   * const subtitleText = riveInstance.getTextRunValueAtPath(\"subtitle\", \"group/nestedGroup\");\n   *\n   * @remarks\n   * If the text run cannot be found at the specified path, a warning will be logged to the console.\n   */\n  public getTextRunValueAtPath(\n    textName: string,\n    path: string,\n  ): string | undefined {\n    const run: rc.TextValueRun = this.retrieveTextAtPath(textName, path);\n    if (!run) {\n      console.warn(\n        `Could not get text with name: '${textName}', at path:'${path}'`,\n      );\n      return;\n    }\n    return run.text;\n  }\n\n  /**\n   * Sets the text value for a specified text run at a given path\n   * @param textName The name of the text run\n   * @param value The new text value to set\n   * @param path The path to the text run within the artboard\n   * @returns void\n   *\n   * @example\n   * // Set the text value for a text run named \"title\" at one nested artboard deep\n   * riveInstance.setTextRunValueAtPath(\"title\", \"New Title\", \"artboard1\");\n   *\n   * @example\n   * // Set the text value for a text run named \"subtitle\" within a nested group two artboards deep\n   * riveInstance.setTextRunValueAtPath(\"subtitle\", \"New Subtitle\", \"group/nestedGroup\");\n   *\n   * @remarks\n   * If the text run cannot be found at the specified path, a warning will be logged to the console.\n   */\n  public setTextRunValueAtPath(textName: string, value: string, path: string) {\n    const run: rc.TextValueRun = this.retrieveTextAtPath(textName, path);\n    if (!run) {\n      console.warn(\n        `Could not set text with name: '${textName}', at path:'${path}'`,\n      );\n      return;\n    }\n    run.text = value;\n  }\n\n  // Returns a list of playing machine names\n  public get playingStateMachineNames(): string[] {\n    // If the file's not loaded, we got nothing to return\n    if (!this.loaded) {\n      return [];\n    }\n    return this.animator.stateMachines\n      .filter((m) => m.playing)\n      .map((m) => m.name);\n  }\n\n  // Returns a list of playing animation names\n  public get playingAnimationNames(): string[] {\n    // If the file's not loaded, we got nothing to return\n    if (!this.loaded) {\n      return [];\n    }\n    return this.animator.animations.filter((a) => a.playing).map((a) => a.name);\n  }\n\n  // Returns a list of paused animation names\n  public get pausedAnimationNames(): string[] {\n    // If the file's not loaded, we got nothing to return\n    if (!this.loaded) {\n      return [];\n    }\n    return this.animator.animations\n      .filter((a) => !a.playing)\n      .map((a) => a.name);\n  }\n\n  /**\n   *  Returns a list of paused machine names\n   * @returns a list of state machine names that are paused\n   */\n  public get pausedStateMachineNames(): string[] {\n    // If the file's not loaded, we got nothing to return\n    if (!this.loaded) {\n      return [];\n    }\n    return this.animator.stateMachines\n      .filter((m) => !m.playing)\n      .map((m) => m.name);\n  }\n\n  /**\n   * @returns true if any animation is playing\n   */\n  public get isPlaying(): boolean {\n    return this.animator.isPlaying;\n  }\n\n  /**\n   * @returns true if all instanced animations are paused\n   */\n  public get isPaused(): boolean {\n    return this.animator.isPaused;\n  }\n\n  /**\n   * @returns true if no animations are playing or paused\n   */\n  public get isStopped(): boolean {\n    return this.animator.isStopped;\n  }\n\n  /**\n   * @returns the bounds of the current artboard, or undefined if the artboard\n   * isn't loaded yet.\n   */\n  public get bounds(): Bounds {\n    return this.artboard ? this.artboard.bounds : undefined;\n  }\n\n  /**\n   * Subscribe to Rive-generated events\n   * @param type the type of event to subscribe to\n   * @param callback callback to fire when the event occurs\n   */\n  public on(type: EventType, callback: EventCallback) {\n    this.eventManager.add({\n      type: type,\n      callback: callback,\n    });\n  }\n\n  /**\n   * Unsubscribes from a Rive-generated event\n   * @param type the type of event to unsubscribe from\n   * @param callback the callback to unsubscribe\n   */\n  public off(type: EventType, callback: EventCallback) {\n    this.eventManager.remove({\n      type: type,\n      callback: callback,\n    });\n  }\n\n  /**\n   * Unsubscribes from a Rive-generated event\n   * @deprecated\n   * @param callback the callback to unsubscribe from\n   */\n  public unsubscribe(type: EventType, callback: EventCallback) {\n    console.warn(\"This function is deprecated: please use `off()` instead.\");\n    this.off(type, callback);\n  }\n\n  /**\n   * Unsubscribes all Rive listeners from an event type, or everything if no type is\n   * given\n   * @param type the type of event to unsubscribe from, or all types if\n   * undefined\n   */\n  public removeAllRiveEventListeners(type?: EventType) {\n    this.eventManager.removeAll(type);\n  }\n\n  /**\n   * Unsubscribes all listeners from an event type, or everything if no type is\n   * given\n   * @deprecated\n   * @param type the type of event to unsubscribe from, or all types if\n   * undefined\n   */\n  public unsubscribeAll(type?: EventType) {\n    console.warn(\n      \"This function is deprecated: please use `removeAllRiveEventListeners()` instead.\",\n    );\n    this.removeAllRiveEventListeners(type);\n  }\n\n  /**\n   * Stops the rendering loop; this is different from pausing in that it doesn't\n   * change the state of any animation. It stops rendering from occurring. This\n   * is designed for situations such as when Rive isn't visible.\n   *\n   * The only way to start rendering again is to call `startRendering`.\n   * Animations that are marked as playing will start from the position that\n   * they would have been at if rendering had not been stopped.\n   */\n  public stopRendering() {\n    if (this.loaded && this.frameRequestId) {\n      if (this.runtime.cancelAnimationFrame) {\n        this.runtime.cancelAnimationFrame(this.frameRequestId);\n      } else {\n        cancelAnimationFrame(this.frameRequestId);\n      }\n      this.frameRequestId = null;\n    }\n  }\n\n  /**\n   * Starts the rendering loop if it has been previously stopped. If the\n   * renderer is already active, then this will have zero effect.\n   */\n  public startRendering() {\n    if (this.loaded && this.artboard && !this.frameRequestId) {\n      if (this.runtime.requestAnimationFrame) {\n        this.frameRequestId = this.runtime.requestAnimationFrame(\n          this._boundDraw,\n        );\n      } else {\n        this.frameRequestId = requestAnimationFrame(this._boundDraw);\n      }\n    }\n  }\n\n  /**\n   * Enables frames-per-second (FPS) reporting for the runtime\n   * If no callback is provided, Rive will append a fixed-position div at the top-right corner of\n   * the page with the FPS reading\n   * @param fpsCallback - Callback from the runtime during the RAF loop that supplies the FPS value\n   */\n  public enableFPSCounter(fpsCallback?: FPSCallback) {\n    this.runtime.enableFPSCounter(fpsCallback);\n  }\n\n  /**\n   * Disables frames-per-second (FPS) reporting for the runtime\n   */\n  public disableFPSCounter() {\n    this.runtime.disableFPSCounter();\n  }\n\n  /**\n   * Returns the contents of a Rive file: the artboards, animations, and state machines\n   */\n  public get contents(): RiveFileContents {\n    if (!this.loaded) {\n      return undefined;\n    }\n    const riveContents: RiveFileContents = {\n      artboards: [],\n    };\n    for (let i = 0; i < this.file.artboardCount(); i++) {\n      const artboard = this.file.artboardByIndex(i);\n      const artboardContents: ArtboardContents = {\n        name: artboard.name,\n        animations: [],\n        stateMachines: [],\n      };\n      for (let j = 0; j < artboard.animationCount(); j++) {\n        const animation = artboard.animationByIndex(j);\n        artboardContents.animations.push(animation.name);\n      }\n      for (let k = 0; k < artboard.stateMachineCount(); k++) {\n        const stateMachine = artboard.stateMachineByIndex(k);\n        const name = stateMachine.name;\n        const instance = new this.runtime.StateMachineInstance(\n          stateMachine,\n          artboard,\n        );\n        const inputContents: StateMachineInputContents[] = [];\n        for (let l = 0; l < instance.inputCount(); l++) {\n          const input = instance.input(l);\n          inputContents.push({ name: input.name, type: input.type });\n        }\n        artboardContents.stateMachines.push({\n          name: name,\n          inputs: inputContents,\n        });\n      }\n      riveContents.artboards.push(artboardContents);\n    }\n    return riveContents;\n  }\n\n  /**\n   * Getter / Setter for the volume of the artboard\n   */\n  public get volume(): number {\n    if (this.artboard && this.artboard.volume !== this._volume) {\n      this._volume = this.artboard.volume;\n    }\n    return this._volume;\n  }\n\n  public set volume(value: number) {\n    this._volume = value;\n    if (this.artboard) {\n      this.artboard.volume = value * audioManager.systemVolume;\n    }\n  }\n\n  /**\n   * The width of the artboard.\n   *\n   * This will return 0 if the artboard is not loaded yet and a custom\n   * width has not been set.\n   *\n   * Do not set this value manually when using {@link resizeDrawingSurfaceToCanvas}\n   * with a {@link Layout.fit} of {@link Fit.Layout}, as the artboard width is\n   * automatically set.\n   */\n  public get artboardWidth(): number {\n    if (this.artboard) {\n      return this.artboard.width;\n    }\n    return this._artboardWidth ?? 0;\n  }\n\n  public set artboardWidth(value: number) {\n    this._artboardWidth = value;\n    if (this.artboard) {\n      this.artboard.width = value;\n    }\n  }\n\n  /**\n   * The height of the artboard.\n   *\n   * This will return 0 if the artboard is not loaded yet and a custom\n   * height has not been set.\n   *\n   * Do not set this value manually when using {@link resizeDrawingSurfaceToCanvas}\n   * with a {@link Layout.fit} of {@link Fit.Layout}, as the artboard height is\n   * automatically set.\n   */\n  public get artboardHeight(): number {\n    if (this.artboard) {\n      return this.artboard.height;\n    }\n    return this._artboardHeight ?? 0;\n  }\n\n  public set artboardHeight(value: number) {\n    this._artboardHeight = value;\n\n    if (this.artboard) {\n      this.artboard.height = value;\n    }\n  }\n\n  /**\n   * Reset the artboard size to its original values.\n   */\n  public resetArtboardSize() {\n    if (this.artboard) {\n      this.artboard.resetArtboardSize();\n      this._artboardWidth = this.artboard.width;\n      this._artboardHeight = this.artboard.height;\n    } else {\n      // If the artboard isn't loaded, we need to reset the custom width and height\n      this._artboardWidth = undefined;\n      this._artboardHeight = undefined;\n    }\n  }\n\n  /**\n   * The device pixel ratio used in rendering and canvas/artboard resizing.\n   *\n   * This value will be overidden by the device pixel ratio used in\n   * {@link resizeDrawingSurfaceToCanvas}. If you use that method, do not set this value.\n   */\n  public get devicePixelRatioUsed(): number {\n    return this._devicePixelRatioUsed;\n  }\n\n  public set devicePixelRatioUsed(value: number) {\n    this._devicePixelRatioUsed = value;\n  }\n\n  /**\n   * Initialize the data context with the view model instance.\n   */\n  public bindViewModelInstance(viewModelInstance: ViewModelInstance | null) {\n    if (this.artboard && !this.destroyed) {\n      if (viewModelInstance && viewModelInstance.runtimeInstance) {\n        viewModelInstance.internalIncrementReferenceCount();\n        this._viewModelInstance?.cleanup();\n        this._viewModelInstance = viewModelInstance;\n        if (this.animator.stateMachines.length > 0) {\n          this.animator.stateMachines.forEach((stateMachine) =>\n            stateMachine.bindViewModelInstance(viewModelInstance),\n          );\n        } else {\n          this.artboard.bindViewModelInstance(\n            viewModelInstance.runtimeInstance,\n          );\n        }\n      }\n    }\n  }\n\n  public get viewModelInstance(): ViewModelInstance | null {\n    return this._viewModelInstance;\n  }\n\n  public viewModelByIndex(index: number): ViewModel | null {\n    const viewModel = this.file.viewModelByIndex(index);\n    if (viewModel !== null) {\n      return new ViewModel(viewModel);\n    }\n    return null;\n  }\n\n  public viewModelByName(name: string): ViewModel | null {\n    const viewModel = this.file.viewModelByName(name);\n    if (viewModel !== null) {\n      return new ViewModel(viewModel);\n    }\n    return null;\n  }\n\n  public enums(): DataEnum[] {\n    if (this._dataEnums === null) {\n      const dataEnums = this.file.enums();\n      this._dataEnums = dataEnums.map((dataEnum) => {\n        return new DataEnum(dataEnum);\n      });\n    }\n    return this._dataEnums;\n  }\n\n  public defaultViewModel(): ViewModel | null {\n    if (this.artboard) {\n      const viewModel = this.file.defaultArtboardViewModel(this.artboard);\n      if (viewModel) {\n        return new ViewModel(viewModel);\n      }\n    }\n    return null;\n  }\n}\n\nexport class ViewModel {\n  private _viewModel: rc.ViewModel;\n\n  constructor(viewModel: rc.ViewModel) {\n    this._viewModel = viewModel;\n  }\n\n  public get instanceCount(): number {\n    return this._viewModel.instanceCount;\n  }\n\n  public get name(): string {\n    return this._viewModel.name;\n  }\n\n  public instanceByIndex(index: number): ViewModelInstance | null {\n    const instance = this._viewModel.instanceByIndex(index);\n    if (instance !== null) {\n      return new ViewModelInstance(instance, null);\n    }\n    return null;\n  }\n\n  public instanceByName(name: string): ViewModelInstance | null {\n    const instance = this._viewModel.instanceByName(name);\n    if (instance !== null) {\n      return new ViewModelInstance(instance, null);\n    }\n    return null;\n  }\n\n  public defaultInstance(): ViewModelInstance | null {\n    const runtimeInstance = this._viewModel.defaultInstance();\n    if (runtimeInstance !== null) {\n      return new ViewModelInstance(runtimeInstance, null);\n    }\n    return null;\n  }\n\n  public instance(): ViewModelInstance | null {\n    const runtimeInstance = this._viewModel.instance();\n    if (runtimeInstance !== null) {\n      return new ViewModelInstance(runtimeInstance, null);\n    }\n    return null;\n  }\n\n  public get properties(): rc.ViewModelProperty[] {\n    return this._viewModel.getProperties();\n  }\n\n  public get instanceNames(): string[] {\n    return this._viewModel.getInstanceNames();\n  }\n}\n\nexport class DataEnum {\n  private _dataEnum: rc.DataEnum;\n\n  constructor(dataEnum: rc.DataEnum) {\n    this._dataEnum = dataEnum;\n  }\n\n  public get name(): string {\n    return this._dataEnum.name;\n  }\n\n  public get values(): string[] {\n    return this._dataEnum.values;\n  }\n}\n\nenum PropertyType {\n  Number = \"number\",\n  String = \"string\",\n  Boolean = \"boolean\",\n  Color = \"color\",\n  Trigger = \"trigger\",\n  Enum = \"enum\",\n}\n\nexport class ViewModelInstance {\n  private _runtimeInstance: rc.ViewModelInstance | null;\n\n  private _parents: ViewModelInstance[] = [];\n\n  private _children: ViewModelInstance[] = [];\n\n  private _viewModelInstances: Map<string, ViewModelInstance> = new Map();\n\n  private _propertiesWithCallbacks: ViewModelInstanceValue[] = [];\n\n  private _referenceCount = 0;\n\n  constructor(\n    runtimeInstance: rc.ViewModelInstance,\n    parent: ViewModelInstance | null,\n  ) {\n    this._runtimeInstance = runtimeInstance;\n    if (parent !== null) {\n      this._parents.push(parent);\n    }\n  }\n\n  public get runtimeInstance(): rc.ViewModelInstance | null {\n    return this._runtimeInstance;\n  }\n\n  public handleCallbacks() {\n    if (this._propertiesWithCallbacks.length !== 0) {\n      this._propertiesWithCallbacks.forEach((property) => {\n        property.handleCallbacks();\n      });\n      this._propertiesWithCallbacks.forEach((property) => {\n        property.clearChanges();\n      });\n    }\n    this._children.forEach((child) => child.handleCallbacks());\n  }\n\n  public addParent(parent: ViewModelInstance) {\n    this._parents.push(parent);\n    if (this._propertiesWithCallbacks.length > 0 || this._children.length > 0) {\n      parent.addToViewModelCallbacks(this);\n    }\n  }\n\n  public removeParent(parent: ViewModelInstance) {\n    const index = this._parents.indexOf(parent);\n    if (index !== -1) {\n      const parent = this._parents[index];\n      parent.removeFromViewModelCallbacks(this);\n      this._parents.splice(index, 1);\n    }\n  }\n\n  /*\n   * method for internal use, it shouldn't be called externally\n   */\n  public addToPropertyCallbacks(property: ViewModelInstanceValue) {\n    if (!this._propertiesWithCallbacks.includes(property)) {\n      this._propertiesWithCallbacks.push(property);\n      if (this._propertiesWithCallbacks.length > 0) {\n        this._parents.forEach((parent) => {\n          parent.addToViewModelCallbacks(this);\n        });\n      }\n    }\n  }\n\n  /*\n   * method for internal use, it shouldn't be called externally\n   */\n  public removeFromPropertyCallbacks(property: ViewModelInstanceValue) {\n    if (this._propertiesWithCallbacks.includes(property)) {\n      this._propertiesWithCallbacks = this._propertiesWithCallbacks.filter(\n        (prop) => prop !== property,\n      );\n      if (\n        this._children.length === 0 &&\n        this._propertiesWithCallbacks.length === 0\n      ) {\n        this._parents.forEach((parent) => {\n          parent.removeFromViewModelCallbacks(this);\n        });\n      }\n    }\n  }\n\n  /*\n   * method for internal use, it shouldn't be called externally\n   */\n  public addToViewModelCallbacks(instance: ViewModelInstance) {\n    if (!this._children.includes(instance)) {\n      this._children.push(instance);\n      this._parents.forEach((parent) => {\n        parent.addToViewModelCallbacks(this);\n      });\n    }\n  }\n\n  /*\n   * method for internal use, it shouldn't be called externally\n   */\n  public removeFromViewModelCallbacks(instance: ViewModelInstance) {\n    if (this._children.includes(instance)) {\n      this._children = this._children.filter((child) => child !== instance);\n      if (\n        this._children.length === 0 &&\n        this._propertiesWithCallbacks.length === 0\n      ) {\n        this._parents.forEach((parent) => {\n          parent.removeFromViewModelCallbacks(this);\n        });\n      }\n    }\n  }\n\n  private clearCallbacks() {\n    this._propertiesWithCallbacks.forEach((property) => {\n      property.clearCallbacks();\n    });\n  }\n\n  private propertyFromPath(\n    path: string,\n    type: PropertyType,\n  ): ViewModelInstanceValue | null {\n    const pathSegments = path.split(\"/\");\n    return this.propertyFromPathSegments(pathSegments, 0, type);\n  }\n\n  private viewModelFromPathSegments(\n    pathSegments: string[],\n    index: number,\n  ): ViewModelInstance | null {\n    const viewModelInstance = this.internalViewModelInstance(\n      pathSegments[index],\n    );\n    if (viewModelInstance !== null) {\n      if (index == pathSegments.length - 1) {\n        return viewModelInstance;\n      } else {\n        return viewModelInstance.viewModelFromPathSegments(\n          pathSegments,\n          index++,\n        );\n      }\n    }\n    return null;\n  }\n\n  private propertyFromPathSegments(\n    pathSegments: string[],\n    index: number,\n    type: PropertyType,\n  ): ViewModelInstanceValue | null {\n    if (index < pathSegments.length - 1) {\n      const viewModelInstance = this.internalViewModelInstance(\n        pathSegments[index],\n      );\n      if (viewModelInstance !== null) {\n        return viewModelInstance.propertyFromPathSegments(\n          pathSegments,\n          index + 1,\n          type,\n        );\n      } else {\n        return null;\n      }\n    }\n    let instance: rc.ViewModelInstanceValue | null = null;\n    switch (type) {\n      case PropertyType.Number:\n        instance = this._runtimeInstance?.number(pathSegments[index]) ?? null;\n        if (instance !== null) {\n          return new ViewModelInstanceNumber(\n            instance as rc.ViewModelInstanceNumber,\n            this,\n          );\n        }\n        break;\n      case PropertyType.String:\n        instance = this._runtimeInstance?.string(pathSegments[index]) ?? null;\n        if (instance !== null) {\n          return new ViewModelInstanceString(\n            instance as rc.ViewModelInstanceString,\n            this,\n          );\n        }\n        break;\n      case PropertyType.Boolean:\n        instance = this._runtimeInstance?.boolean(pathSegments[index]) ?? null;\n        if (instance !== null) {\n          return new ViewModelInstanceBoolean(\n            instance as rc.ViewModelInstanceBoolean,\n            this,\n          );\n        }\n        break;\n      case PropertyType.Color:\n        instance = this._runtimeInstance?.color(pathSegments[index]) ?? null;\n        if (instance !== null) {\n          return new ViewModelInstanceColor(\n            instance as rc.ViewModelInstanceColor,\n            this,\n          );\n        }\n        break;\n      case PropertyType.Trigger:\n        instance = this._runtimeInstance?.trigger(pathSegments[index]) ?? null;\n        if (instance !== null) {\n          return new ViewModelInstanceTrigger(\n            instance as rc.ViewModelInstanceTrigger,\n            this,\n          );\n        }\n        break;\n      case PropertyType.Enum:\n        instance = this._runtimeInstance?.enum(pathSegments[index]) ?? null;\n        if (instance !== null) {\n          return new ViewModelInstanceEnum(\n            instance as rc.ViewModelInstanceEnum,\n            this,\n          );\n        }\n        break;\n    }\n    return null;\n  }\n\n  private internalViewModelInstance(name: string): ViewModelInstance | null {\n    if (this._viewModelInstances.has(name)) {\n      return this._viewModelInstances.get(name)!;\n    }\n    const viewModelRuntimeInstance = this._runtimeInstance?.viewModel(name);\n    if (viewModelRuntimeInstance !== null) {\n      const viewModelInstance = new ViewModelInstance(\n        viewModelRuntimeInstance!,\n        this,\n      );\n      viewModelInstance.internalIncrementReferenceCount();\n      this._viewModelInstances.set(name, viewModelInstance);\n      return viewModelInstance;\n    }\n    return null;\n  }\n\n  /**\n   * method to access a property instance of type number belonging\n   * to the view model instance or to a nested view model instance\n   * @param path - path to the number property\n   */\n  public number(path: string): ViewModelInstanceNumber | null {\n    const viewmodelInstanceValue = this.propertyFromPath(\n      path,\n      PropertyType.Number,\n    );\n    return viewmodelInstanceValue as ViewModelInstanceNumber;\n  }\n\n  /**\n   * method to access a property instance of type string belonging\n   * to the view model instance or to a nested view model instance\n   * @param path - path to the string property\n   */\n  public string(path: string): ViewModelInstanceString | null {\n    const viewmodelInstanceValue = this.propertyFromPath(\n      path,\n      PropertyType.String,\n    );\n    return viewmodelInstanceValue as ViewModelInstanceString | null;\n  }\n\n  /**\n   * method to access a property instance of type boolean belonging\n   * to the view model instance or to a nested view model instance\n   * @param path - path to the boolean property\n   */\n  public boolean(path: string): ViewModelInstanceBoolean | null {\n    const viewmodelInstanceValue = this.propertyFromPath(\n      path,\n      PropertyType.Boolean,\n    );\n    return viewmodelInstanceValue as ViewModelInstanceBoolean | null;\n  }\n\n  /**\n   * method to access a property instance of type color belonging\n   * to the view model instance or to a nested view model instance\n   * @param path - path to the ttrigger property\n   */\n  public color(path: string): ViewModelInstanceColor | null {\n    const viewmodelInstanceValue = this.propertyFromPath(\n      path,\n      PropertyType.Color,\n    );\n    return viewmodelInstanceValue as ViewModelInstanceColor | null;\n  }\n\n  /**\n   * method to access a property instance of type trigger belonging\n   * to the view model instance or to a nested view model instance\n   * @param path - path to the trigger property\n   */\n  public trigger(path: string): ViewModelInstanceTrigger | null {\n    const viewmodelInstanceValue = this.propertyFromPath(\n      path,\n      PropertyType.Trigger,\n    );\n    return viewmodelInstanceValue as ViewModelInstanceTrigger | null;\n  }\n\n  /**\n   * method to access a property instance of type enum belonging\n   * to the view model instance or to a nested view model instance\n   * @param path - path to the enum property\n   */\n  public enum(path: string): ViewModelInstanceEnum | null {\n    const viewmodelInstanceValue = this.propertyFromPath(\n      path,\n      PropertyType.Enum,\n    );\n    return viewmodelInstanceValue as ViewModelInstanceEnum | null;\n  }\n\n  /**\n   * method to access a view model property instance belonging\n   * to the view model instance or to a nested view model instance\n   * @param path - path to the view model property\n   */\n  public viewModel(path: string): ViewModelInstance | null {\n    const pathSegments = path.split(\"/\");\n    const parentViewModelInstance =\n      pathSegments.length > 1\n        ? this.viewModelFromPathSegments(\n            pathSegments.slice(0, pathSegments.length - 1),\n            0,\n          )\n        : this;\n    if (parentViewModelInstance != null) {\n      return parentViewModelInstance.internalViewModelInstance(\n        pathSegments[pathSegments.length - 1],\n      );\n    }\n    return null;\n  }\n\n  public internalReplaceViewModel(\n    name: string,\n    value: ViewModelInstance,\n  ): boolean {\n    if (value.runtimeInstance !== null) {\n      const result =\n        this._runtimeInstance?.replaceViewModel(name, value.runtimeInstance!) ||\n        false;\n      if (result) {\n        value.internalIncrementReferenceCount();\n        const oldInstance = this.internalViewModelInstance(name);\n        if (oldInstance !== null) {\n          oldInstance.removeParent(this);\n          if (this._children.includes(oldInstance)) {\n            this._children = this._children.filter(\n              (child) => child !== oldInstance,\n            );\n          }\n          oldInstance.cleanup();\n        }\n        this._viewModelInstances.set(name, value);\n        value.addParent(this);\n      }\n      return result;\n    }\n    return false;\n  }\n\n  /**\n   * method to replace a view model property with another view model value\n   * @param path - path to the view model property\n   * @param value - view model that will replace the original\n   */\n  public replaceViewModel(path: string, value: ViewModelInstance): boolean {\n    const pathSegments = path.split(\"/\");\n    const viewModelInstance =\n      pathSegments.length > 1\n        ? this.viewModelFromPathSegments(\n            pathSegments.slice(0, pathSegments.length - 1),\n            0,\n          )\n        : this;\n    return (\n      viewModelInstance?.internalReplaceViewModel(\n        pathSegments[pathSegments.length - 1],\n        value,\n      ) ?? false\n    );\n  }\n\n  /*\n   * method to add one to the reference counter of the instance.\n   * Use if the file owning the reference is destroyed but the instance needs to stay around\n   */\n  public incrementReferenceCount() {\n    this._referenceCount++;\n    this._runtimeInstance?.incrementReferenceCount();\n  }\n\n  /*\n   * method to subtract one to the reference counter of the instance.\n   * Use if incrementReferenceCount has been called\n   */\n  public decrementReferenceCount() {\n    this._referenceCount--;\n    this._runtimeInstance?.decrementReferenceCount();\n  }\n\n  public get properties(): rc.ViewModelProperty[] {\n    return (\n      this._runtimeInstance?.getProperties().map((prop) => ({ ...prop })) || []\n    );\n  }\n\n  public internalIncrementReferenceCount() {\n    this._referenceCount++;\n  }\n\n  public cleanup() {\n    this._referenceCount--;\n    if (this._referenceCount <= 0) {\n      this._runtimeInstance = null;\n      this.clearCallbacks();\n      this._propertiesWithCallbacks = [];\n      this._viewModelInstances.forEach((value) => {\n        value.cleanup();\n      });\n      this._viewModelInstances.clear();\n      const children = [...this._children];\n      this._children.length = 0;\n      const parents = [...this._parents];\n      this._parents.length = 0;\n      children.forEach((child) => {\n        child.removeParent(this);\n      });\n      parents.forEach((parent) => {\n        parent.removeFromViewModelCallbacks(this);\n      });\n    }\n  }\n}\n\nexport class ViewModelInstanceValue {\n  protected _parentViewModel: ViewModelInstance;\n  protected callbacks: EventCallback[] = [];\n  protected _viewModelInstanceValue: rc.ViewModelInstanceValue;\n  constructor(instance: rc.ViewModelInstanceValue, parent: ViewModelInstance) {\n    this._viewModelInstanceValue = instance;\n    this._parentViewModel = parent;\n  }\n\n  public on(callback: EventCallback) {\n    // Since we don't clean the changed flag for properties that don't have listeners,\n    // we clean it the first time we add a listener to it\n    if (this.callbacks.length === 0) {\n      this._viewModelInstanceValue.clearChanges();\n    }\n    if (!this.callbacks.includes(callback)) {\n      this.callbacks.push(callback);\n      this._parentViewModel.addToPropertyCallbacks(this);\n    }\n  }\n  public off(callback?: EventCallback) {\n    if (!callback) {\n      this.callbacks.length = 0;\n    } else {\n      this.callbacks = this.callbacks.filter((cb) => cb !== callback);\n    }\n    if (this.callbacks.length === 0) {\n      this._parentViewModel.removeFromPropertyCallbacks(this);\n    }\n  }\n  public internalHandleCallback(callback: Function) {}\n\n  public handleCallbacks() {\n    if (this._viewModelInstanceValue.hasChanged) {\n      this.callbacks.forEach((callback) => {\n        this.internalHandleCallback(callback);\n      });\n    }\n  }\n\n  public clearChanges() {\n    this._viewModelInstanceValue.clearChanges();\n  }\n\n  public clearCallbacks() {\n    this.callbacks.length = 0;\n  }\n\n  public get name() {\n    return this._viewModelInstanceValue.name;\n  }\n}\n\nexport class ViewModelInstanceString extends ViewModelInstanceValue {\n  constructor(instance: rc.ViewModelInstanceString, parent: ViewModelInstance) {\n    super(instance, parent);\n  }\n\n  public get value(): string {\n    return (this._viewModelInstanceValue as rc.ViewModelInstanceString).value;\n  }\n\n  public set value(val: string) {\n    (this._viewModelInstanceValue as rc.ViewModelInstanceString).value = val;\n  }\n  public internalHandleCallback(callback: Function) {\n    callback(this.value);\n  }\n}\n\nexport class ViewModelInstanceNumber extends ViewModelInstanceValue {\n  constructor(instance: rc.ViewModelInstanceNumber, parent: ViewModelInstance) {\n    super(instance, parent);\n  }\n\n  public get value(): number {\n    return (this._viewModelInstanceValue as rc.ViewModelInstanceNumber).value;\n  }\n\n  public set value(val: number) {\n    (this._viewModelInstanceValue as rc.ViewModelInstanceNumber).value = val;\n  }\n  public internalHandleCallback(callback: Function) {\n    callback(this.value);\n  }\n}\n\nexport class ViewModelInstanceBoolean extends ViewModelInstanceValue {\n  constructor(\n    instance: rc.ViewModelInstanceBoolean,\n    parent: ViewModelInstance,\n  ) {\n    super(instance, parent);\n  }\n\n  public get value(): boolean {\n    return (this._viewModelInstanceValue as rc.ViewModelInstanceBoolean).value;\n  }\n\n  public set value(val: boolean) {\n    (this._viewModelInstanceValue as rc.ViewModelInstanceBoolean).value = val;\n  }\n  public internalHandleCallback(callback: Function) {\n    callback(this.value);\n  }\n}\n\nexport class ViewModelInstanceTrigger extends ViewModelInstanceValue {\n  constructor(\n    instance: rc.ViewModelInstanceTrigger,\n    parent: ViewModelInstance,\n  ) {\n    super(instance, parent);\n  }\n\n  public trigger(): void {\n    return (\n      this._viewModelInstanceValue as rc.ViewModelInstanceTrigger\n    ).trigger();\n  }\n\n  public internalHandleCallback(callback: Function) {\n    callback();\n  }\n}\n\nexport class ViewModelInstanceEnum extends ViewModelInstanceValue {\n  constructor(instance: rc.ViewModelInstanceEnum, parent: ViewModelInstance) {\n    super(instance, parent);\n  }\n\n  public get value(): string {\n    return (this._viewModelInstanceValue as rc.ViewModelInstanceEnum).value;\n  }\n\n  public set value(val: string) {\n    (this._viewModelInstanceValue as rc.ViewModelInstanceEnum).value = val;\n  }\n\n  public set valueIndex(val: number) {\n    (this._viewModelInstanceValue as rc.ViewModelInstanceEnum).valueIndex = val;\n  }\n\n  public get valueIndex(): number {\n    return (this._viewModelInstanceValue as rc.ViewModelInstanceEnum)\n      .valueIndex;\n  }\n\n  public get values(): string[] {\n    return (this._viewModelInstanceValue as rc.ViewModelInstanceEnum).values;\n  }\n\n  public internalHandleCallback(callback: Function) {\n    callback(this.value);\n  }\n}\n\nexport class ViewModelInstanceColor extends ViewModelInstanceValue {\n  constructor(instance: rc.ViewModelInstanceColor, parent: ViewModelInstance) {\n    super(instance, parent);\n  }\n\n  public get value(): number {\n    return (this._viewModelInstanceValue as rc.ViewModelInstanceColor).value;\n  }\n\n  public set value(val: number) {\n    (this._viewModelInstanceValue as rc.ViewModelInstanceColor).value = val;\n  }\n\n  public rgb(r: number, g: number, b: number) {\n    (this._viewModelInstanceValue as rc.ViewModelInstanceColor).rgb(r, g, b);\n  }\n\n  public rgba(r: number, g: number, b: number, a: number) {\n    (this._viewModelInstanceValue as rc.ViewModelInstanceColor).argb(\n      a,\n      r,\n      g,\n      b,\n    );\n  }\n\n  public argb(a: number, r: number, g: number, b: number) {\n    (this._viewModelInstanceValue as rc.ViewModelInstanceColor).argb(\n      a,\n      r,\n      g,\n      b,\n    );\n  }\n\n  // Value 0 to 255\n  public alpha(a: number) {\n    (this._viewModelInstanceValue as rc.ViewModelInstanceColor).alpha(a);\n  }\n\n  // Value 0 to 1\n  public opacity(o: number) {\n    (this._viewModelInstanceValue as rc.ViewModelInstanceColor).alpha(\n      Math.round(Math.max(0, Math.min(1, o)) * 255),\n    );\n  }\n  public internalHandleCallback(callback: Function) {\n    callback(this.value);\n  }\n}\n\n/**\n * Contents of a state machine input\n */\ninterface StateMachineInputContents {\n  name: string;\n  type: StateMachineInputType;\n  initialValue?: boolean | number;\n}\n\n/**\n * Contents of a state machine\n */\ninterface StateMachineContents {\n  name: string;\n  inputs: StateMachineInputContents[];\n}\n\n/**\n * Contents of an artboard\n */\ninterface ArtboardContents {\n  animations: string[];\n  stateMachines: StateMachineContents[];\n  name: string;\n}\n\n/**\n * contents of a Rive file\n */\ninterface RiveFileContents {\n  artboards?: ArtboardContents[];\n}\n\n// Loads Rive data from a URI via fetch.\nconst loadRiveFile = async (src: string): Promise<ArrayBuffer> => {\n  const req = new Request(src);\n  const res = await fetch(req);\n  const buffer = await res.arrayBuffer();\n  return buffer;\n};\n\n// #endregion\n\n// #region utility functions\n\n/*\n * Utility function to ensure an object is a string array\n */\nconst mapToStringArray = (obj?: string[] | string | undefined): string[] => {\n  if (typeof obj === \"string\") {\n    return [obj];\n  } else if (obj instanceof Array) {\n    return obj;\n  }\n  // If obj is undefined, return empty array\n  return [];\n};\n\n// #endregion\n\n// #region testing utilities\n\n// Exports to only be used for tests\nexport const Testing = {\n  EventManager: EventManager,\n  TaskQueueManager: TaskQueueManager,\n};\n\n// #endregion\n\n// #region asset loaders\n\n/**\n * Decodes bytes into an audio asset.\n *\n * Be sure to call `.unref()` on the audio once it is no longer needed. This\n * allows the engine to clean it up when it is not used by any more animations.\n */\nexport const decodeAudio = (bytes: Uint8Array): Promise<rc.Audio> => {\n  return new Promise<rc.Audio>((resolve) =>\n    RuntimeLoader.getInstance((rive: rc.RiveCanvas): void => {\n      rive.decodeAudio(bytes, resolve);\n    }),\n  );\n};\n\n/**\n * Decodes bytes into an image.\n *\n * Be sure to call `.unref()` on the image once it is no longer needed. This\n * allows the engine to clean it up when it is not used by any more animations.\n */\nexport const decodeImage = (bytes: Uint8Array): Promise<rc.Image> => {\n  return new Promise<rc.Image>((resolve) =>\n    RuntimeLoader.getInstance((rive: rc.RiveCanvas): void => {\n      rive.decodeImage(bytes, resolve);\n    }),\n  );\n};\n\n/**\n * Decodes bytes into a font.\n *\n * Be sure to call `.unref()` on the font once it is no longer needed. This\n * allows the engine to clean it up when it is not used by any more animations.\n */\nexport const decodeFont = (bytes: Uint8Array): Promise<rc.Font> => {\n  return new Promise<rc.Font>((resolve) =>\n    RuntimeLoader.getInstance((rive: rc.RiveCanvas): void => {\n      rive.decodeFont(bytes, resolve);\n    }),\n  );\n};\n\n// #endregion\n"], "names": [], "sourceRoot": ""}