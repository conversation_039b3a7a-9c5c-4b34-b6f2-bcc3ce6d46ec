/**
 * Services Component Styles
 *
 * This file contains styles for the services page:
 * - Services page layout
 * - Service item cards
 * - Service icons and pricing
 * - Important note styling
 * - Services buttons
 */

/* ======================================================
SERVICES PAGE STYLES
====================================================== */

/* SERVICES PAGE BUTTON - shows the page when clicked */
.services:active + .services-page {
  pointer-events: auto;
  opacity: 1;
}

/* SERVICES PAGE SIZE */
.card.services-active {
  top: -20px; /* Adjust the top position */
  height: 850px;  /* Increased height to fit content */
  max-width: 1200px;   /* Adjust the max-width */
  transition: all var(--transition-slow) cubic-bezier(0.25, 0.1, 0.25, 1);
}

.services-page {
  position: absolute;
  inset: 6px;
  background-color: rgb(230, 245, 245);
  opacity: 0;
  z-index: 30;
  pointer-events: none;
  transition: opacity 0.4s ease, transform 0.4s ease;
  mix-blend-mode: normal;
  padding: 30px;
  border-radius: 8px;
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.05);
  overflow-y: auto;
  will-change: opacity, transform;
}

/* Services header styling */
.services-header {
  max-width: 1000px;
  margin: 0 auto 20px;
  text-align: center;
}

/* Services title styling */
.services-title {
  position: relative;
  display: inline-block;
  margin-bottom: 25px;
  padding-bottom: 12px;
  color: var(--color-primary-darker);
  font-family: var(--font-tertiary);
  font-weight: 800;
  font-size: 2rem;
  letter-spacing: 0.5px;
  text-transform: none;
}

.services-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--color-primary-dark), var(--color-primary-light));
  border-radius: 4px;
  transition: width var(--transition-medium);
}

.services-title:hover::after {
  width: 120px;
}

/* Services container styling */
.services-container {
  display: flex;
  flex-direction: row; /* Horizontal layout */
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

/* Service item styling */
.services-item {
  flex: 1 1 calc(33.333% - 20px); /* Three items per row with gap */
  min-width: 250px;
  max-width: 350px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  margin-bottom: 20px;
  will-change: transform, box-shadow;
}

.services-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* Service item header */
.service-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.service-icon {
  height: 2.2rem;
  width: 2.2rem;
  color: var(--color-primary);
  flex-shrink: 0;
  transition: transform 0.3s ease, color 0.3s ease;
}

.services-item:hover .service-icon {
  color: var(--color-primary-dark);
}

.service-header-content h2 {
  font-family: var(--font-primary);
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--color-text-dark);
  margin: 0;
  padding: 0;
  line-height: 1.3;
  transition: color 0.3s ease;
}


.services-item:hover h2 {
  color: var(--color-primary-dark);
}

/* Service header content */
.service-header-content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  justify-content: space-between;
  gap: 8px;
  flex-basis: 0;
}

/* Service price container */
.service-price-container {
  display: flex;
  align-items: center;
  margin: 0;
  flex-shrink: 0;
}

/* Service description */
.service-description p {
  margin: 0;
  line-height: 1.6;
  font-size: 0.95rem;
  color: var(--color-text-dark);
  font-family: var(--font-secondary);
  border-top: 1px solid rgba(79, 209, 197, 0.2);
  padding-top: 12px;
}

/* Service icon container */
.service-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 60px;
  height: 60px;
  background-color: rgba(79, 209, 197, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
  flex-shrink: 0;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
}

.service-price {
  font-family: var(--font-primary);
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--color-primary-dark);
}

/* Important note styling */
.important-note {
  max-width: 800px;
  margin: 30px auto 0;
  padding: 15px 20px;
  background-color: rgba(255, 255, 255, 0.8);
  border-left: 4px solid var(--color-primary);
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.note-title {
  font-weight: bold;
  margin-bottom: 10px;
  margin-top: 0;
  font-size: 1.1rem;
  color: var(--color-primary-dark);
  text-align: left;
  display: flex;
  align-items: center;
  font-family: var(--font-tertiary);
  letter-spacing: 0.5px;
}

.note-title::before {
  content: '⚠️';
  margin-right: 10px;
  font-size: 1.2rem;
}

.note-text {
  margin-top: 0;
  font-size: 0.95rem;
  line-height: 1.6;
  color: var(--color-text-medium);
  font-weight: 400;
  font-family: var(--font-secondary);
  letter-spacing: 0.2px;
  transition: color 0.3s ease;
  text-align: left;
  margin-bottom: 0;
  padding-left: 30px; /* Align with text after icon */
}

/* Back to card button */
.back-to-card {
  display: inline-block;
  margin-top: 20px;
  padding: 10px 20px;
  background-color: var(--color-primary);
  color: white;
  font-family: var(--font-secondary);
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color var(--transition-medium), transform var(--transition-medium);
}

.back-to-card:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-2px);
}

.back-to-card:active {
  transform: translateY(0);
}

/* Services footer and buttons container */
.services-footer {
  position: relative;
  margin-top: 40px;
  padding-bottom: 40px;
  text-align: center;
  display: flex;
  width: 100%;
}

.services-buttons-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 20px;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Base styles for all service buttons */
.services-book-button,
.services-back-button,
.services-area-button {
  flex: 1;
  padding: 16px 20px;
  color: white;
  text-align: center;
  font-family: var(--font-tertiary);
  font-weight: 700;
  font-size: 16px;
  letter-spacing: 1px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.25s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 5;
  min-height: 56px;
  max-width: 300px;
}

/* Book button specific styles */
.services-book-button {
  background: linear-gradient(to right, var(--color-primary-dark), var(--color-primary));
  text-transform: uppercase;
}

.services-book-button:hover {
  background: linear-gradient(to right, var(--color-primary-darker), var(--color-primary-dark));
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.services-book-button:active {
  background: linear-gradient(to right, var(--color-primary), var(--color-primary-lighter));
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Back button specific styles */
.services-back-button {
  background: linear-gradient(to right, var(--color-grey-dark), var(--color-grey));
  display: flex;
  align-items: center;
  justify-content: center;
}

.services-back-button::before {
  content: '←';
  margin-right: 8px;
  font-size: 1.2rem;
  transition: transform 0.2s ease;
}

.services-back-button:hover {
  background: linear-gradient(to right, var(--color-grey-dark), var(--color-grey));
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.services-back-button:hover::before {
  transform: translateX(-3px);
}

.services-back-button:active {
  background: linear-gradient(to right, var(--color-grey), var(--color-grey-light));
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Area button specific styles */
.services-area-button {
  background: linear-gradient(to right, #2196F3, #1976D2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.services-area-button:hover {
  background: linear-gradient(to right, #1976D2, #1565C0);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.services-area-button:active {
  background: linear-gradient(to right, #1565C0, #0D47A1);
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Shine effect for services book button */
.services-book-button::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%);
  transform: rotate(45deg) translateY(-50%) translateX(-50%);
  opacity: 0;
  transition: opacity var(--transition-medium);
}

.services-book-button:hover::after {
  opacity: 1;
  animation: shine 1.5s infinite;
}

/* Responsive styles moved to responsive.css */