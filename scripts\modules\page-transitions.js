/**
 * Page Transitions Module
 *
 * This module handles transitions between different pages/views:
 * - About Me page transitions
 * - Services page transitions
 * - Main card view transitions
 * - Mobile-specific transition handling
 */

import { isMobileDevice } from './utils.js';
import { delegateEvent, delegateEvents } from './event-delegation.js';

/**
 * Initializes page transitions for the card
 * @param {HTMLElement} card - The main card element
 * @returns {Object} - Public methods for controlling page transitions
 */
export function initPageTransitions(card) {
  // Page elements
  const aboutMePage = document.querySelector('.about-me-page');
  const servicesPage = document.querySelector('.services-page');
  const cardHeader = document.querySelector('.card-header');
  const contactInfo = document.querySelector('.contact-info');
  const bookButton = document.querySelector('.book-button');

  // Event removal functions
  let removeCardClickListeners;
  let removeServicesPageClickListeners;
  let removeWindowResizeListener;
  let removeBookButtonClickListener;

  // Add default class to card initially
  card.classList.add('card-default');

  // Initialize event listeners
  initEventListeners();

  /**
   * Sets up all event listeners for page transitions using event delegation
   */
  function initEventListeners() {
    // Use event delegation for card buttons
    removeCardClickListeners = delegateEvents(card, {
      'click': {
        '.about-me': handleAboutMeClick,
        '.services': handleServicesClick,
        '.back-button': hideAllPages
      }
    });

    // Use event delegation for services page buttons
    if (servicesPage) {
      removeServicesPageClickListeners = delegateEvents(servicesPage, {
        'click': {
          '.services-book-button': handleServicesBookClick,
          '.services-back-button': hideAllPages
        }
      });
    }

    // Add click handler for the main book button
    if (bookButton) {
      removeBookButtonClickListener = () => {
        bookButton.removeEventListener('click', handleBookButtonClick);
      };
      bookButton.addEventListener('click', handleBookButtonClick);
    }

    // Window resize handler for mobile orientation changes
    removeWindowResizeListener = () => {
      window.removeEventListener('resize', handleResize);
    };
    window.addEventListener('resize', handleResize);
  }

  /**
   * Handles About Me button click
   */
  function handleAboutMeClick() {
    // If about me page is already visible, hide it
    if (aboutMePage.style.opacity === '1') {
      hideAllPages();
    } else {
      // Hide any other open pages first
      hideAllPages();

      // Show about me page
      aboutMePage.style.opacity = '1';
      aboutMePage.style.animation = 'about-me-fadeIn 0.3s ease forwards';
      card.classList.add('about-me-active');
      card.classList.remove('card-default');

      // Hide main card elements
      cardHeader.classList.add('inactive');
      contactInfo.classList.add('inactive');
      bookButton.classList.add('inactive');

      // For mobile devices, disable card tilt effect when viewing About Me page
      if (isMobileDevice()) {
        card.style.transform = 'rotateX(0deg) rotateY(0deg)';
        // Prevent scrolling on the body when the card is in fullscreen
        document.body.style.overflow = 'hidden';
      }
    }
  }

  /**
   * Handles Services button click
   */
  function handleServicesClick() {
    // If services page is already visible, hide it
    if (servicesPage.style.opacity === '1') {
      hideAllPages();
    } else {
      // Hide any other open pages first
      hideAllPages();

      // Show services page with improved animation
      servicesPage.style.opacity = '1';
      servicesPage.style.animation = 'services-fadeIn 0.5s cubic-bezier(0.25, 0.1, 0.25, 1.4) forwards';
      card.classList.add('services-active');
      card.classList.remove('card-default');

      // Hide main card elements
      cardHeader.classList.add('inactive');
      contactInfo.classList.add('inactive');
      bookButton.classList.add('inactive');

      // For mobile devices, disable card tilt effect when viewing Services page
      if (isMobileDevice()) {
        card.style.transform = 'rotateX(0deg) rotateY(0deg)';
        // Prevent scrolling on the body when the card is in fullscreen
        document.body.style.overflow = 'hidden';

        // Ensure the services page is scrollable
        servicesPage.style.overflowY = 'auto';
        servicesPage.style.height = '100%';
      }
    }
  }

  /**
   * Handles main Book button click
   */
  function handleBookButtonClick() {
    // Redirect to the booking form page
    window.location.href = 'booking-form.html';
  }

  /**
   * Handles Services Book button click
   */
  function handleServicesBookClick() {
    // First hide all pages
    hideAllPages();

    // Redirect to the booking form page
    window.location.href = 'booking-form.html';

    // For mobile, ensure we're back to normal card size
    if (isMobileDevice()) {
      card.classList.remove('services-active');
      document.body.style.overflow = '';
    }
  }

  /**
   * Handles window resize events
   */
  function handleResize() {
    if (isMobileDevice()) {
      if (card.classList.contains('about-me-active') || card.classList.contains('services-active')) {
        // Reset transform to prevent tilt effect on mobile
        card.style.transform = 'rotateX(0deg) rotateY(0deg)';
      }
    }
  }

  /**
   * Hides all pages and resets the card to its default state
   */
  function hideAllPages() {
    // Hide about me page
    if (aboutMePage) {
      aboutMePage.style.opacity = '0';
      aboutMePage.style.animation = 'about-me-fadeOut 0.3s ease forwards';
    }
    card.classList.remove('about-me-active');

    // Hide services page with improved animation
    if (servicesPage) {
      servicesPage.style.opacity = '0';
      servicesPage.style.animation = 'services-fadeOut 0.4s cubic-bezier(0.25, 0.1, 0.25, 1) forwards';
    }
    card.classList.remove('services-active');

    // Hide service area page if it exists
    const serviceAreaPage = document.querySelector('.service-area-page');
    if (serviceAreaPage) {
      serviceAreaPage.style.opacity = '0';
      serviceAreaPage.style.display = 'none';
      // Clean up map if it exists
      if (typeof window.cleanupMap === 'function') {
        window.cleanupMap();
      }
    }

    // Add default class back to card
    card.classList.add('card-default');

    // Show main card elements
    if (cardHeader) cardHeader.classList.remove('inactive');
    if (contactInfo) contactInfo.classList.remove('inactive');
    if (bookButton) bookButton.classList.remove('inactive');

    // Restore body scrolling
    document.body.style.overflow = '';

    // For mobile devices, restore original card size and position
    if (isMobileDevice()) {
      // Reset any inline styles that might have been applied
      card.style.removeProperty('height');
      card.style.removeProperty('max-width');
      card.style.removeProperty('width');
      card.style.removeProperty('top');
      card.style.removeProperty('overflow-y');

      // Allow the CSS to take over with the original styling
      setTimeout(() => {
        // Force a reflow to ensure transitions work properly
        void card.offsetHeight;
      }, 50);
    }
  }

  // Return public methods
  return {
    hideAllPages,
    showAboutMe: handleAboutMeClick,
    showServices: handleServicesClick,
    // Clean up method to remove event listeners
    cleanup: () => {
      if (removeCardClickListeners) {
        removeCardClickListeners();
      }
      if (removeServicesPageClickListeners) {
        removeServicesPageClickListeners();
      }
      if (removeWindowResizeListener) {
        removeWindowResizeListener();
      }
      if (removeBookButtonClickListener) {
        removeBookButtonClickListener();
      }
    }
  };
}